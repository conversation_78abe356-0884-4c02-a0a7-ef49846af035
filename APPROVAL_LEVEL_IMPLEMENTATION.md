# 📋 Purchase Request Approval Level Display Implementation

## 🎯 **Objective**
Show the current approval level (e.g., "Procurement Manager", "Cost Center Head") under the "Pending Approval" status tag in the purchase request listing page.

## 🔧 **Changes Made**

### **Backend Changes**

#### **1. Enhanced Purchase Request Controller** (`p2p-backend/src/controllers/purchase-request.controller.ts`)

**Added new interface fields:**
```typescript
interface EnrichedPurchaseRequest extends PurchaseRequest {
  totalValue: number;
  businessUnit: BusinessUnit | null;
  costCenter: CostCenter | null;
  createdByUser: User | null;
  currentApprovalLevel?: string | null;    // NEW: Display name
  currentApprovalRole?: string | null;     // NEW: Role code
}
```

**Enhanced enrichPurchaseRequests function:**
- ✅ **Batch query for current approvals** - Single query to get all pending approvals
- ✅ **Approval level mapping** - Maps role codes to display names:
  - `cc_head` → "Cost Center Head"
  - `biz_fin` → "Business Finance" 
  - `procurement_manager` → "Procurement Manager"
- ✅ **Optimized performance** - No N+1 queries for approval data

**Key implementation:**
```typescript
// Get current approval levels for all purchase requests in a single query
const currentApprovals = await AppDataSource
  .getRepository(Approval)
  .createQueryBuilder('approval')
  .where('approval.requestId IN (:...prIds)', { prIds })
  .andWhere('approval.requestType = :requestType', { requestType: RequestType.PURCHASE_REQUEST })
  .andWhere('approval.approvalType = :approvalType', { approvalType: ApprovalType.PR_APPROVAL })
  .andWhere('approval.status = :status', { status: ApprovalStatus.PENDING_APPROVAL })
  .orderBy('approval.approvalSequence', 'ASC')
  .getMany();
```

#### **2. Updated API Schema** (`p2p-backend/src/schemas/purchase-request.schema.ts`)

**Added new optional fields:**
```typescript
export const PurchaseRequestWithItemsSchema = Type.Object({
  // ... existing fields
  currentApprovalLevel: Type.Optional(Type.Union([Type.String(), Type.Null()])),
  currentApprovalRole: Type.Optional(Type.Union([Type.String(), Type.Null()])),
  // ... rest of schema
});
```

### **Frontend Changes**

#### **3. Enhanced Purchase Request Listing** (`p2p-frontend/src/pages/PurchaseRequests/index.js`)

**Updated status display to show approval level:**
```jsx
<TableCell>
  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
    <Chip
      label={getStatusDisplay(pr.status)}
      size="small"
      color={getStatusColor(pr.status)}
      variant="outlined"
      sx={{
        height: '20px',
        fontSize: '0.65rem',
        fontWeight: 'medium',
        '& .MuiChip-label': { px: 0.75 }
      }}
    />
    {/* Show approval level for pending approval status */}
    {isPendingApproval(pr.status) && pr.currentApprovalLevel && (
      <Typography 
        variant="caption" 
        sx={{ 
          fontSize: '0.6rem',
          color: 'text.secondary',
          fontWeight: 'medium',
          lineHeight: 1.2
        }}
      >
        {pr.currentApprovalLevel}
      </Typography>
    )}
  </Box>
</TableCell>
```

## 📊 **Expected Behavior**

### **For Pending Approval PRs:**
```
┌─────────────────────┐
│   Pending Approval  │  ← Status chip
├─────────────────────┤
│ Procurement Manager │  ← Approval level (NEW)
└─────────────────────┘
```

### **For Non-Pending PRs:**
```
┌─────────────────────┐
│      Approved       │  ← Status chip only
└─────────────────────┘
```

## 🧪 **Testing**

### **Test Script** (`p2p-backend/test-approval-level.js`)

**Usage:**
```bash
cd p2p-backend

# 1. Get JWT token
curl -X POST http://localhost:5100/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "your-password"}'

# 2. Update AUTH_TOKEN in test script
# 3. Run test
node test-approval-level.js
```

**Test validates:**
- ✅ API returns `currentApprovalLevel` and `currentApprovalRole` fields
- ✅ Pending PRs have approval level information
- ✅ Approval levels are correctly mapped to display names

## 🔍 **API Response Example**

```json
{
  "data": [
    {
      "id": 55,
      "status": "PENDING_PROCUREMENT_APPROVAL",
      "currentApprovalLevel": "Procurement Manager",
      "currentApprovalRole": "procurement_manager",
      "businessUnit": { "id": 1, "name": "Operations" },
      "costCenter": { "id": 2, "name": "IT Department" },
      "totalValue": 25000,
      // ... other fields
    }
  ],
  "pagination": { ... }
}
```

## 🎨 **Visual Design**

The approval level appears as small, secondary text below the status chip:

- **Font size:** `0.6rem` (smaller than status chip)
- **Color:** `text.secondary` (muted gray)
- **Weight:** `medium` (slightly bold for readability)
- **Spacing:** `0.5` gap between chip and text

## 🚀 **Performance Benefits**

1. **Single Query:** All approval levels fetched in one database query
2. **No N+1 Issues:** Batch loading prevents multiple queries per PR
3. **Cached Data:** Leverages existing caching infrastructure
4. **Minimal Overhead:** Only adds approval data for pending PRs

## ✅ **Verification Checklist**

- [ ] Backend returns `currentApprovalLevel` field for pending PRs
- [ ] Frontend displays approval level under pending status chips
- [ ] Approval levels show correct display names
- [ ] Non-pending PRs don't show approval level text
- [ ] Performance remains optimal (no additional N+1 queries)
- [ ] Test script validates functionality

## 🔄 **Future Enhancements**

1. **Color coding:** Different colors for different approval levels
2. **Icons:** Add role-specific icons next to approval levels
3. **Tooltips:** Show additional approval information on hover
4. **Progress indicators:** Show approval sequence progress
