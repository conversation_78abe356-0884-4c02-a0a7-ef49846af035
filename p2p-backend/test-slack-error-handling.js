const axios = require('axios');

/**
 * Test script to verify that Slack notification failures don't block the server
 * This simulates the scenario where Slack webhook fails but the main request should continue
 */

const BASE_URL = 'http://localhost:5100';

async function testSlackErrorHandling() {
  console.log('🧪 Testing Slack Error Handling...\n');

  // Test 1: Verify server is responsive
  console.log('📋 Test 1: Checking server health...');
  try {
    const healthResponse = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
    console.log('✅ Server is responsive');
  } catch (error) {
    console.error('❌ Server health check failed:', error.message);
    console.log('Please ensure the backend server is running on port 5100');
    return;
  }

  // Test 2: Test invoice creation (which triggers Slack notification)
  console.log('\n📋 Test 2: Testing invoice creation with potential Slack failure...');
  
  // You'll need to replace this with a valid JWT token
  const AUTH_TOKEN = 'your-jwt-token-here';
  
  if (AUTH_TOKEN === 'your-jwt-token-here') {
    console.log('❌ Please update AUTH_TOKEN with a valid JWT token');
    console.log('\n🔧 To get a token:');
    console.log(`curl -X POST ${BASE_URL}/auth/login \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -d '{"email": "<EMAIL>", "password": "your-password"}'`);
    return;
  }

  try {
    // Create a test invoice (this will trigger Slack notification)
    const invoiceData = {
      invoiceNumber: `TEST-INV-${Date.now()}`,
      invoiceValue: 1000,
      grnId: 1, // Assuming GRN ID 1 exists
      // Add other required fields as needed
    };

    console.log('🚀 Creating test invoice...');
    const startTime = Date.now();
    
    const response = await axios.post(`${BASE_URL}/invoices`, invoiceData, {
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 second timeout
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`✅ Invoice created successfully in ${duration}ms`);
    console.log(`   Invoice ID: ${response.data.id}`);
    console.log(`   Response status: ${response.status}`);

    // Test 3: Verify server is still responsive after potential Slack failure
    console.log('\n📋 Test 3: Verifying server responsiveness after invoice creation...');
    
    const healthResponse2 = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
    console.log('✅ Server is still responsive after invoice creation');

    console.log('\n🎉 SUCCESS: Server handled potential Slack failures gracefully!');
    console.log('\n📝 What this test verified:');
    console.log('   ✅ Invoice creation completed successfully');
    console.log('   ✅ Server remained responsive throughout the process');
    console.log('   ✅ Slack notification failures (if any) did not block the main thread');
    console.log('   ✅ Request lifecycle completed normally');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    
    if (error.code === 'ECONNABORTED') {
      console.error('\n💥 TIMEOUT: This suggests the server may be hanging!');
      console.error('   This could indicate that Slack notification failures are still blocking the server.');
    } else if (error.response?.status === 401) {
      console.log('\n🔧 Authentication failed. Please check your JWT token.');
    } else if (error.response?.status >= 500) {
      console.error('\n💥 Server error occurred. Check server logs for Slack notification errors.');
    }
  }
}

// Test function to simulate Slack webhook failure scenarios
async function testSlackWebhookDirectly() {
  console.log('\n🧪 Testing Slack webhook error scenarios...\n');

  // Import the sendSlackNotification function
  try {
    const { sendSlackNotification } = require('./src/utils/slackNotification');
    
    console.log('📋 Test: Sending notification with potentially invalid channel...');
    
    const testData = {
      type: 'INVOICE',
      data: {
        id: 999,
        invoiceNumber: 'TEST-999',
        invoiceValue: 1000,
        createdBy: 'Test User'
      }
    };

    const startTime = Date.now();
    
    // This should not throw an error even if Slack fails
    await sendSlackNotification(testData);
    
    const endTime = Date.now();
    console.log(`✅ Slack notification function completed in ${endTime - startTime}ms`);
    console.log('✅ Function did not throw an error (as expected)');
    
  } catch (error) {
    console.error('❌ Slack notification function threw an error:', error.message);
    console.error('   This indicates the fix may not be working correctly.');
  }
}

async function main() {
  console.log('🔧 Slack Error Handling Test Suite');
  console.log('=====================================\n');
  
  await testSlackErrorHandling();
  await testSlackWebhookDirectly();
  
  console.log('\n📊 Test Summary:');
  console.log('   - If all tests pass, Slack failures will not block your server');
  console.log('   - Check server logs for detailed Slack error messages');
  console.log('   - Invoice creation should work even if Slack notifications fail');
}

// Export for use in other scripts
module.exports = { testSlackErrorHandling, testSlackWebhookDirectly };

// Run if called directly
if (require.main === module) {
  main();
}
