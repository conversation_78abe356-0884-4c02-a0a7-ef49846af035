import { FastifyRequest, FastifyReply, FastifyInstance, FastifyPluginCallback } from 'fastify';
import fp from 'fastify-plugin';

/**
 * Configuration options for the request timeout plugin
 */
interface RequestTimeoutOptions {
  /**
   * Timeout in milliseconds after which requests will be terminated
   * @default 60000 (60 seconds)
   */
  timeout?: number;
  
  /**
   * HTTP status code to use for timeout responses
   * @default 503 (Service Unavailable)
   */
  statusCode?: number;
  
  /**
   * Routes to exclude from timeout monitoring (e.g., '/health', '/metrics')
   * @default []
   */
  excludeRoutes?: string[];
}

/**
 * Fastify plugin to add request timeout handling
 * This helps prevent hanging requests by ensuring they eventually time out
 * and return a proper error response instead of hanging indefinitely
 */
const requestTimeoutPlugin: FastifyPluginCallback<RequestTimeoutOptions> = (
  fastify: FastifyInstance,
  options: RequestTimeoutOptions,
  done
) => {
  const {
    timeout = 60000, // Default to 60 seconds
    statusCode = 503, // Default to Service Unavailable
    excludeRoutes = []
  } = options;
  
  // Add onRequest hook to handle timeouts
  fastify.addHook('onRequest', (request, reply, hookDone) => {
    // Skip timeout handling for excluded routes
    if (excludeRoutes.some(route => request.url.startsWith(route))) {
      hookDone();
      return;
    }
    
    // Set a unique request ID for logging
    const requestId = request.id || Math.random().toString(36).substring(2, 15);
    
    // Set a timeout for the request
    const timeoutId = setTimeout(() => {
      // Log detailed information about the timed-out request
      console.error({
        message: `[Request Timeout] Request timed out after ${timeout}ms`,
        requestId,
        method: request.method,
        url: request.url,
        headers: request.headers,
        params: request.params,
        query: request.query,
        timestamp: new Date().toISOString()
      });
      
      // If the response hasn't been sent yet, send a timeout response
      if (!reply.sent) {
        reply.status(statusCode).send({
          error: true,
          code: 'REQUEST_TIMEOUT',
          message: 'Request timed out. The server took too long to process your request.',
          requestId
        });
      }
    }, timeout);

    // Store the timeout ID in the request for later reference
    (request as any).timeoutId = timeoutId;

    // Clear the timeout when the response is sent
    reply.raw.on('finish', () => {
      clearTimeout(timeoutId);
    });

    // Continue processing the request
    hookDone();
  });

  // Add onError hook to handle errors and clear timeouts
  fastify.addHook('onError', (request, reply, error, hookDone) => {
    // Clear the timeout if it exists
    const timeoutId = (request as any).timeoutId;
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    // Continue error handling
    hookDone();
  });

  done();
};

/**
 * Export the plugin with fastify-plugin to ensure it's properly registered
 */
export const requestTimeout = fp(requestTimeoutPlugin, {
  name: 'request-timeout',
  fastify: '4.x'
});

/**
 * Legacy middleware function for backward compatibility
 * @deprecated Use the plugin version instead
 */
export const requestTimeoutMiddleware = (
  request: FastifyRequest,
  reply: FastifyReply,
  done: () => void
) => {
  // Set a timeout for the request (60 seconds)
  const timeoutMs = 60000;
  const timeoutId = setTimeout(() => {
    // Log the timeout
    console.error(`[Request Timeout] Request to ${request.url} timed out after ${timeoutMs}ms`);
    
    // If the response hasn't been sent yet, send a timeout response
    if (!reply.sent) {
      reply.status(503).send({
        error: true,
        code: 'REQUEST_TIMEOUT',
        message: 'Request timed out. The server is experiencing high load or is stuck processing your request.'
      });
    }
  }, timeoutMs);

  // Clear the timeout when the response is sent
  reply.raw.on('finish', () => {
    clearTimeout(timeoutId);
  });

  // Continue processing the request
  done();
};
