import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../db/typeorm.config';
import { User } from '../entities/User.entity';
import { UserRole } from '../entities/UserRole.entity';
import * as Sentry from '@sentry/node';

/**
 * Authentication middleware that verifies if the user is authenticated
 * This middleware should be applied to all protected routes
 */
// Global user cache to prevent redundant queries across requests
const globalUserCache = new Map<string, { user: any, roles: any[], timestamp: number }>();
const CACHE_TTL = 30000; // 30 seconds cache

// Cleanup expired cache entries every 5 minutes
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of globalUserCache.entries()) {
    if (now - value.timestamp > CACHE_TTL) {
      globalUserCache.delete(key);
    }
  }
  console.log(`[Auth] Cache cleanup completed. Current cache size: ${globalUserCache.size}`);
}, 300000);

export const authenticate = async (
  request: FastifyRequest,
  reply: FastifyReply
) => {
  try {
    // Skip authentication for auth routes
    if (request.url.startsWith('/auth/') && request.url !== '/auth/me') {
      return;
    }

    // Skip authentication for health check
    if (request.url === '/health') {
      return;
    }

    // Prevent duplicate authentication within the same request
    if ((request as any).isAuthenticated) {
      console.log(`[Auth] Request ${request.id} already authenticated, skipping`);
      return;
    }

    // Get token from cookie or authorization header
    let token = request.cookies?.token;
    
    if (!token && request.headers.authorization) {
      const authHeader = request.headers.authorization;
      if (authHeader.startsWith('Bearer ')) {
        token = authHeader.substring(7);
      }
    }

    if (!token) {
      return reply.status(401).send({ error: true, message: 'Authentication required' });
    }

    // Try to decode the token without verification first
    let decoded;
    try {
      // First try to verify the token
      decoded = await request.server.jwt.verify(token);
    } catch (verifyError) {
      // If verification fails, try to decode it without verification
      try {
        decoded = request.server.jwt.decode(token);
        console.log('Token decoded without verification:', decoded);
      } catch (decodeError) {
        console.error('Error decoding token:', decodeError);
        return reply.status(401).send({ 
          error: true,
          message: 'Authentication failed: Invalid token format' 
        });
      }
    }
    
    if (!decoded || !(decoded as any).id) {
      console.error('Decoded token missing ID:', decoded);
      return reply.status(401).send({ 
        error: true,
        message: 'Authentication failed: Token missing required fields' 
      });
    }
<<<<<<< Updated upstream
    
    // Get user from database
    const userRepository = AppDataSource.getRepository(User);
    const user = await userRepository.findOne({
      where: { id: (decoded as any).id }
    });
=======

    const userId = (decoded as any).id;
    const cacheKey = `user_${userId}`;

    // Check global cache first
    const cachedData = globalUserCache.get(cacheKey);
    const now = Date.now();

    let user, userRoles;

    if (cachedData && (now - cachedData.timestamp) < CACHE_TTL) {
      console.log(`[Auth] Using cached user ${userId}`);
      user = cachedData.user;
      userRoles = cachedData.roles;
    } else {
      // Get user and roles in a single optimized query
      const userRepository = AppDataSource.getRepository(User);
      console.log(`[Auth] Fetching user ${userId} with roles from database`);
      user = await userRepository.findOne({
        where: { id: userId },
        relations: ['roles'] // Load roles in the same query
      });

      if (user) {
        userRoles = user.roles || [];
        // Cache the result
        globalUserCache.set(cacheKey, {
          user: { ...user, roles: undefined }, // Don't duplicate roles in cache
          roles: userRoles,
          timestamp: now
        });
        console.log(`[Auth] User ${userId} cached with ${userRoles.length} roles`);
      }
    }
>>>>>>> Stashed changes

    if (!user) {
      return reply.status(401).send({ 
        error: true,
<<<<<<< Updated upstream
        message: `Invalid authentication token: User with ID ${(decoded as any).id} not found` 
      });
    }

    // Get user roles
    const userRoleRepository = AppDataSource.getRepository(UserRole);
    const userRoles = await userRoleRepository.find({
      where: { user: { id: user.id } }
    });

=======
        message: `Invalid authentication token: User with ID ${userId} not found`
      });
    }

>>>>>>> Stashed changes
    // Add user and roles to request
    request.user = user;
    (request as any).userRoles = userRoles;
    (request as any).isAuthenticated = true; // Mark request as authenticated

<<<<<<< Updated upstream
    // Set Sentry user context
    Sentry.setUser({
      id: user.id.toString(),
      username: user.username,
      email: user.email,
    });

=======
    // Add a simple cache to prevent redundant user queries within the same request
    (request as any).userCache = new Map();
    (request as any).userCache.set(user.id, user);

    console.log(`[Auth] Request ${request.id} authenticated successfully for user ${user.id}`);
>>>>>>> Stashed changes
  } catch (err) {
    console.error('Authentication error:', err);
    reply.status(401).send({ error: true, message: `Authentication failed: ${(err as Error).message}` });
  }
};

/**
 * Role-based access control middleware
 * This middleware checks if the user has the required roles
 * @param roles Array of roles that are allowed to access the route
 */
export const hasRole = (roles: string[]) => {
  return async (
    request: FastifyRequest,
    reply: FastifyReply
  ) => {
    try {
      const userRoles = (request as any).userRoles || [];
      
      // Check if user has any of the required roles
      const hasRequiredRole = userRoles.some((userRole: UserRole) => 
        roles.includes(userRole.role)
      );

      if (!hasRequiredRole) {
        return reply.status(403).send({ error: true, message: 'Access denied: Insufficient permissions' });
      }
    } catch (err) {
      console.error('Role check error:', err);
      reply.status(500).send({ error: true, message: `Error checking permissions: ${(err as Error).message}` });
    }
  };
};
