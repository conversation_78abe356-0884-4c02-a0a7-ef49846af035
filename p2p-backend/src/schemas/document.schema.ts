import { Static, Type } from '@sinclair/typebox';

export enum EntityType {
  PURCHASE_REQUEST = 'purchase_request',
  PURCHASE_ORDER = 'purchase_order',
  QUOTATION = 'quotation',
  INVOICE = 'invoice',
  GRN = 'grn'
}

export const DocumentSchema = Type.Object({
  id: Type.Number(),
  fileName: Type.String(),
  fileKey: Type.String(),
  fileUrl: Type.String(),
  fileSize: Type.Number(),
  contentType: Type.String(),
  entityType: Type.Enum(EntityType),
  entityId: Type.Number(),
  displayName: Type.Optional(Type.String()),
  createdAt: Type.String({ format: 'date-time' }),
  updatedAt: Type.String({ format: 'date-time' })
});

export const CreateDocumentSchema = Type.Object({
  fileName: Type.String(),
  fileKey: Type.String(),
  fileSize: Type.Number(),
  contentType: Type.String(),
  entityType: Type.Enum(EntityType),
  entityId: Type.Number(),
  displayName: Type.Optional(Type.String())
});

export const DocumentResponseSchema = Type.Object({
  document: DocumentSchema
});

export const DocumentsResponseSchema = Type.Object({
  documents: Type.Array(DocumentSchema)
});

export const FileUploadResponseSchema = Type.Object({
  fileKey: Type.String(),
  fileUrl: Type.String()
});

export type Document = Static<typeof DocumentSchema>;
export type CreateDocument = Static<typeof CreateDocumentSchema>;
