import { Type } from '@sinclair/typebox';
import { ApprovalStatus } from '../constants/enums';

// Common response schemas
export const ErrorResponseSchema = {
  400: Type.Object({
    error: Type.Boolean(),
    message: Type.String()
  }),
  403: Type.Object({
    error: Type.Boolean(),
    message: Type.String()
  }),
  404: Type.Object({
    error: Type.Boolean(),
    message: Type.String()
  }),
  500: Type.Object({
    error: Type.Boolean(),
    message: Type.String()
  })
};

// User Schema
export const UserSchema = Type.Object({
  id: Type.Number(),
  username: Type.String(),
  email: Type.Optional(Type.Union([Type.String(), Type.Null()])),
  name: Type.Optional(Type.Union([Type.String(), Type.Null()])),
  createdAt: Type.Optional(Type.String()),
  updatedAt: Type.Optional(Type.String())
});

// Business Unit and Cost Center Schemas
export const CostCenterSchema = Type.Object({
  id: Type.Number(),
  name: Type.String()
});

export const BusinessUnitSchema = Type.Object({
  id: Type.Number(),
  name: Type.String()
});

// Purchase Request Item Schema
export const PrItemSchema = Type.Object({
  id: Type.Number(),
  prId: Type.Number(),
  itemCategoryId: Type.Union([Type.Number(), Type.Null()]),
  itemName: Type.Union([Type.String(), Type.Null()]),
  itemDescription: Type.Union([Type.String(), Type.Null()]),
  quantity: Type.Union([Type.Number(), Type.String(), Type.Null()]),
  uom: Type.Union([Type.String(), Type.Null()]),
  estimatedPricePerQuantity: Type.Union([Type.Number(), Type.String(), Type.Null()])
});

// Purchase Request Schema
export const PurchaseRequestSchema = Type.Object({
  id: Type.Number(),
  prType: Type.String(),
  status: Type.String(),
  costCenterId: Type.Union([Type.Number(), Type.Null()]),
  businessUnitId: Type.Union([Type.Number(), Type.Null()]),
  remarks: Type.Union([Type.String(), Type.Null()]),
  createdBy: Type.Number(),
  createdAt: Type.String(),
  updatedAt: Type.String()
});

// Purchase Request with Items Schema
export const PurchaseRequestWithItemsSchema = Type.Object({
  id: Type.Number(),
  prType: Type.String(),
  status: Type.String(),
  costCenterId: Type.Union([Type.Number(), Type.Null()]),
  businessUnitId: Type.Union([Type.Number(), Type.Null()]),
  remarks: Type.Union([Type.String(), Type.Null()]),
  createdBy: Type.Number(),
  createdAt: Type.String(),
  updatedAt: Type.String(),
  items: Type.Array(PrItemSchema),
  totalValue: Type.Optional(Type.Number()),
  businessUnit: Type.Optional(Type.Union([BusinessUnitSchema, Type.Null()])),
  costCenter: Type.Optional(Type.Union([CostCenterSchema, Type.Null()])),
  createdByUser: Type.Optional(Type.Union([UserSchema, Type.Null()])),
  // Verification dialog properties
  showPRVerificationDialog: Type.Optional(Type.Boolean()),
  showPRVerificationToRole: Type.Optional(Type.Union([Type.String(), Type.Null()])),
  // Purchase Order related information
  poId: Type.Optional(Type.Union([Type.Number(), Type.Null()])),
  selectedQuoteId: Type.Optional(Type.Union([Type.Number(), Type.Null()])),
  poStatus: Type.Optional(Type.String()),
  // Document attachments
  attachments: Type.Optional(Type.Array(
    Type.Object({
      id: Type.Number(),
      fileName: Type.String(),
      fileKey: Type.String(),
      fileUrl: Type.String(),
      fileSize: Type.Optional(Type.Number()),
      contentType: Type.Optional(Type.String()),
      entityType: Type.String(),
      entityId: Type.Number(),
      displayName: Type.Optional(Type.Union([Type.String(), Type.Null()])),
      createdAt: Type.Optional(Type.String()),
      updatedAt: Type.Optional(Type.String())
    })
  ))
});

// Approval Schema
export const ApprovalSchema = Type.Object({
  id: Type.Number(),
  requestId: Type.Number(),
  requestType: Type.String(),
  approvalType: Type.String(),
  approvalSequence: Type.Number(),
  status: Type.String(),
  remarks: Type.Union([Type.String(), Type.Null()]),
  verifiedBy: Type.Union([Type.Number(), Type.Null()]),
  verifiedAt: Type.Union([Type.String(), Type.Null()])
});

// Approval with Created/Updated timestamps Schema
export const ApprovalWithTimestampsSchema = Type.Object({
  id: Type.Number(),
  requestId: Type.Number(),
  requestType: Type.String(),
  approvalType: Type.String(),
  approvalSequence: Type.Number(),
  verifiedBy: Type.Union([Type.Number(), Type.Null()]),
  status: Type.String(),
  remarks: Type.Union([Type.String(), Type.Null()]),
  verifiedAt: Type.Union([Type.String(), Type.Null()]),
  createdAt: Type.String(),
  updatedAt: Type.String()
});

// Pagination Schema
export const PaginationSchema = Type.Object({
  total: Type.Number(),
  limit: Type.Number(),
  offset: Type.Number(),
  hasMore: Type.Boolean()
});

// Request Schemas
export const CreatePurchaseRequestBodySchema = Type.Object({
  prType: Type.String(),
  costCenterId: Type.Optional(Type.Number()),
  businessUnitId: Type.Optional(Type.Number()),
  remarks: Type.Optional(Type.String()),
  items: Type.Array(
    Type.Object({
      itemCategoryId: Type.Number(),
      itemName: Type.String(),
      itemDescription: Type.Optional(Type.String()),
      quantity: Type.Union([Type.Number(), Type.String()]),
      uom: Type.String(),
      estimatedPricePerQuantity: Type.Number()
    })
  )
});

export const ApprovalActionBodySchema = Type.Object({
  status: Type.Enum(ApprovalStatus),
  remarks: Type.Optional(Type.String())
});

export const ResubmitBodySchema = Type.Object({
  remarks: Type.Optional(Type.String())
});

export const IdParamSchema = Type.Object({
  id: Type.Number()
});

export const RequestIdParamSchema = Type.Object({
  requestId: Type.Number()
});

export const PurchaseRequestQuerySchema = Type.Object({
  limit: Type.Optional(Type.Number()),
  offset: Type.Optional(Type.Number()),
  status: Type.Optional(Type.String())
});

export const BusinessUnitWithCostCentersSchema = Type.Object({
  id: Type.Number(),
  name: Type.String(),
  costCenters: Type.Optional(Type.Array(CostCenterSchema))
});

// Response Schemas
export const GetAllPurchaseRequestsResponseSchema = Type.Object({
  data: Type.Array(PurchaseRequestWithItemsSchema),
  pagination: PaginationSchema
});

export const ApprovalActionResponseSchema = Type.Object({
  message: Type.String(),
  approval: ApprovalSchema
});

export const ResubmitResponseSchema = Type.Object({
  message: Type.String(),
  purchaseRequest: PurchaseRequestSchema
});

export const BusinessUnitsWithCostCentersResponseSchema = Type.Array(BusinessUnitWithCostCentersSchema);
