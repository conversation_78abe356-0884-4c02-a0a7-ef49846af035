import { Type } from '@sinclair/typebox';
import { InvoiceType } from '../entities/Invoice.entity';

// Schema for creating an advance invoice
export const CreateAdvanceInvoiceSchema = Type.Object({
  poId: Type.Number(),
  invoiceNumber: Type.Optional(Type.String()),
  invoiceValue: Type.Number({ minimum: 0 })
});

// Schema for creating a bill with GRN items
export const CreateBillWithGrnSchema = Type.Object({
  poId: Type.Number(),
  invoiceNumber: Type.Optional(Type.String()),
  invoiceValue: Type.Number({ minimum: 0 }),
  grnItems: Type.Array(
    Type.Object({
      poItemId: Type.Number(),
      grnQuantity: Type.Number({ minimum: 0 })
    })
  )
});

// Common response schema for all invoice related operations
export const InvoiceResponseSchema = Type.Object({
  message: Type.String(),
  invoice: Type.Object({
    id: Type.Number(),
    poId: Type.Number(),
    type: Type.Enum(InvoiceType),
    invoiceNumber: Type.Optional(Type.String()),
    invoiceValue: Type.Number(),
    createdAt: Type.String({ format: 'date-time' })
  })
});

// Detailed invoice response including GRN items if present
export const DetailedInvoiceResponseSchema = Type.Object({
  message: Type.String(),
  invoice: Type.Object({
    id: Type.Number(),
    poId: Type.Number(),
    type: Type.Enum(InvoiceType),
    invoiceNumber: Type.Optional(Type.String()),
    invoiceValue: Type.Number(),
    createdBy: Type.Number(),
    createdAt: Type.String({ format: 'date-time' }),
    updatedAt: Type.String({ format: 'date-time' }),
    purchaseOrder: Type.Object({
      id: Type.Number(),
      poNumber: Type.String(),
      status: Type.String()
    }),
    grnItems: Type.Array(
      Type.Object({
        id: Type.Number(),
        poItemId: Type.Number(),
        grnQuantity: Type.Number(),
        poItem: Type.Object({
          id: Type.Number(),
          itemName: Type.String(),
          quantity: Type.Number(),
          pricePerUnit: Type.Number()
        })
      })
    )
  })
});

// Schema for ID parameter
export const IdParamSchema = Type.Object({
  id: Type.Number()
});

// Schema for error response
export const ErrorResponseSchema = Type.Object({
  error: Type.Boolean(),
  message: Type.String()
});
