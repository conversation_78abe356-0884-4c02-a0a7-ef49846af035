import { Type } from '@sinclair/typebox';

// Schema for creating a new billing address
export const CreateBillingAddressSchema = Type.Object({
  address: Type.String({ minLength: 1 }),
  company_name: Type.Optional(Type.String()),
  gstin: Type.Optional(Type.String())
});

// Schema for updating a billing address
export const UpdateBillingAddressSchema = Type.Object({
  address: Type.String({ minLength: 1 }),
  company_name: Type.Optional(Type.String()),
  gstin: Type.Optional(Type.String())
});

// Schema for ID parameter
export const IdParamSchema = Type.Object({
  id: Type.Number()
});

// Response schema for a single billing address
export const BillingAddressResponseSchema = Type.Object({
  id: Type.Number(),
  address: Type.String(),
  company_name: Type.Union([Type.String(), Type.Null()]),
  gstin: Type.Union([Type.String(), Type.Null()]),
  createdAt: Type.String({ format: 'date-time' }),
  updatedAt: Type.String({ format: 'date-time' }),
  deletedAt: Type.Union([
    Type.String({ format: 'date-time' }),
    Type.Null()
  ])
});

// Response schema for a list of billing addresses with pagination
export const BillingAddressListResponseSchema = Type.Object({
  data: Type.Array(BillingAddressResponseSchema),
  pagination: Type.Object({
    totalCount: Type.Number(),
    page: Type.Number(),
    limit: Type.Number(),
    totalPages: Type.Number()
  })
});

// Query params schema for filtering billing addresses
export const BillingAddressQuerySchema = Type.Object({
  page: Type.Optional(Type.Number({ minimum: 1 })),
  limit: Type.Optional(Type.Number({ minimum: 1, maximum: 100 })),
  search: Type.Optional(Type.String()),
  includeDeleted: Type.Optional(Type.Boolean())
});
