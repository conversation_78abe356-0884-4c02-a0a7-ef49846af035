/**
 * Enum for approval roles
 */
export enum ApprovalRole {
  CC_HEAD = 'cc_head',
  BIZ_FIN = 'biz_fin',
  PROCUREMENT_MANAGER = 'procurement_manager',
  FINANCE = 'finance'
}

/**
 * Enum for approval types
 */
export enum ApprovalType {
  PR_APPROVAL = 'PR_APPROVAL',
  QUOTE_APPROVAL = 'QUOTE_APPROVAL',
  INVOICE_APPROVAL = 'INVOICE_APPROVAL',
}

/**
 * Enum for approval statuses
 */
export enum ApprovalStatus {
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  ON_HOLD = 'ON_HOLD',
  SKIPPED = 'SKIPPED'
}

/**
 * Enum for purchase request types
 */
export enum PurchaseRequestType {
  STANDARD = 'STANDARD',
  URGENT = 'URGENT'
}

/**
 * Enum for purchase request statuses
 */
export enum PurchaseRequestStatus {
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED',
  VOID = 'VOID'
}

/**
 * Enum for request types (for polymorphic relationships)
 */
export enum RequestType {
  PURCHASE_REQUEST = 'PurchaseRequest',
  INVOICE = 'Invoice'
}

export enum EmailRequestType {
  PURCHASE_REQUEST = 'PurchaseRequest',
  INVOICE = 'Invoice',
  PURCHASE_ORDER = 'PurchaseOrder',
  NEXT_APPROVAL = 'NextApproval'
}
