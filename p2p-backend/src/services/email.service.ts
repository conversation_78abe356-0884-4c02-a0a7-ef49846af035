import nodemailer from 'nodemailer';

type EmailOptions = {
  to: string;
  subject: string;
  html: string;
  from?: string;
};

class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private fromEmail: string;

  constructor() {
    console.log('[EmailService] Initializing...');
    
    this.fromEmail = process.env.EMAIL_FROM || '<EMAIL>';
    
    if (!process.env.EMAIL_HOST || !process.env.EMAIL_USER || !process.env.EMAIL_PASSWORD) {
      console.warn('[EmailService] Email configuration is incomplete. Email notifications will not be sent.');
      return;
    }

    try {
      console.log('[EmailService] Creating SMTP transporter...');
      
      this.transporter = nodemailer.createTransport({
        host: process.env.EMAIL_HOST,
        port: parseInt(process.env.EMAIL_PORT || '587', 10),
        secure: process.env.EMAIL_SECURE === 'true',
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASSWORD,
        },
        debug: true
      });
      
      // Verify connection configuration
      this.transporter.verify((error) => {
        if (error) {
          console.error('[EmailService] SMTP Connection Error:', error.message);
        } else {
          console.log('[EmailService] SMTP Server is ready');
        }
      });
      
    } catch (error) {
      console.error('[EmailService] Failed to create email transporter:', error);
    }
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    console.log(`[EmailService] Preparing to send email to: ${options.to}`);

    if (process.env.ENV != 'production') {
      console.log('[EmailService] Email not sent in non-production environment');
      return false;
    }
    
    if (!this.transporter) {
      console.warn('[EmailService] Email transporter not configured. Email not sent.');
      return false;
    }

    try {
      const mailOptions = {
        from: this.fromEmail,
        to: options.to,
        subject: options.subject,
        html: options.html,
      };
      
      console.log(`[EmailService] Sending email to ${options.to} with subject: ${options.subject}`);
      
      const info = await this.transporter.sendMail(mailOptions);
      
      console.log(`[EmailService] Email sent successfully! Message ID: ${info.messageId}`);
      
      return true;
      
    } catch (error) {
      console.error('[EmailService] Error sending email:');
      console.error(error);
      return false;
    }
  }
}

export const emailService = new EmailService();
