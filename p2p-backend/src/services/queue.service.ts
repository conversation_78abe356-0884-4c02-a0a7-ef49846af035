type Job = () => Promise<void>;

interface QueueStats {
  totalProcessed: number;
  totalErrors: number;
  queueLength: number;
  isProcessing: boolean;
  lastProcessedAt: Date | null;
  lastErrorAt: Date | null;
}

class QueueService {
  private queue: Job[] = [];
  private isProcessing = false;
  private static instance: QueueService;
  private concurrentJobs = 0;
  private maxConcurrentJobs = 5; // Process up to 5 jobs concurrently
  private processingTimeout: NodeJS.Timeout | null = null;
  
  // Stats tracking
  private stats: QueueStats = {
    totalProcessed: 0,
    totalErrors: 0,
    queueLength: 0,
    isProcessing: false,
    lastProcessedAt: null,
    lastErrorAt: null
  };

  private constructor() {
    // Start processing queue immediately
    this.scheduleQueueProcessing();
    
    // Log queue stats every minute
    setInterval(() => this.logQueueStats(), 60000);
  }

  public static getInstance(): QueueService {
    if (!QueueService.instance) {
      QueueService.instance = new QueueService();
    }
    return QueueService.instance;
  }

  public addToQueue(job: Job): void {
    this.queue.push(job);
    this.stats.queueLength = this.queue.length;
    
    // Ensure queue processing is scheduled
    this.scheduleQueueProcessing();
  }
  
  private scheduleQueueProcessing(): void {
    // If already scheduled or processing at max capacity, do nothing
    if (this.processingTimeout || this.concurrentJobs >= this.maxConcurrentJobs) {
      return;
    }
    
    // Schedule immediate processing
    this.processingTimeout = setTimeout(() => {
      this.processingTimeout = null;
      this.processQueue();
    }, 0);
  }

  private async processQueue(): Promise<void> {
    // If queue is empty, nothing to do
    if (this.queue.length === 0) {
      this.isProcessing = false;
      return;
    }
    
    this.isProcessing = true;
    this.stats.isProcessing = true;
    
    // Process jobs while there are jobs in the queue and we're below max concurrent jobs
    while (this.queue.length > 0 && this.concurrentJobs < this.maxConcurrentJobs) {
      const job = this.queue.shift();
      if (job) {
        this.concurrentJobs++;
        this.stats.queueLength = this.queue.length;
        
        // Process job asynchronously without awaiting
        this.processJob(job).finally(() => {
          this.concurrentJobs--;
          // Schedule more processing if there are more jobs
          if (this.queue.length > 0) {
            this.scheduleQueueProcessing();
          } else if (this.concurrentJobs === 0) {
            this.isProcessing = false;
            this.stats.isProcessing = false;
          }
        });
      }
    }
  }
  
  private async processJob(job: Job): Promise<void> {
    try {
      await job();
      this.stats.totalProcessed++;
      this.stats.lastProcessedAt = new Date();
    } catch (error) {
      this.stats.totalErrors++;
      this.stats.lastErrorAt = new Date();
      console.error('[QueueService] Error processing job:', error);
    }
  }
  
  private logQueueStats(): void {
    console.log('[QueueService] Stats:', {
      queueLength: this.queue.length,
      concurrentJobs: this.concurrentJobs,
      totalProcessed: this.stats.totalProcessed,
      totalErrors: this.stats.totalErrors,
      isProcessing: this.isProcessing
    });
  }
  
  public getStats(): QueueStats {
    return { ...this.stats, queueLength: this.queue.length };
  }
}

export const queueService = QueueService.getInstance();
