import { emailService } from './email.service';
import { queueService } from './queue.service';
import { ApprovalRole, ApprovalStatus, EmailRequestType } from '../constants/enums';
import { User } from '../entities/User.entity';
import { AppDataSource } from '../db/typeorm.config';
import { templates, TemplateData } from './email/templates';
import { PurchaseRequest } from '../entities/PurchaseRequest.entity';
import { PurchaseOrder } from '../entities/PurchaseOrder.entity';
import { Invoice } from '../entities/Invoice.entity';

// Map request types to their corresponding entity classes
const requestTypeToEntity = {
  [EmailRequestType.PURCHASE_REQUEST]: PurchaseRequest,
  [EmailRequestType.PURCHASE_ORDER]: PurchaseOrder,
  [EmailRequestType.INVOICE]: Invoice,
  [EmailRequestType.NEXT_APPROVAL]: null
};

class NotificationService {
  async sendApprovalNotification(
    requestType: EmailRequestType,
    requestId: number,
    approver: User,
    status: ApprovalStatus,
    approvalRole: ApprovalRole,
    remarks?: string,
    requestDisplay?: string,
  ): Promise<boolean> {
    // Add the email sending job to the queue and return immediately
    queueService.addToQueue(async () => {
      console.log(`[NotificationService] Processing email for ${requestDisplay} #${requestId} in background`);
      
      try {
        let templateData: TemplateData;
        
        if (requestType === EmailRequestType.NEXT_APPROVAL) {
          const requestTypeDisplay = this.formatRequestType(requestDisplay || 'request');
          const subject = `${requestTypeDisplay} waiting to be Approved: ${requestTypeDisplay} #${requestId}`;
          templateData = {
            requestType: requestTypeDisplay,
            requestId,
            status,
            approvalRole,
            recipient: {
              firstname: approver.firstName,
              lastname: approver.lastName,
            },
            approver: {
              firstName: approver.firstName,
              lastName: approver.lastName,
            },
            remarks,
            frontendUrl: process.env.FRONTEND_URL + this.formatUrl(requestDisplay || 'request', requestId),
            recipientType: 'creator',
          };
          const html = templates.nextApprover(templateData);
          await emailService.sendEmail({
            to: approver.email,
            subject,
            html
          });
        }
        else{
          const entityClass = requestTypeToEntity[requestType];
          if (entityClass) {
            const requestRepo = AppDataSource.getRepository(entityClass);
            const userRepo = AppDataSource.getRepository(User);
            
            const request = await requestRepo.findOne({
              where: { id: requestId }
            });

            if (!request) {
              console.error(`[NotificationService] Request ${requestId} not found`);
              return;
            }

            let creator = await userRepo.findOne({ where: { id: request.createdBy } });
            if (!creator || !creator.email) {
              console.error('[NotificationService] Creator or creator email not found');
              return;
            }

            const requestTypeDisplay = this.formatRequestType(requestType);
            const isApproved = status === ApprovalStatus.APPROVED;
            const subject = isApproved 
              ? `${requestTypeDisplay} Approved: ${requestType} #${requestId}`
              : `${requestTypeDisplay} Rejected: ${requestType} #${requestId}`;
            
            // Prepare template data
            templateData = {
              requestType: requestTypeDisplay,
              requestId,
              status,
              approvalRole,
              recipient: {
                firstname: creator.firstName,
                lastname: creator.lastName,
              },
              approver: {
                firstName: approver.firstName,
                lastName: approver.lastName,
              },
              remarks,
              frontendUrl: process.env.FRONTEND_URL + this.formatUrl(requestType, requestId),
              recipientType: 'creator',
            };

            const html = templates.creator(templateData);

            await emailService.sendEmail({
              to: creator.email,
              subject,
              html
            });
          }
        }
       
        console.log(`[NotificationService] Email sent for ${requestType} #${requestId}`);
      } catch (error) {
        console.error(`[NotificationService] Error processing email for ${requestType} #${requestId}:`, error);
      }
    });
    
    // Return true immediately as we've queued the job
    return true;
  }

  private formatRequestType(requestType: string): string {
    switch (requestType) {
      case EmailRequestType.PURCHASE_ORDER:
        return 'Purchase Order';
      case EmailRequestType.PURCHASE_REQUEST:
        return 'Purchase Request';
      case EmailRequestType.INVOICE:
        return 'Invoice';
      default:
        return requestType;
    }
  }

  private formatUrl(requestType: string, requestId: number): string {
    switch (requestType) {
      case EmailRequestType.PURCHASE_ORDER:
        return `/purchase-orders/${requestId}`;
      case EmailRequestType.PURCHASE_REQUEST:
        return `/approval-detail/${requestId}`;
      case EmailRequestType.INVOICE:
        return `/invoices/${requestId}`;
      default:
        return requestType;
    }
  }


}

export const notificationService = new NotificationService();
