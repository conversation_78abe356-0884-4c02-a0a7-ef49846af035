import { getRepository, Repository, Like, ILike } from 'typeorm';
import { Document } from '../entities/document.entity';
import { EntityType } from '../schemas/document.schema';
import s3Service from './s3.service';
import { AppDataSource } from '../db/typeorm.config';

export class DocumentService {
  private get documentRepository(): Repository<Document> {
    return AppDataSource.getRepository(Document);
  }

  /**
   * Create a new document record
   */
  async createDocument(documentData: Partial<Document>): Promise<Document> {
    try {
      const document = this.documentRepository.create(documentData);
      return await this.documentRepository.save(document);
    } catch (error) {
      console.error('Error creating document record:', error instanceof Error ? error.message : String(error));
      throw new Error('Failed to create document record');
    }
  }

  /**
   * Get all documents for a specific entity
   */
  async getDocumentsByEntity(entityType: EntityType | string, entityId: number): Promise<Document[]> {
    try {
      console.log(`Fetching documents for ${entityType} with ID ${entityId}`);
      
      const documents = await this.documentRepository.find({
        where: {
          entityType,
          entityId
        },
        order: {
          createdAt: 'DESC'
        }
      });
      
      console.log(`Found ${documents.length} documents for ${entityType} ${entityId}:`, 
        documents.map(d => ({ id: d.id, fileKey: d.fileKey, fileName: d.fileName })));

      // Generate fresh pre-signed URLs for all documents in parallel
      if (documents.length > 0) {
        await Promise.all(documents.map(async (doc) => {
          try {
            doc.fileUrl = await s3Service.getFileUrl(doc.fileKey);
          } catch (error) {
            console.error(`Error generating URL for file ${doc.fileKey}:`, error);
            doc.fileUrl = ''; // Set empty string as fallback
          }
        }));
      }

      return documents;
    } catch (error) {
      console.error(`Error fetching documents for ${entityType} ${entityId}:`, error instanceof Error ? error.message : String(error));
      throw new Error(`Failed to fetch documents for ${entityType} ${entityId}`);
    }
  }

  /**
   * Get a single document by its ID
   */
  async getDocumentById(id: number): Promise<Document | null> {
    try {
      const document = await this.documentRepository.findOne({
        where: { id }
      });
      
      if (document) {
        // Generate a fresh pre-signed URL
        document.fileUrl = await s3Service.getFileUrl(document.fileKey);
      }
      
      return document;
    } catch (error) {
      console.error(`Error fetching document with ID ${id}:`, error instanceof Error ? error.message : String(error));
      throw new Error(`Failed to fetch document with ID ${id}`);
    }
  }

  /**
   * Delete a document and its file from S3
   */
  async deleteDocument(id: number): Promise<boolean> {
    try {
      const document = await this.documentRepository.findOne({
        where: { id }
      });
      
      if (!document) {
        return false;
      }
      
      // Delete from S3 first
      await s3Service.deleteFile(document.fileKey);
      
      // Then delete from database
      await this.documentRepository.remove(document);
      
      return true;
    } catch (error) {
      console.error(`Error deleting document with ID ${id}:`, error instanceof Error ? error.message : String(error));
      throw new Error(`Failed to delete document with ID ${id}`);
    }
  }

  /**
   * Search for documents by filename
   */
  async searchDocuments(searchTerm: string): Promise<Document[]> {
    try {
      const documents = await this.documentRepository.find({
        where: [
          { fileName: ILike(`%${searchTerm}%`) },
          { displayName: ILike(`%${searchTerm}%`) }
        ],
        order: {
          createdAt: 'DESC'
        },
        take: 20
      });
      
      // Generate fresh pre-signed URLs for all documents in parallel
      if (documents.length > 0) {
        await Promise.all(documents.map(async (doc) => {
          try {
            doc.fileUrl = await s3Service.getFileUrl(doc.fileKey);
          } catch (error) {
            console.error(`Error generating URL for file ${doc.fileKey}:`, error);
            doc.fileUrl = ''; // Set empty string as fallback
          }
        }));
      }
      
      return documents;
    } catch (error) {
      console.error(`Error searching for documents with term '${searchTerm}':`, error instanceof Error ? error.message : String(error));
      throw new Error(`Failed to search for documents`);
    }
  }
}

// Create a singleton instance
export const documentService = new DocumentService();
