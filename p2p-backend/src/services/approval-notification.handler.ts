import { ApprovalRole, ApprovalStatus, ApprovalType, RequestType } from '../constants/enums';
import { notificationService } from './notification.service';
import { User } from '../entities/User.entity';
import { EmailRequestType } from '../constants/enums';

export class ApprovalNotificationHandler {
  static async handleApproval(
    requestType: EmailRequestType,
    requestId: number,
    approver: User,
    status: ApprovalStatus,
    approvalRole: ApprovalRole,
    remarks?: string,
    requestDisplay?: string
  ): Promise<void> {
    try {
      // TODO: Remove this for Staging & local after testing
      await notificationService.sendApprovalNotification(
        requestType,
        requestId,
        approver,
        status,
        approvalRole,
        remarks,
        requestDisplay
      );
    } catch (error) {
      console.error('Error in approval notification handler:', error);
    }
  }

  // Helper methods for specific approval types
  static async handlePurchaseRequestApproval(
    requestId: number,
    approver: User,
    status: ApprovalStatus,
    approvalRole: ApprovalRole,
    remarks?: string,
    requestDisplay?: string
  ): Promise<void> {
    return this.handleApproval(
      EmailRequestType.PURCHASE_REQUEST,
      requestId,
      approver,
      status,
      approvalRole,
      remarks,
      requestDisplay
    );
  }

  static async handleInvoiceApproval(
    requestId: number,
    approver: User,
    status: ApprovalStatus,
    approvalRole: ApprovalRole,
    remarks?: string,
    requestDisplay?: string
  ): Promise<void> {
    return this.handleApproval(
      EmailRequestType.INVOICE,
      requestId,
      approver,
      status,
      approvalRole,
      remarks,
      requestDisplay
    );
  }

  static async handlePurchaseOrderApproval(
    requestId: number,
    approver: User,
    status: ApprovalStatus,
    approvalRole: ApprovalRole,
    remarks?: string,
    requestDisplay?: string
  ): Promise<void> {
    return this.handleApproval(
      EmailRequestType.PURCHASE_ORDER,
      requestId,
      approver,
      status,
      approvalRole,
      remarks,
      requestDisplay
    );
  }

  static async handleNextApproverApproval(
    requestId: number,
    approver: User,
    status: ApprovalStatus,
    approvalRole: ApprovalRole,
    remarks?: string,
    requestDisplay?: EmailRequestType,
  ): Promise<void> {
    return this.handleApproval(
      EmailRequestType.NEXT_APPROVAL,
      requestId,
      approver,
      status,
      approvalRole,
      remarks,
      requestDisplay
    );
  }
}
