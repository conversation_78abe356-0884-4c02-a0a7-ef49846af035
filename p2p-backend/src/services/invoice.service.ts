import { AppDataSource } from '../db/typeorm.config';
import { Invoice } from '../entities/Invoice.entity';

export const getAllInvoices = async (page: number = 1, limit: number = 10) => {
  const skip = (page - 1) * limit;
  const invoiceRepo = AppDataSource.getRepository(Invoice);
  
  // Get total count
  const totalCount = await invoiceRepo.count();
  
  // Get paginated results
  const invoices = await invoiceRepo.find({
    relations: ['grn', 'grn.purchaseOrder', 'grn.purchaseOrder.quotation', 'creator'],
    order: { id: 'DESC' },
    skip,
    take: limit
  });
  
  return {
    invoices,
    pagination: {
      totalCount,
      page,
      limit,
      totalPages: Math.ceil(totalCount / limit)
    }
  };
};

export const getInvoiceById = async (invoiceId: number) => {
  const invoiceRepo = AppDataSource.getRepository(Invoice);
  return invoiceRepo.findOne({
    where: { id: invoiceId },
    relations: [
      'grn',
      'grn.purchaseOrder',  
      'grn.purchaseOrder.quotation',
      'creator',
    ],
  });
};
