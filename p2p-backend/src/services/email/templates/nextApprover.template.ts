import { TemplateData } from './types';

export const nextApproverTemplate = (data: TemplateData) => {
  const { requestType, requestId, approvalRole, frontendUrl, recipient } = data;
  
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; color: #333;">
      <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-bottom: 1px solid #e0e0e0;">
        <h1 style="color: #2c7d3f; margin: 0;">Vegrow P2P Portal</h1>
        <h2 style="color: #333; margin: 10px 0 0 0;">Action Required: ${requestType} Needs Your Approval</h2>
      </div>
      
      <div style="padding: 20px;">
        <p>Hello ${recipient.firstname} ${recipient.lastname},</p>
        <p>A ${requestType} (#${requestId}) requires your ${approvalRole} approval.</p>
        <p>Please review and take appropriate action in the 
          <a href="${frontendUrl}" style="color: #2c7d3f; text-decoration: none;">P2P Portal</a>.
        </p>
        
        <div style="margin: 25px 0; text-align: center;">
          <a href="${frontendUrl}" 
             style="background-color: #2c7d3f; color: white; padding: 10px 20px; 
                    text-decoration: none; border-radius: 4px; display: inline-block;">
            Review ${requestType}
          </a>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="margin: 0 0 10px 0;">Best regards,<br/>Vegrow P2P Team</p>
          <img src="https://www.vegrow.in/static/media/vegrow-logo.2d3c0b6b.svg" 
               alt="Vegrow Logo" 
               style="max-width: 120px; height: auto; margin-top: 10px;">
        </div>
      </div>
    </div>
  `;
};
