import { TemplateData } from './types';

export const creatorTemplate = (data: TemplateData) => {
  const { requestType, requestId, status, approvalRole, approver, remarks, frontendUrl, recipient } = data;
  const isApproved = status === 'APPROVED';
  const statusText = isApproved ? 'Approved' : 'Rejected';
  
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; color: #333;">
      <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-bottom: 1px solid #e0e0e0;">
        <h1 style="color: #2c7d3f; margin: 0;">Vegrow P2P Portal</h1>
        <h2 style="color: #333; margin: 10px 0 0 0;">${requestType} ${statusText}</h2>
      </div>
      
      <div style="padding: 20px;">
        <p>Hello ${recipient.firstname} ${recipient.lastname},</p>
        <p>Your ${requestType} #${requestId} has been 
          <strong style="color: ${isApproved ? '#2c7d3f' : '#d32f2f'}">${status.toLowerCase()}</strong> 
          by ${approvalRole} (${approver.firstName} ${approver.lastName}).
        </p>
        
        ${remarks ? `
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0;">
          <p style="margin: 0 0 8px 0; font-weight: bold; color: #555;">Remarks:</p>
          <p style="margin: 0; color: #333;">${remarks}</p>
        </div>
        ` : ''}
        
        <p>You can view the details in the 
          <a href="${frontendUrl}" style="color: #2c7d3f; text-decoration: none;">P2P Portal</a>.
        </p>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="margin: 0 0 10px 0;">Best regards,<br/>Vegrow P2P Team</p>
          <img src="https://www.vegrow.in/static/media/vegrow-logo.2d3c0b6b.svg" 
               alt="Vegrow Logo" 
               style="max-width: 120px; height: auto; margin-top: 10px;">
        </div>
      </div>
    </div>
  `;
};
