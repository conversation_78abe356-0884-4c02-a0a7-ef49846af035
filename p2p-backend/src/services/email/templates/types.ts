import { ApprovalRole, ApprovalStatus } from '../../../constants/enums';

export interface TemplateData {
  requestType: string;
  requestId: number;
  status: ApprovalStatus;
  approvalRole: ApprovalRole;
  recipient: {
    firstname: string;
    lastname: string;
  };
  approver: {
    firstName: string;
    lastName: string;
  };
  remarks?: string;
  frontendUrl: string;
  recipientType: 'creator' | 'nextApprover';
}

export type TemplateFunction = (data: TemplateData) => string;
