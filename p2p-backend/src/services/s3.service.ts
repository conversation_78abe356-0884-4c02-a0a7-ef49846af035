import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import * as dotenv from 'dotenv';
import { createReadStream, ReadStream } from 'fs';
import { FastifyRequest } from 'fastify';
import * as path from 'path';
import * as crypto from 'crypto';

// Load environment variables if not done already
dotenv.config();

const s3Config = {
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || ''
  },
  region: process.env.AWS_REGION || 'ap-south-1',
  bucketName: process.env.ENV === 'production' ? 
  (process.env.AWS_BUCKET_NAME || 'vg-p2p-docs-prod') : 
  (process.env.AWS_BUCKET_NAME || 'vg-p2p-docs-staging')
};

class S3Service {
  private s3Client: S3Client;

  constructor() {
    this.s3Client = new S3Client({
      region: s3Config.region,
      credentials: s3Config.credentials
    });
  }

  /**
   * Generate a unique file key for S3
   */
  generateFileKey(filename: string): string {
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    const extension = path.extname(filename);
    const sanitizedName = path.basename(filename, extension)
      .replace(/[^a-z0-9]/gi, '-')
      .toLowerCase();
    
    return `${sanitizedName}-${timestamp}-${randomString}${extension}`;
  }

  /**
   * Upload a file to S3 from a multipart request
   */
  async uploadFile(request: FastifyRequest): Promise<{ fileKey: string; fileUrl: string; fileName: string; fileSize: number; contentType: string }> {
    let file; 
    try {
      
      // First, try to get the file directly from the body if attachFieldsToBody is enabled
      let fileData: any = null;
      let filename = '';
      let mimetype = '';
      let fileBuffer: Buffer | null = null;
      
      // Method 1: Check if the body has a file field (when attachFieldsToBody is true)
      if (request.body && typeof request.body === 'object') {
        // Check if we have a direct file field
        if ('file' in request.body) {
          const bodyFile = (request.body as any).file;
          
          // The file might be stored in an array-like object with numeric indices
          if (Array.isArray(bodyFile) || (typeof bodyFile === 'object' && Object.keys(bodyFile).some(k => !isNaN(Number(k))))) {
            console.log('File appears to be in an array-like structure');
            // Try to get the first item in the array-like object
            const fileEntry = Array.isArray(bodyFile) ? bodyFile[0] : bodyFile[0];
            if (fileEntry) {
              console.log('Array file entry keys:', Object.keys(fileEntry).join(', '));
              fileData = fileEntry;
              
              // Get filename, mimetype, and data from the file entry
              if ('filename' in fileEntry) filename = fileEntry.filename;
              if ('mimetype' in fileEntry) mimetype = fileEntry.mimetype;
              if ('data' in fileEntry) fileBuffer = fileEntry.data;
              
              if (fileBuffer) {
                console.log(`Using file from array structure, filename: ${filename}, buffer size: ${fileBuffer.length} bytes`);
              }
            }
          } else if (bodyFile && typeof bodyFile === 'object') {
            fileData = bodyFile;
            
            // Try to extract filename from various properties
            if ('filename' in bodyFile) {
              filename = bodyFile.filename as string;
            } else if ('fieldname' in bodyFile) {
              filename = bodyFile.fieldname as string;
            } else {
              filename = 'unknown-file';
            }
            
            // Try to extract mimetype
            if ('mimetype' in bodyFile) {
              mimetype = bodyFile.mimetype as string;
            } else if ('type' in bodyFile) {
              mimetype = bodyFile.type as string;
            } else {
              mimetype = 'application/octet-stream';
            }
            
            // Try to extract file data from various properties
            if ('data' in bodyFile && bodyFile.data) {
              fileBuffer = bodyFile.data as Buffer;
            } else if ('buffer' in bodyFile && bodyFile.buffer) {
              fileBuffer = bodyFile.buffer as Buffer;
            } else if ('value' in bodyFile && bodyFile.value) {
              // Some versions of @fastify/multipart package store the file in 'value'
              if (Buffer.isBuffer(bodyFile.value)) {
                fileBuffer = bodyFile.value as Buffer;
              } else if (typeof bodyFile.value === 'object' && 'buffer' in bodyFile.value) {
                fileBuffer = bodyFile.value.buffer as Buffer;
              }
            }
            
            if (fileBuffer) {
              console.log(`Using file from request body, filename: ${filename}, buffer size: ${fileBuffer.length} bytes`);
            }
          }
        }
        
        // If we didn't find a file, try looking for it at the root level
        // Some multipart implementations put files directly in the body with numeric indices
        if (!fileBuffer) {
          for (const key in request.body) {
            if (!isNaN(Number(key))) {
              const potentialFile = (request.body as any)[key];
              console.log(`Found potential file at index ${key}:`, typeof potentialFile);
              
              if (potentialFile && typeof potentialFile === 'object') {
                if ('filename' in potentialFile) filename = potentialFile.filename;
                if ('mimetype' in potentialFile) mimetype = potentialFile.mimetype;
                
                // Try different properties that might contain the file data
                if ('data' in potentialFile && potentialFile.data) {
                  fileBuffer = potentialFile.data;
                  fileData = potentialFile;
                } else if ('buffer' in potentialFile && potentialFile.buffer) {
                  fileBuffer = potentialFile.buffer;
                  fileData = potentialFile;
                } else if ('value' in potentialFile && potentialFile.value) {
                  if (Buffer.isBuffer(potentialFile.value)) {
                    fileBuffer = potentialFile.value;
                    fileData = potentialFile;
                  }
                }
                
                if (fileBuffer) {
                  console.log(`Using file from body index ${key}, filename: ${filename}, size: ${fileBuffer.length} bytes`);
                  break;
                }
              }
            }
          }
        }
      }
      
      // Method 2: Try the traditional file() method if we didn't get a file from the body
      if (!fileData) {
        try {
          console.log('Trying request.file() method...');
          const file = await request.file();
          if (file) {
            fileData = file;
            filename = file.filename;
            mimetype = file.mimetype;
            
            // Read the file into a buffer manually - works with all versions
            const chunks: Buffer[] = [];
            for await (const chunk of file.file) {
              chunks.push(chunk);
            }
            fileBuffer = Buffer.concat(chunks);
            console.log('Using file from request.file(), read using chunks');
          }
        } catch (fileError) {
          console.warn('Error using request.file():', fileError);
        }
      }
      
      // Method 3: Try parts() method as a last resort
      if (!fileData && typeof request.parts === 'function') {
        try {
          console.log('Trying request.parts() method...');
          const parts = request.parts();
          for await (const part of parts) {
            if (part.file) {
              fileData = part;
              filename = part.filename;
              mimetype = part.mimetype;
              
              // Read the file into a buffer manually - works with all versions
              const chunks: Buffer[] = [];
              for await (const chunk of part.file) {
                chunks.push(chunk);
              }
              fileBuffer = Buffer.concat(chunks);
              console.log('Using file from request.parts(), read using chunks');
              break;
            }
          }
        } catch (partsError) {
          console.warn('Error using request.parts():', partsError);
        }
      }
      
      // If we still don't have a file, throw an error
      if (!fileData || !fileBuffer) {
        throw new Error('No file uploaded');
      }
      
      const fileKey = this.generateFileKey(filename);

      // Upload to S3
      const command = new PutObjectCommand({
        Bucket: s3Config.bucketName,
        Key: fileKey,
        Body: fileBuffer,
        ContentType: mimetype
      });

      await this.s3Client.send(command);
      console.log('File uploaded to S3 successfully');
      
      const fileUrl = await this.getFileUrl(fileKey);
      console.log(`Generated signed URL: ${fileUrl.substring(0, 50)}...`);

      return {
        fileKey,
        fileUrl,
        fileName: filename,
        fileSize: fileBuffer.length,
        contentType: mimetype
      };
    } catch (error) {
      // More specific error diagnostics
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`Error uploading file to S3: ${errorMessage}, Request parts: ${typeof request.parts === 'function' ? 'available' : 'not available'}, File found: ${file ? 'yes' : 'no'}`);
      throw new Error(`Failed to upload file: ${errorMessage}`);
    }
  }

  /**
   * Upload multiple files to S3 from a multipart request
   */
  async uploadMultipleFiles(request: FastifyRequest, maxFiles: number = 5): Promise<Array<{ fileKey: string; fileUrl: string; fileName: string; fileSize: number; contentType: string }>> {
    try {

      const results: Array<{ fileKey: string; fileUrl: string; fileName: string; fileSize: number; contentType: string }> = [];
      let fileCount = 0;
      
      // First check if files are in the request body (with attachFieldsToBody)
      if (request.body && typeof request.body === 'object') {
        // Look for numeric indices (0, 1, 2, etc.) which often contain files
        const numericKeys = Object.keys(request.body).filter(key => !isNaN(Number(key)));
        console.log(`Found ${numericKeys.length} potential files with numeric keys:`, numericKeys.join(', '));
        
        // Process each numeric key that might be a file
        for (const key of numericKeys) {
          if (fileCount >= maxFiles) {
            console.warn(`Maximum file limit (${maxFiles}) reached, ignoring extra files`);
            break;
          }
          
          const potentialFile = (request.body as any)[key];
          if (potentialFile && typeof potentialFile === 'object') {
            console.log(`Processing file at index ${key}`);
            let filename = '';
            let mimetype = '';
            let fileBuffer: Buffer | null = null;
            
            // Extract file details
            if ('filename' in potentialFile) {
              filename = potentialFile.filename as string;
            } else if ('name' in potentialFile) {
              filename = potentialFile.name as string;
            } else {
              filename = `file-${key}`;
            }
            
            // Extract mimetype
            if ('mimetype' in potentialFile) {
              mimetype = potentialFile.mimetype as string;
            } else if ('type' in potentialFile) {
              mimetype = potentialFile.type as string;
            } else {
              mimetype = 'application/octet-stream';
            }
            
            // Try to find the file data from various possible properties
            if ('data' in potentialFile && potentialFile.data) {
              fileBuffer = potentialFile.data as Buffer;
            } else if ('buffer' in potentialFile && potentialFile.buffer) {
              fileBuffer = potentialFile.buffer as Buffer;
            } else if ('value' in potentialFile && potentialFile.value) {
              if (Buffer.isBuffer(potentialFile.value)) {
                fileBuffer = potentialFile.value;
              }
            }
            
            if (fileBuffer) {
              fileCount++;
              try {
                const fileKey = this.generateFileKey(filename);
                
                // Upload to S3
                const command = new PutObjectCommand({
                  Bucket: s3Config.bucketName,
                  Key: fileKey,
                  Body: fileBuffer,
                  ContentType: mimetype
                });
                
                await this.s3Client.send(command);
                const fileUrl = await this.getFileUrl(fileKey);
                
                results.push({
                  fileKey,
                  fileUrl,
                  fileName: filename,
                  fileSize: fileBuffer.length,
                  contentType: mimetype
                });
              } catch (error) {
                console.error(`Error processing file at index ${key}:`, error);
              }
            }
          }
        }
        
        // Also look for named fields like 'files', 'file[]', etc.
        const fileFields = ['files', 'file[]'];
        for (const field of fileFields) {
          if (field in request.body) {
            const filesArray = (request.body as any)[field];
            
            if (Array.isArray(filesArray)) {
              for (let i = 0; i < filesArray.length && fileCount < maxFiles; i++) {
                const file = filesArray[i];
                // Process the file similar to how we process numeric indices
                if (file && typeof file === 'object') {
                  let filename = '';
                  let mimetype = '';
                  let fileBuffer: Buffer | null = null;
                  
                  // Extract file details
                  if ('filename' in file) {
                    filename = file.filename as string;
                  } else if ('name' in file) {
                    filename = file.name as string;
                  } else {
                    filename = `file-${field}-${i}`;
                  }
                  
                  // Extract mimetype
                  if ('mimetype' in file) {
                    mimetype = file.mimetype as string;
                  } else if ('type' in file) {
                    mimetype = file.type as string;
                  } else {
                    mimetype = 'application/octet-stream';
                  }
                  
                  // Find the buffer
                  if ('data' in file && file.data) {
                    fileBuffer = file.data as Buffer;
                  } else if ('buffer' in file && file.buffer) {
                    fileBuffer = file.buffer as Buffer;
                  } else if ('value' in file && file.value) {
                    if (Buffer.isBuffer(file.value)) {
                      fileBuffer = file.value;
                    }
                  }
                  
                  if (fileBuffer) {
                    fileCount++;
                    try {
                      const fileKey = this.generateFileKey(filename);
                      
                      // Upload to S3
                      const command = new PutObjectCommand({
                        Bucket: s3Config.bucketName,
                        Key: fileKey,
                        Body: fileBuffer,
                        ContentType: mimetype
                      });
                      
                      await this.s3Client.send(command);
                      const fileUrl = await this.getFileUrl(fileKey);
                      
                      results.push({
                        fileKey,
                        fileUrl,
                        fileName: filename,
                        fileSize: fileBuffer.length,
                        contentType: mimetype
                      });
                    } catch (error) {
                      console.error(`Error processing file from ${field}[${i}]:`, error);
                    }
                  }
                }
              }
            }
          }
        }
      }
      
      // If we didn't find any files in the body, try using the streaming API
      if (results.length === 0) {
        console.log('No files found in request body, trying streaming API...');
        try {
          const parts = request.parts();
          
          // Process parts (files) from the multipart request
          for await (const part of parts) {
            // Skip if it's not a file or if we've hit the limit
            if (!part.file) continue;
            
            fileCount++;
            if (fileCount > maxFiles) {
              console.warn(`Maximum file limit (${maxFiles}) exceeded, ignoring extra files`);
              // Skip the rest but don't stop processing
              continue;
            }
            
            try {
              // Get the file data
              const file = part;
              // Read file data using chunks since toBuffer() might not be available
              const chunks: Buffer[] = [];
              for await (const chunk of file.file) {
                chunks.push(chunk);
              }
              const buffer = Buffer.concat(chunks);
              const fileKey = this.generateFileKey(file.filename);

              // Upload to S3
              const command = new PutObjectCommand({
                Bucket: s3Config.bucketName,
                Key: fileKey,
                Body: buffer,
                ContentType: file.mimetype
              });

              await this.s3Client.send(command);
              const fileUrl = await this.getFileUrl(fileKey);

              results.push({
                fileKey,
                fileUrl,
                fileName: file.filename,
                fileSize: buffer.length,
                contentType: file.mimetype
              });
              console.log(`Uploaded file ${file.filename} using streaming API, size: ${buffer.length} bytes`);
            } catch (error) {
              // If we hit an error with one file, log it but continue with other files
              console.error('Error processing a file using streaming API:', error);
            }
          }
        } catch (streamError) {
          console.warn('Error using streaming API for files:', streamError);
        }
      }
      
      if (results.length === 0) {
        throw new Error('No files uploaded successfully');
      }
      
      return results;
    } catch (error) {
      console.error('Error uploading files to S3:', error instanceof Error ? error.message : String(error));
      throw new Error(`Failed to upload files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload a buffer directly to S3
   */
  async uploadBufferToS3(filename: string, contentType: string, buffer: Buffer): Promise<{ fileKey: string; fileUrl: string; fileName: string; fileSize: number; contentType: string }> {
    try {
      const fileKey = this.generateFileKey(filename);

      const command = new PutObjectCommand({
        Bucket: s3Config.bucketName,
        Key: fileKey,
        Body: buffer,
        ContentType: contentType
      });

      await this.s3Client.send(command);
      const fileUrl = await this.getFileUrl(fileKey);

      return {
        fileKey,
        fileUrl,
        fileName: filename,
        fileSize: buffer.length,
        contentType
      };
    } catch (error) {
      console.error('Error uploading buffer to S3:', error instanceof Error ? error.message : String(error));
      throw new Error(`Failed to upload buffer: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload file from a filesystem path
   */
  async uploadFileFromPath(filePath: string, contentType: string): Promise<{ fileKey: string; fileUrl: string }> {
    try {
      const fileName = path.basename(filePath);
      const fileKey = this.generateFileKey(fileName);
      const fileStream = createReadStream(filePath);

      const command = new PutObjectCommand({
        Bucket: s3Config.bucketName,
        Key: fileKey,
        Body: fileStream,
        ContentType: contentType
      });

      await this.s3Client.send(command);
      const fileUrl = await this.getFileUrl(fileKey);

      return { fileKey, fileUrl };
    } catch (error) {
      console.error('Error uploading file to S3:', error);
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate a pre-signed URL for retrieving a file
   */
  async getFileUrl(fileKey: string, expiresIn: number = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: s3Config.bucketName,
        Key: fileKey
      });

      return await getSignedUrl(this.s3Client, command, { expiresIn });
    } catch (error) {
      console.error('Error generating pre-signed URL:', error);
      throw new Error(`Failed to generate file URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete a file from S3
   */
  async deleteFile(fileKey: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: s3Config.bucketName,
        Key: fileKey
      });

      await this.s3Client.send(command);
    } catch (error) {
      console.error('Error deleting file from S3:', error);
      throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

const s3Service = new S3Service();
export default s3Service;