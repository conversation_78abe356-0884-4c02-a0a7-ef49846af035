import 'reflect-metadata';
import { fastify } from 'fastify';
import { config } from './config';
import { registerPlugins } from './plugins';
import { registerRoutes } from './routes';
import { initializeDatabase } from './db/typeorm.config';
import { initializeTracing } from './tracing';
import { initSentry } from './utils/sentry';
import { setupGlobalErrorHandlers } from './utils/error-handler';

const Sentry = initSentry();

try {
  initializeTracing();
} catch (error) {
  console.error("Tracing initialization failed:", error);
}

// Create Fastify server with proper timeout configuration
const server = fastify({
  logger: {
    level: config.NODE_ENV === 'development' ? 'debug' : 'info'
  },
  // Set explicit timeouts for the server
  connectionTimeout: 60000, // 60 seconds connection timeout
  keepAliveTimeout: 65000, // 65 seconds keep-alive (must be > connectionTimeout)
  // Maximum number of pending requests
  maxRequestsPerSocket: 100
});

// Setup global error handlers first
setupGlobalErrorHandlers(server);

// Register plugins
registerPlugins(server);

// Register routes
registerRoutes(server);

// Add sentry error handler
Sentry.setupFastifyErrorHandler(server);

// Add a global 404 handler
server.setNotFoundHandler((request, reply) => {
  reply.status(404).send({
    error: true,
    code: 'ROUTE_NOT_FOUND',
    message: `Route ${request.method}:${request.url} not found`,
    requestId: request.id
  });
});

// Start the server
const start = async () => {
  try {
    // Initialize TypeORM database connection
    await initializeDatabase();
    
    await server.listen({ 
      port: config.PORT, 
      host: config.HOST 
    });
    
    console.log(`Server is running on ${config.HOST}:${config.PORT}`);
    
    // Log server configuration
    console.log(`Server timeout configuration:`);
    console.log(`- Connection timeout: 60s`);
    console.log(`- Request timeout: 60s`);
    console.log(`- Keep-alive timeout: 65s`);
  } catch (err) {
    server.log.error(err);
    process.exit(1);
  }
};

// Graceful shutdown handling
const gracefulShutdown = async (signal: string) => {
  console.log(`Received ${signal}. Shutting down gracefully...`);
  try {
    await server.close();
    console.log('Server closed successfully');
    process.exit(0);
  } catch (err) {
    console.error('Error during shutdown:', err);
    process.exit(1);
  }
};

// Listen for termination signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

start();
