import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../db/typeorm.config';
import { PurchaseOrder, PurchaseOrderStatus, PurchaseOrderType } from '../entities/PurchaseOrder.entity';
import { PoItem } from '../entities/PoItem.entity';
import { Approval } from '../entities/Approval.entity';
import { Quotation } from '../entities/Quotation.entity';
import { ApprovalRole, ApprovalStatus } from '../constants/enums';
import { User } from '../entities/User.entity';
import { In } from 'typeorm';

// Interface for request body
interface UpdateRejectedPOBody {
  quotationId: number;
  type?: PurchaseOrderType;
  vendorId: number;
  businessUnitId?: number;
  costCenterId?: number;
  paymentTerms?: string;
  deliveryTerms?: string;
  remarks?: string;
  totalAmount?: number;
  currency?: string;
  items: {
    itemCategoryId?: number;
    itemName: string;
    itemDescription?: string;
    quantity: number;
    uom: string;
    pricePerUnit: number;
    gstPercentage?: number;
    gstAmount?: number;
    totalValue?: number;
  }[];
}

/**
 * Update a rejected purchase order with a new quote and restart the approval workflow
 */
export const updateRejectedPO = async (
  request: FastifyRequest<{
    Params: { id: number },
    Body: UpdateRejectedPOBody
  }>,
  reply: FastifyReply
) => {
  // Start a transaction to ensure data consistency
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Ensure user is authenticated
    if (!request.user) {
      return reply.status(401).send({ error: true, message: 'Authentication required' });
    }

    const { id } = request.params;
    const {
      quotationId,
      type = PurchaseOrderType.ONE_TIME,
      vendorId,
      businessUnitId,
      costCenterId,
      paymentTerms,
      deliveryTerms,
      remarks,
      totalAmount,
      currency = 'INR',
      items
    } = request.body;
    const userId = (request.user as User).id;
    const userRoles = request.userRoles || [];

    // Check if user is authorized to update POs (only procurement managers)
    const isProcurementManager = userRoles.some((role: {role: string}) => role.role === ApprovalRole.PROCUREMENT_MANAGER);

    if (!isProcurementManager) {
      await queryRunner.rollbackTransaction();
      return reply.status(403).send({
        error: true,
        message: 'Only procurement managers can update purchase orders with new quotes'
      });
    }

    // Find the purchase order
    const purchaseOrder = await queryRunner.manager.findOne(PurchaseOrder, {
      where: { id },
      relations: ['items']
    });

    if (!purchaseOrder) {
      await queryRunner.rollbackTransaction();
      return reply.status(404).send({ error: true, message: 'Purchase order not found' });
    }

    // Verify PO is in REJECTED status
    if (purchaseOrder.status !== PurchaseOrderStatus.REJECTED) {
      await queryRunner.rollbackTransaction();
      return reply.status(400).send({
        error: true,
        message: 'Only rejected purchase orders can be updated with new quotes'
      });
    }

    // Check if quotation exists
    const quotation = await queryRunner.manager.findOne(Quotation, {
      where: { id: quotationId }
    });

    if (!quotation) {
      await queryRunner.rollbackTransaction();
      return reply.status(404).send({ error: true, message: 'Quotation not found' });
    }

    // Verify quotation is for the same PR as the PO
    if (quotation.prId !== purchaseOrder.prId) {
      await queryRunner.rollbackTransaction();
      return reply.status(400).send({
        error: true,
        message: 'Quotation must be for the same purchase request'
      });
    }

    // Update the purchase order
    purchaseOrder.quotationId = quotationId;
    purchaseOrder.type = type;
    purchaseOrder.status = PurchaseOrderStatus.PENDING_APPROVAL;
    purchaseOrder.vendorId = vendorId;

    // Only update these fields if provided
    if (businessUnitId) purchaseOrder.businessUnitId = businessUnitId;
    if (costCenterId) purchaseOrder.costCenterId = costCenterId;
    if (paymentTerms) purchaseOrder.paymentTerms = paymentTerms;
    if (deliveryTerms) purchaseOrder.deliveryTerms = deliveryTerms;
    if (remarks) purchaseOrder.remarks = remarks;
    if (currency) purchaseOrder.currency = currency;

    // First save base purchase order to get updated ID
    const savedPO = await queryRunner.manager.save(purchaseOrder);

    // Make sure we have the ID from the saved PO
    const poId = savedPO.id;

    if (!poId) {
      await queryRunner.rollbackTransaction();
      return reply.status(500).send({
        error: true,
        message: 'Failed to update purchase order - could not get PO ID'
      });
    }

    // Calculate total from the PO items if not provided
    let calculatedTotal = 0;

    // Use direct SQL query to delete existing PO items to bypass TypeORM relationship management
    await queryRunner.query(`DELETE FROM po_items WHERE po_id = ?`, [poId]);

    // Clear items array to ensure TypeORM doesn't try to manage the relationships
    if (purchaseOrder.items) {
      // Use an empty array instead of undefined to satisfy TypeScript
      purchaseOrder.items = [];
    }

    // Save the PO with empty items array before creating new items
    await queryRunner.manager.save(purchaseOrder, { reload: false });

    // Create new PO items
    if (items && items.length > 0) {
      const poItemRepo = queryRunner.manager.getRepository(PoItem);
      console.log(`Creating ${items.length} new PO items for PO ID: ${poId}`);

      // Double check the PO ID exists before proceeding
      if (!poId) {
        await queryRunner.rollbackTransaction();
        return reply.status(500).send({
          error: true,
          message: 'Cannot create PO items without a valid PO ID'
        });
      }

      console.log('Creating PO items with poId:', poId);

      // Create array of PO item entities
      const poItems: PoItem[] = [];

      // Loop through each item manually to ensure poId is set
      for (const itemData of items) {
        const poItem = new PoItem();

        // Explicitly set the poId - this is critical
        poItem.poId = poId;

        // Set other item properties
        // Only set itemCategoryId if it's provided
        if (itemData.itemCategoryId !== undefined) {
          poItem.itemCategoryId = itemData.itemCategoryId;
        }
        poItem.itemName = itemData.itemName;
        poItem.itemDescription = itemData.itemDescription || '';
        poItem.quantity = itemData.quantity;
        poItem.uom = itemData.uom || 'Nos';
        poItem.pricePerUnit = itemData.pricePerUnit;

        // Handle GST calculation
        const gstPercentage = itemData.gstPercentage !== undefined ? itemData.gstPercentage : 18;
        poItem.gstPercentage = gstPercentage;

        const priceBeforeTax = poItem.pricePerUnit * poItem.quantity;
        const gstAmount = (priceBeforeTax * gstPercentage) / 100;
        poItem.gstAmount = itemData.gstAmount !== undefined ? itemData.gstAmount : gstAmount;

        // Calculate total value if not provided
        poItem.totalValue = itemData.totalValue !== undefined ?
          itemData.totalValue : priceBeforeTax + poItem.gstAmount;

        calculatedTotal += poItem.totalValue;

        // Add the configured item to our array
        poItems.push(poItem);
      }

      try {
        // Save all items at once
        await queryRunner.manager.save(poItems);
      } catch (error) {
        console.error('Error saving PO items:', error);

        // If batch save fails, try saving items one by one to identify the problematic item
        for (let i = 0; i < poItems.length; i++) {
          try {
            // Double check poId is set right before saving
            await queryRunner.manager.save(poItems[i]);
          } catch (itemError) {
            console.error(`Error saving item ${i+1}:`, itemError, poItems[i]);
            throw itemError; // Re-throw to trigger transaction rollback
          }
        }
      }
    }

    // Update the total amount
    purchaseOrder.totalAmount = totalAmount !== undefined ? totalAmount : calculatedTotal;
    await queryRunner.manager.save(purchaseOrder);

    // Find the latest approval sequence for this PR
    const latestApproval = await queryRunner.manager
      .createQueryBuilder(Approval, 'approval')
      .where('approval.requestId = :prId', { prId: purchaseOrder.prId })
      .andWhere('approval.requestType = :requestType', { requestType: 'PR_APPROVAL' })
      .orderBy('approval.approvalSequence', 'DESC')
      .getOne();

    let nextSequence = 1;
    if (latestApproval) {
      nextSequence = (latestApproval.approvalSequence || 0) + 1;
    }

    // Check for existing approvals for this PR with QUOTE_APPROVAL type
    const existingApprovals = await queryRunner.manager.find(Approval, {
      where: {
        requestId: purchaseOrder.prId,
        requestType: 'PR_APPROVAL',
        approvalType: 'QUOTE_APPROVAL'
      }
    });

    // If approvals already exist, update them to PENDING_APPROVAL
    if (existingApprovals.length > 0) {
      // Update all existing approval statuses
      for (const approval of existingApprovals) {
        approval.status = ApprovalStatus.PENDING_APPROVAL;
        approval.remarks = 'Reopened after PO update with new quote';
        // Use 0 as placeholder for no verification (matches entity definition)
        approval.verifiedBy = 0;
        // Use current date as a placeholder since we can't set to null/undefined
        approval.verifiedAt = new Date();
      }
      await queryRunner.manager.save(existingApprovals);
    } else {
      // Create new approvals if none exist
      // Create BizFin approval record
      const bizFinApproval = new Approval();
      bizFinApproval.requestId = purchaseOrder.prId;
      bizFinApproval.requestType = 'PR_APPROVAL';
      bizFinApproval.approvalType = 'QUOTE_APPROVAL';
      bizFinApproval.approvalRole = 'biz_fin';
      bizFinApproval.approvalSequence = nextSequence;
      bizFinApproval.status = 'PENDING_APPROVAL';

      await queryRunner.manager.save(bizFinApproval);

      // Create CC_HEAD approval record
      const ccHeadApproval = new Approval();
      ccHeadApproval.requestId = purchaseOrder.prId;
      ccHeadApproval.requestType = 'PR_APPROVAL';
      ccHeadApproval.approvalType = 'QUOTE_APPROVAL';
      ccHeadApproval.approvalRole = 'cc_head';
      ccHeadApproval.approvalSequence = nextSequence + 1;
      ccHeadApproval.status = 'PENDING_APPROVAL';

      await queryRunner.manager.save(ccHeadApproval);
    }

    // Commit transaction
    await queryRunner.commitTransaction();

    return reply.status(200).send({
      message: 'Purchase order updated with new quote and sent for approval',
      purchaseOrder: {
        id: purchaseOrder.id,
        poNumber: purchaseOrder.poNumber,
        status: purchaseOrder.status,
        quotationId: purchaseOrder.quotationId,
        updatedAt: purchaseOrder.updatedAt,
        totalAmount: purchaseOrder.totalAmount
      }
    });
  } catch (error: any) {
    // Rollback transaction on error
    await queryRunner.rollbackTransaction();
    console.error('Error updating purchase order with new quote:', error);
    return reply.status(500).send({
      error: true,
      message: error.message || 'Internal server error'
    });
  } finally {
    // Release query runner
    await queryRunner.release();
  }
};