import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../db/typeorm.config';
import { Approval } from '../entities/Approval.entity';
import { PurchaseRequest } from '../entities/PurchaseRequest.entity';
import { PurchaseOrderStatus } from '../entities/PurchaseOrder.entity';
import { ApprovalRole, ApprovalStatus, ApprovalType, EmailRequestType, PurchaseRequestStatus, RequestType } from '../constants/enums';
import { User } from '../entities/User.entity';
import { UserRole } from '../entities/UserRole.entity';
import { ApprovalNotificationHandler } from '../services/approval-notification.handler';
import { notifyNextApprovers } from '../utils/next-approval-email';

// Interface for approval action request body
interface ApprovalActionBody {
  status: ApprovalStatus;
  remarks?: string;
}

// Interface for approval action request params
interface ApprovalActionParams {
  requestId: number;
}

/**
 * Process approval for a specific role
 * @param role The approval role (CC_HEAD, BIZ_FIN, PROCUREMENT_MANAGER)
 * @param request The FastifyRequest object
 * @param reply The FastifyReply object
 */
const processApproval = async (
  role: ApprovalRole,
  request: FastifyRequest<{
    Body: ApprovalActionBody;
    Params: ApprovalActionParams;
  }>,
  reply: FastifyReply
) => {
  const { requestId } = request.params;
  const { status, remarks } = request.body;
  const user = request.user as User;

  // Start a transaction
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Check if the user has the required role
    const userRoleRepo = queryRunner.manager.getRepository(UserRole);
    const userRoles = await userRoleRepo.find({
      where: { userId: user.id }
    });

    const hasRequiredRole = userRoles.some((userRole: UserRole) => userRole.role === role);
    if (!hasRequiredRole) {
      await queryRunner.rollbackTransaction();
      return reply.status(403).send({
        error: true,
        message: `You don't have the ${role} role required to perform this action`
      });
    }

    // Get the purchase request with FOR UPDATE lock to prevent race conditions
    const purchaseRequest = await queryRunner.manager
      .createQueryBuilder(PurchaseRequest, 'pr')
      .setLock('pessimistic_write')
      .where('pr.id = :id', { id: requestId })
      .getOne();

    if (!purchaseRequest) {
      await queryRunner.rollbackTransaction();
      return reply.status(404).send({
        error: true,
        message: 'Purchase request not found'
      });
    }

    // Get the approval entry for this role with FOR UPDATE lock
    const approval = await queryRunner.manager
      .createQueryBuilder(Approval, 'approval')
      .setLock('pessimistic_write')
      .where('approval.requestId = :requestId', { requestId })
      .andWhere('approval.requestType = :requestType', { requestType: RequestType.PURCHASE_REQUEST })
      .andWhere('approval.approvalType = :approvalType', { approvalType: ApprovalType.PR_APPROVAL })
      .andWhere('approval.approvalSequence = :sequence', { 
        sequence: role === ApprovalRole.CC_HEAD ? 1 : role === ApprovalRole.BIZ_FIN ? 2 : 3 
      })
      .getOne();

    if (!approval) {
      await queryRunner.rollbackTransaction();
      return reply.status(404).send({
        error: true,
        message: `No approval entry found for ${role} role`
      });
    }

    // Check if approval is already processed to prevent duplicate processing
    if (approval.status !== ApprovalStatus.PENDING_APPROVAL) {
      await queryRunner.rollbackTransaction();
      return reply.status(400).send({
        error: true,
        message: `This purchase request has already been ${approval.status.toLowerCase()} by ${role}`
      });
    }

    // Check if this approval is in the correct sequence
    // For CC_HEAD (sequence 1), it's always the first approval
    // For BIZ_FIN (sequence 2), CC_HEAD must be approved
    // For PROCUREMENT_MANAGER (sequence 3), BIZ_FIN must be approved
    if (role !== ApprovalRole.CC_HEAD) {
      const previousApprovals = await queryRunner.manager
        .createQueryBuilder(Approval, 'approval')
        .where('approval.requestId = :requestId', { requestId })
        .andWhere('approval.requestType = :requestType', { requestType: RequestType.PURCHASE_REQUEST })
        .andWhere('approval.approvalType = :approvalType', { approvalType: ApprovalType.PR_APPROVAL })
        .andWhere('approval.approvalSequence = :sequence', { 
          sequence: role === ApprovalRole.BIZ_FIN ? 1 : 2 
        })
        .getMany();

      const allPreviousApproved = previousApprovals.every(
        (prevApproval: Approval) => prevApproval.status === ApprovalStatus.APPROVED
      );

      if (!allPreviousApproved) {
        await queryRunner.rollbackTransaction();
        return reply.status(400).send({
          error: true,
          message: `Previous approvals must be completed before ${role} can approve/reject`
        });
      }
    }

    // If the status is REJECTED, update all subsequent approvals to NOT_INITIATED
    if (status === ApprovalStatus.REJECTED) {
      // Update the purchase request status to REJECTED
      purchaseRequest.status = PurchaseRequestStatus.REJECTED;
      await queryRunner.manager.save(purchaseRequest);

      // Update all subsequent approvals to NOT_INITIATED
      const subsequentApprovals = await queryRunner.manager
        .createQueryBuilder(Approval, 'approval')
        .where('approval.requestId = :requestId', { requestId })
        .andWhere('approval.requestType = :requestType', { requestType: RequestType.PURCHASE_REQUEST })
        .andWhere('approval.approvalType = :approvalType', { approvalType: ApprovalType.PR_APPROVAL })
        .andWhere('approval.approvalSequence > :sequence', { 
          sequence: role === ApprovalRole.CC_HEAD ? 
            approval.approvalSequence : 
            role === ApprovalRole.BIZ_FIN ? 
              approval.approvalSequence : 
              approval.approvalSequence - 1
        })
        .getMany();

      for (const subApproval of subsequentApprovals) {
        subApproval.status = ApprovalStatus.PENDING_APPROVAL;
        await queryRunner.manager.save(subApproval);
      }
    } else if (status === ApprovalStatus.APPROVED) {
      // If this is the last approval (PROCUREMENT_MANAGER), update the PR status to APPROVED
      if (role === ApprovalRole.PROCUREMENT_MANAGER) {
        purchaseRequest.status = PurchaseRequestStatus.APPROVED;
        await queryRunner.manager.save(purchaseRequest);
      }
    }

    // Update the current approval
    approval.status = status;
    approval.remarks = remarks || ''; // Ensure remarks is not undefined
    approval.verifiedBy = user.id;
    approval.verifiedAt = new Date();
    
    await queryRunner.manager.save(approval);

    // Store data needed for notifications after transaction
    const approvalData = {
      requestId,
      status,
      role,
      remarks,
      approvalSequence: approval.approvalSequence,
      costCenterId: purchaseRequest.costCenterId
    };

    // Get the creator of the purchase request
    const creator = await queryRunner.manager.findOne(User, {
      where: { id: purchaseRequest.createdBy },
      relations: ['roles']
    });

    // Commit the transaction first
    await queryRunner.commitTransaction();

    // Send notifications outside of transaction to reduce overhead
    try {
      // Send notification to creator
      if (creator) {
        // Use non-blocking notification to avoid waiting for email to be sent
        ApprovalNotificationHandler.handlePurchaseRequestApproval(
          approvalData.requestId,
          creator,
          approvalData.status,
          approvalData.role as ApprovalRole,
          approvalData.remarks,
          EmailRequestType.PURCHASE_REQUEST
        ).catch(err => console.error(`Error sending creator notification: ${err.message}`));
      } else {
        console.error(`Creator not found for purchase request ${approvalData.requestId}`);
      }

      // Send notification to next approvers if approved
      if (approvalData.status === ApprovalStatus.APPROVED) {
        notifyNextApprovers({
          requestId: approvalData.requestId,
          requestType: RequestType.PURCHASE_REQUEST,
          approvalType: ApprovalType.PR_APPROVAL,
          currentApprovalSequence: approvalData.approvalSequence + 1,
          status: ApprovalStatus.PENDING_APPROVAL,
          remarks: '',
          emailRequestType: EmailRequestType.PURCHASE_REQUEST,
          costCenterId: approvalData.costCenterId
        }).catch(err => console.error(`Error notifying next approvers: ${err.message}`));
      }
    } catch (notificationError) {
      // Log notification errors but don't fail the request
      console.error(`Notification error for purchase request ${requestId}:`, notificationError);
    }

    return reply.status(200).send({
      message: `Purchase request ${status === ApprovalStatus.APPROVED ? 'approved' : 'rejected'} successfully`,
      approval
    });
  } catch (error) {
    // Rollback the transaction in case of error
    await queryRunner.rollbackTransaction();
    console.error(`Error processing ${role} approval:`, error);
    return reply.status(500).send({
      error: true,
      message: 'Internal server error'
    });
  } finally {
    // Release the query runner
    await queryRunner.release();
  }
};

/**
 * Process CC_HEAD approval
 */
export const processCCHeadApproval = async (
  request: FastifyRequest<{
    Body: ApprovalActionBody;
    Params: ApprovalActionParams;
  }>,
  reply: FastifyReply
) => {
  return processApproval(ApprovalRole.CC_HEAD, request, reply);
};

/**
 * Process BIZ_FIN approval
 */
export const processBizFinApproval = async (
  request: FastifyRequest<{
    Body: ApprovalActionBody;
    Params: ApprovalActionParams;
  }>,
  reply: FastifyReply
) => {
  return processApproval(ApprovalRole.BIZ_FIN, request, reply);
};

/**
 * Process PROCUREMENT_MANAGER approval
 */
export const processProcurementManagerApproval = async (
  request: FastifyRequest<{
    Body: ApprovalActionBody;
    Params: ApprovalActionParams;
  }>,
  reply: FastifyReply
) => {
  return processApproval(ApprovalRole.PROCUREMENT_MANAGER, request, reply);
};

/**
 * Get all approvals for a purchase request
 */
export const getPurchaseRequestApprovals = async (
  request: FastifyRequest<{
    Params: ApprovalActionParams;
  }>,
  reply: FastifyReply
) => {
  const { requestId } = request.params;

  try {
    // Get the purchase request
    const prRepo = AppDataSource.getRepository(PurchaseRequest);
    const purchaseRequest = await prRepo.findOne({
      where: { id: requestId }
    });

    if (!purchaseRequest) {
      return reply.status(404).send({
        error: true,
        message: 'Purchase request not found'
      });
    }

    // Get all approvals for this purchase request
    const approvalRepo = AppDataSource.getRepository(Approval);
    const approvals = await approvalRepo.find({
      where: {
        requestId,
        requestType: RequestType.PURCHASE_REQUEST,
        approvalType: ApprovalType.PR_APPROVAL
      },
      order: {
        approvalSequence: 'ASC'
      }
    });

    return reply.status(200).send(approvals);
  } catch (error) {
    console.error('Error getting purchase request approvals:', error);
    return reply.status(500).send({
      error: true,
      message: 'Internal server error'
    });
  }
};
