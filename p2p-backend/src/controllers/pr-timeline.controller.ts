import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../db/typeorm.config';
import { PurchaseRequest } from '../entities/PurchaseRequest.entity';
import { Approval } from '../entities/Approval.entity';
import { User } from '../entities/User.entity';
import { ApprovalType, ApprovalStatus, RequestType } from '../constants/enums';
import { getRoleDisplayName } from '../utils/roleMapping';
import { Undefined } from '@sinclair/typebox';
import { InvoiceStatus } from '../entities/Invoice.entity';

const statusMapping: Record<string, string> = {
  [ApprovalStatus.PENDING_APPROVAL]: 'Pending Approval',
  [ApprovalStatus.APPROVED]: 'Approved',
  [ApprovalStatus.REJECTED]: 'Rejected',
  [ApprovalStatus.ON_HOLD]: 'On Hold',
  [ApprovalStatus.SKIPPED]: 'Skipped'
};

// Interface for timeline event
interface TimelineEvent {
  eventType: string;
  actioner: {
    id: number;
    username?: string;
    firstName?: string;
    lastName?: string;
    name?: string;
  } | null;
  actionedAt?: Date | undefined;
  remarks?: string | null;
  status?: string;
  role?: string;
  invoiceId?: number;
  invoiceNumber?: string;
}

/**
 * Get the complete timeline for a purchase request
 */
export const getPurchaseRequestTimeline = async (
  request: FastifyRequest<{ Params: { id: string } }> & { user?: User },
  reply: FastifyReply
) => {
  try {
    // Ensure user is authenticated
    if (!request.user) {
      return reply.status(401).send({ message: 'Authentication required' });
    }

    const id = parseInt(request.params.id, 10);

    // Get the purchase request
    const purchaseRequest = await AppDataSource
      .getRepository(PurchaseRequest)
      .createQueryBuilder('pr')
      .where('pr.id = :id', { id })
      .getOne();

    if (!purchaseRequest) {
      return reply.status(404).send({ message: 'Purchase request not found' });
    }

    // Get all users for lookup
    const users = await AppDataSource.getRepository(User).find();
    const userMap: Record<number, User> = {};
    users.forEach(user => {
      userMap[user.id] = user;
    });

    const creator = userMap[purchaseRequest.createdBy] || null;

    // Create initial timeline event for PR creation
    const timeline: TimelineEvent[] = [
      {
        eventType: 'Purchase Request Created',
        actioner: creator ? {
          id: creator.id,
          username: creator.username,
          firstName: creator.firstName,
          lastName: creator.lastName
        } : null,
        actionedAt: purchaseRequest.createdAt,
        status: purchaseRequest.status
      }
    ];

    // Get all approvals for the purchase request
    const approvals = await AppDataSource
      .getRepository(Approval)
      .createQueryBuilder('approval')
      .where('approval.requestId = :requestId', { requestId: id })
      // .andWhere('approval.requestType = :requestType', { requestType: RequestType.PURCHASE_REQUEST })
      .orderBy('approval.approvalSequence', 'ASC')
      .addOrderBy('approval.updatedAt', 'ASC')
      .getMany();

    // Add approval events to timeline
    let procurementManagerApproval = null;

    // --- INVOICE APPROVAL HISTORY APPEND ---
    // 1. Find all POs for this PR
    const purchaseOrders = await AppDataSource.getRepository('PurchaseOrder')
      .createQueryBuilder('po')
      .where('po.prId = :prId', { prId: id })
      .getMany();

    // Add each approval to the timeline
    for (const approval of approvals.filter((approval: Approval) => approval.approvalType === ApprovalType.PR_APPROVAL)) {
      const approver = approval.verifiedBy ? userMap[approval.verifiedBy] : null;

      const timelineEvent: TimelineEvent = {
        eventType: `${getRoleDisplayName(approval.approvalRole)} Approval`,
        actioner: approver ? {
          id: approver.id,
          username: approver.username,
          firstName: approver.firstName,
          lastName: approver.lastName
        } : null,
        actionedAt: approval.verifiedAt,
        remarks: approval.remarks || null,
        status: approval.status,
        role: approval.approvalType
      };

      timeline.push(timelineEvent);

      // Store procurement manager approval for later use
      if (approval.approvalRole === 'procurement_manager' &&
          approval.status === ApprovalStatus.APPROVED) {
        procurementManagerApproval = approval;
      }
    }

    // Add QUOTATION_STARTED event if procurement manager has approved
    if (procurementManagerApproval) {
      const procurementManager = procurementManagerApproval.verifiedBy ?
        userMap[procurementManagerApproval.verifiedBy] : null;

      timeline.push({
        eventType: 'Quotation Process',
        actioner: null,
        actionedAt: undefined,
        remarks: null,
        status: 'INITIATED'
      });
    }
    // IF THERE IS A PO EXISTING IN purchase_orders table THEN ADD A TIMELINE ENTRY FOR PO CREATED
    // const existingPo = await AppDataSource
    //   .getRepository(PurchaseOrder)
    //   .createQueryBuilder('po')
    //   .where('po.purchaseRequestId = :id', { id })
    //   .getOne();

    // if (existingPo) {
    //   const poCreator = userMap[existingPo.createdBy] || null;
    //   timeline.push({
    //     eventType: 'Purchase Order Created',
    //     actioner: poCreator ? {
    //       id: poCreator.id,
    //       username: poCreator.username,
    //       firstName: poCreator.firstName,
    //       lastName: poCreator.lastName
    //     } : null,
    //     actionedAt: existingPo.createdAt,
    //     remarks: 'NA',
    //     status: 'CREATED'
    //   });
    // }

    for (const approval of approvals.filter((approval: Approval) => approval.approvalType === ApprovalType.QUOTE_APPROVAL)) {
      const approver = approval.verifiedBy ? userMap[approval.verifiedBy] : null;

      const timelineEvent: TimelineEvent = {
        eventType: `Quotation ${statusMapping[approval.status] || approval.status} by ${approver?.firstName || ''} ${approver?.lastName || ''} (${approval.approvalRole})`,
        actioner: approver ? {
          id: approver.id,
          username: approver.username,
          firstName: approver.firstName,
          lastName: approver.lastName
        } : null,
        actionedAt: approval.verifiedAt,
        remarks: approval.remarks || null,
        status: approval.status,
        role: approval.approvalType
      };

      timeline.push(timelineEvent);
    }

    // 2. For each PO, find all GRNs
    for (const po of purchaseOrders) {
      const grns = await AppDataSource.getRepository('Grn')
        .createQueryBuilder('grn')
        .where('grn.poId = :poId', { poId: po.id })
        .getMany();
      // 3. For each GRN, find all Invoices
      for (const grn of grns) {
        const invoices = await AppDataSource.getRepository('Invoice')
          .createQueryBuilder('invoice')
          .where('invoice.grnId = :grnId', { grnId: grn.id })
          .andWhere('invoice.status != :voidStatus', { voidStatus: InvoiceStatus.REJECTED })
          .getMany();
        // 4. For each Invoice, fetch all approvals
        for (const invoice of invoices) {
          const invoiceApprovals = await AppDataSource.getRepository('Approval')
            .createQueryBuilder('approval')
            .where('approval.requestId = :invoiceId', { invoiceId: invoice.id })
            .andWhere('approval.requestType = :requestType', { requestType: RequestType.INVOICE })
            .orderBy('approval.approvalSequence', 'ASC')
            .addOrderBy('approval.updatedAt', 'ASC')
            .getMany();
          // 5. Add each approval as a timeline event
          for (const approval of invoiceApprovals) {
            const approver = approval.verifiedBy ? userMap[approval.verifiedBy] : null;
            timeline.push({
              eventType: `Invoice Approval (${invoice.invoiceNumber || invoice.id}) - ${getRoleDisplayName(approval.approvalRole)}`,
              actioner: approver ? {
                id: approver.id,
                username: approver.username,
                firstName: approver.firstName,
                lastName: approver.lastName
              } : null,
              actionedAt: approval.verifiedAt,
              remarks: approval.remarks || null,
              status: approval.status,
              role: approval.approvalType,
              invoiceId: invoice.id,
              invoiceNumber: invoice.invoiceNumber
            });
          }
        }
      }
    }
    // --- END INVOICE APPROVAL HISTORY APPEND ---


    return reply.status(200).send({
      purchaseRequestId: id,
      timeline
    });
  } catch (error) {
    console.error('Error fetching purchase request timeline:', error);
    return reply.status(500).send({ message: 'Internal server error' });
  }
};
