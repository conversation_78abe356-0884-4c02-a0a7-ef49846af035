import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../db/typeorm.config';
import { PurchaseRequest } from '../entities/PurchaseRequest.entity';
import { PrItem } from '../entities/PrItem.entity';
import { Approval } from '../entities/Approval.entity';
import { UserRole } from '../entities/UserRole.entity';
import { User } from '../entities/User.entity';
import { BusinessUnit } from '../entities/BusinessUnit.entity';
import { CostCenter } from '../entities/CostCenter.entity';
import { ItemCategory } from '../entities/ItemCategory.entity';
import { PurchaseOrder } from '../entities/PurchaseOrder.entity';
import { Document } from '../entities/document.entity';
import { In } from 'typeorm';
import { 
  ApprovalRole, 
  ApprovalType, 
  ApprovalStatus, 
  RequestType, 
  PurchaseRequestStatus,
  EmailRequestType
} from '../constants/enums';
import { EntityType } from '../schemas/document.schema';
import { documentService } from '../services/document.service';
import { ApprovalNotificationHandler } from '../services/approval-notification.handler';
import { notifyNextApprovers } from '../utils/next-approval-email';

// Interface for enriched purchase request
interface EnrichedPurchaseRequest extends PurchaseRequest {
  totalValue: number;
  businessUnit: BusinessUnit | null;
  costCenter: CostCenter | null;
  createdByUser: User | null;
}

// Type definitions for request body
interface CreatePurchaseRequestBody {
  prType: RequestType;
  costCenterId?: number;
  businessUnitId?: number;
  remarks?: string;
  createdBy: number;
  items: {
    itemCategoryId: number;
    itemName: string;
    itemDescription?: string;
    quantity: number;
    uom: string;
    estimatedPricePerQuantity: number;
  }[];
  // File attachment options - similar to quotation implementation
  fileKey?: string;       // For single file attachment (backward compatibility)
  fileKeys?: string[];    // For multiple file attachments
}

// Update an existing purchase request (status, etc.)
const updatePurchaseRequest = async (
  request: FastifyRequest<{ Params: { id: number }, Body: { status?: string } }> & { user?: User },
  reply: FastifyReply
) => {
  try {
    if (!request.user) {
      return reply.status(401).send({ message: 'Authentication required' });
    }

    const { id } = request.params;
    const { status } = request.body;
    const repo = AppDataSource.getRepository(PurchaseRequest);
    const purchaseRequest = await repo.findOne({ where: { id }, relations: ['items'] });
    
    if (!purchaseRequest) {
      return reply.status(404).send({ message: 'Purchase request not found' });
    }

    // Validate - Only the user who created the purchase request can update it
    if (purchaseRequest.createdBy !== request.user.id) {
      return reply.status(403).send({ message: 'You are not authorized to update this purchase request' });
    }
    
    const validTransitions = [
      { from: PurchaseRequestStatus.PENDING_APPROVAL, to: PurchaseRequestStatus.VOID },
      { from: PurchaseRequestStatus.VOID, to: PurchaseRequestStatus.PENDING_APPROVAL }
    ];

    // Validate status transition
    if (status && status !== purchaseRequest.status) {
      const isValid = validTransitions.some(
        t => t.from === purchaseRequest.status && t.to === status
      );
      if (!isValid) {
        return reply.status(400).send({ message: `Invalid status transition: ${purchaseRequest.status} -> ${status}` });
      }
      purchaseRequest.status = status;
    }

    await repo.save(purchaseRequest);
    return reply.status(200).send({ message: 'Purchase request updated', purchaseRequest });
  } catch (error) {
    console.error('Error updating purchase request:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};

// Reject a purchase request
const rejectPurchaseRequest = async (
  request: FastifyRequest<{ Params: { id: number }, Body: { status?: string, remarks?: string } }> & { user?: User },
  reply: FastifyReply
) => {
  try {
    const { id } = request.params;
    const { status, remarks } = request.body;
    const repo = AppDataSource.getRepository(PurchaseRequest);
    const purchaseRequest = await repo.findOne({ where: { id }, relations: ['items'] });
    
    if (!purchaseRequest) {
      return reply.status(404).send({ message: 'Purchase request not found' });
    }

    purchaseRequest.status = status || PurchaseRequestStatus.REJECTED;
    purchaseRequest.remarks = remarks || '';
    
    await repo.save(purchaseRequest);
    return reply.status(200).send({ message: 'Purchase request rejected', purchaseRequest });
  } catch (error) {
    console.error('Error rejecting purchase request:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};

// Create a new purchase request
const createPurchaseRequest = async (
  request: FastifyRequest<{ Body: CreatePurchaseRequestBody }> & { user?: User, userRoles?: UserRole[] },
  reply: FastifyReply
) => {
  // Start a transaction
  const queryRunner = AppDataSource.createQueryRunner();
  
  try {
    await queryRunner.connect();
    await queryRunner.startTransaction();

    // Ensure user is authenticated
    if (!request.user) {
      return reply.status(401).send({ message: 'Authentication required' });
    }

    const { 
      prType, 
      costCenterId, 
      businessUnitId, 
      remarks, 
      items,
      fileKey,
      fileKeys
    } = request.body as CreatePurchaseRequestBody;
    
    // Use the authenticated user's ID from the token
    const createdBy = request.user?.id;
    
    if (!createdBy) {
      return reply.status(401).send({ message: 'User not authenticated' });
    }

    // Create the purchase request
    const purchaseRequestRepo = queryRunner.manager.getRepository(PurchaseRequest);
    
    // Only include non-null foreign keys to avoid constraint violations
    const purchaseRequestData: any = {
      prType,
      remarks,
      createdBy
    };
    
    // Only add foreign keys if they have values
    if (costCenterId) {
      purchaseRequestData.costCenterId = costCenterId;
    }
    
    if (businessUnitId) {
      purchaseRequestData.businessUnitId = businessUnitId;
    }

    purchaseRequestData.status = ApprovalStatus.PENDING_APPROVAL;
    
    const newPurchaseRequest = purchaseRequestRepo.create(purchaseRequestData);

    // Save the purchase request
    // TypeORM's save method returns an array when saving an array and a single entity when saving a single entity
    // Force TypeScript to treat it as a single entity
    const savedPurchaseRequest = await queryRunner.manager.save(newPurchaseRequest);
    const prId = (savedPurchaseRequest as any).id;

    // Create and save the PR items
    if (items && items.length > 0) {
      const prItemRepo = queryRunner.manager.getRepository(PrItem);
      
      const prItems = items.map((item: any) => {
        return prItemRepo.create({
          prId: prId,
          itemCategoryId: item.itemCategoryId,
          itemName: item.itemName,
          itemDescription: item.itemDescription,
          quantity: item.quantity,
          uom: item.uom,
          estimatedPricePerQuantity: item.estimatedPricePerQuantity
        });
      });

      await queryRunner.manager.save(prItems);
    }
    
    // Handle file attachments if provided
    let allFileKeys: string[] = [];
    
    // Process fileKey (string) - single file for backward compatibility
    if (fileKey && typeof fileKey === 'string') {
      allFileKeys.push(fileKey);
    }
    
    // Process fileKeys (array) - for multiple files
    if (fileKeys && Array.isArray(fileKeys)) {
      allFileKeys = [...allFileKeys, ...fileKeys];
    }
    
    // Remove duplicates if any
    allFileKeys = [...new Set(allFileKeys)];
    
    // Create document records for each file key
    if (allFileKeys.length > 0) {
      // First, check if these file keys exist in S3 and get metadata
      
      try {
        // Find existing document records to get file metadata
        const existingDocuments = await AppDataSource.getRepository(Document)
          .createQueryBuilder('doc')
          .where('doc.fileKey IN (:...fileKeys)', { fileKeys: allFileKeys })
          .getMany();
        
        const documentMap: Record<string, { fileName: string; fileSize: number; contentType: string }> = {};
        existingDocuments.forEach(doc => {
          documentMap[doc.fileKey] = {
            fileName: doc.fileName || 'Attachment',
            fileSize: doc.fileSize || 0,
            contentType: doc.contentType || 'application/octet-stream'
          };
        });
        
        const documentRepo = queryRunner.manager.getRepository(Document);
        const documentPromises = allFileKeys.map(async fileKey => {
          // Create document record with proper metadata if available
          const metadata = documentMap[fileKey] || {
            fileName: 'Attachment',
            fileSize: 0,
            contentType: 'application/octet-stream'
          };
          
          const document = documentRepo.create({
            entityType: 'purchase_request',  // Use string value directly to match database column type
            entityId: prId,
            fileKey,
            fileName: metadata.fileName,
            fileSize: metadata.fileSize,
            contentType: metadata.contentType
          });
          
          return await queryRunner.manager.save(document);
        });
        
        await Promise.all(documentPromises);
      } catch (error) {
        console.error('Error attaching files to purchase request:', error);
        // Continue with PR creation even if file attachment fails
      }
    }

    // Create approval entries
    // Use the userRoles from the request
    const userRoles = request.userRoles || [];
    const approvalRepo = queryRunner.manager.getRepository(Approval);
    
    // Define all possible approval roles and their sequence
    const allApprovalRoles = [
      { role: ApprovalRole.CC_HEAD, sequence: 1 },
      { role: ApprovalRole.BIZ_FIN, sequence: 2 },
      { role: ApprovalRole.PROCUREMENT_MANAGER, sequence: 3 }
    ];

    // Create approval entries only for required roles
    const approvalEntries = allApprovalRoles.map(({ role, sequence }) => {
      return approvalRepo.create({
        requestId: prId,
        approvalRole: role,
        requestType: RequestType.PURCHASE_REQUEST,
        approvalType: ApprovalType.PR_APPROVAL,
        approvalSequence: sequence,
        status: ApprovalStatus.PENDING_APPROVAL,
      });
    });
    
    // Only save approval entries if there are any required
    if (approvalEntries.length > 0) {
      await queryRunner.manager.save(approvalEntries);
    }

    // Commit the transaction
    await queryRunner.commitTransaction();

    // Fetch the complete purchase request with items
    const completePurchaseRequest = await AppDataSource
      .getRepository(PurchaseRequest)
      .createQueryBuilder('pr')
      .leftJoinAndSelect('pr.items', 'items')
      .leftJoinAndSelect('items.itemCategory', 'itemCategory')
      .where('pr.id = :id', { id: prId })
      .getOne();
      
    // Fetch approvals separately since it's a polymorphic relationship
    const fetchedApprovals = await AppDataSource
      .getRepository(Approval)
      .createQueryBuilder('approval')
      .where('approval.requestId = :requestId', { requestId: prId })
      .andWhere('approval.requestType = :requestType', { requestType: RequestType.PURCHASE_REQUEST })
      .getMany();
      
    // Add approvals to the response
    if (completePurchaseRequest) {
      (completePurchaseRequest as any).approvals = fetchedApprovals;
    }

    notifyNextApprovers({
      requestId: prId,
      requestType: RequestType.PURCHASE_REQUEST,
      approvalType: ApprovalType.PR_APPROVAL,
      currentApprovalSequence: 1,
      status: ApprovalStatus.PENDING_APPROVAL,
      remarks: '',
      emailRequestType: EmailRequestType.PURCHASE_REQUEST,
      costCenterId: costCenterId
    }).catch(err => console.error(`Error notifying next approvers: ${err.message}`));
    

    return reply.status(201).send(completePurchaseRequest);
  } catch (error: any) {
    // Rollback the transaction in case of error
    try {
      // Only rollback if transaction is active
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
    } catch (rollbackError) {
      console.error('Error rolling back transaction:', rollbackError);
    }
    
    console.error('Error creating purchase request:', error);
    return reply.status(500).send({ 
      error: true, 
      message: 'Internal server error', 
      details: error.message 
    });
  } finally {
    // Release the query runner
    try {
      await queryRunner.release();
    } catch (releaseError) {
      console.error('Error releasing query runner:', releaseError);
    }
  }
};

// Define the query parameters interface
export interface GetPurchaseRequestsQuery {
  limit?: number;
  offset?: number;
  status?: string;
}

/**
 * Helper function to enrich purchase requests with business units, cost centers, users, and total value
 */
const enrichPurchaseRequests = async (purchaseRequests: PurchaseRequest[]): Promise<EnrichedPurchaseRequest[]> => {
  try {
    if (purchaseRequests.length === 0) {
      return [];
    }
    
    // Extract IDs for related entities
    const businessUnitIds = [...new Set(purchaseRequests
      .filter(pr => pr.businessUnitId != null)
      .map(pr => pr.businessUnitId))];
      
    const costCenterIds = [...new Set(purchaseRequests
      .filter(pr => pr.costCenterId != null)
      .map(pr => pr.costCenterId))];
      
    const userIds = [...new Set(purchaseRequests
      .filter(pr => pr.createdBy != null)
      .map(pr => pr.createdBy))];

    // Fetch all related entities in parallel to avoid N+1 queries
    const [businessUnits, costCenters, users] = await Promise.all([
      businessUnitIds.length > 0 ? AppDataSource.getRepository(BusinessUnit).find({
        where: { id: In(businessUnitIds) }
      }) : [],
      costCenterIds.length > 0 ? AppDataSource.getRepository(CostCenter).find({
        where: { id: In(costCenterIds) }
      }) : [],
      userIds.length > 0 ? AppDataSource.getRepository(User).find({
        where: { id: In(userIds) }
      }) : []
    ]);

    // Create lookup maps for faster access
    const businessUnitMap = new Map(businessUnits.map(bu => [bu.id, bu]));
    const costCenterMap = new Map(costCenters.map(cc => [cc.id, cc]));
    const userMap = new Map(users.map(user => [user.id, user]));

    // Enrich purchase requests with related entities and calculate total value
    return purchaseRequests.map(pr => {
      // Calculate total value from PR items
      const totalValue = pr.items ? pr.items.reduce((sum, item) => {
        const quantity = parseFloat(item.quantity?.toString() || '0');
        const price = parseFloat(item.estimatedPricePerQuantity?.toString() || '0');
        return sum + (quantity * price);
      }, 0) : 0;

      // Get related entities from maps
      const businessUnit = pr.businessUnitId ? businessUnitMap.get(pr.businessUnitId) || null : null;
      const costCenter = pr.costCenterId ? costCenterMap.get(pr.costCenterId) || null : null;
      const createdByUser = pr.createdBy ? userMap.get(pr.createdBy) || null : null;

      // Return enriched purchase request
      return {
        ...pr,
        totalValue: parseFloat(totalValue.toFixed(2)),
        businessUnit,
        costCenter,
        createdByUser
      };
    });
  } catch (error) {
    console.error('Error enriching purchase requests:', error);
    return purchaseRequests.map(pr => ({ 
      ...pr, 
      totalValue: 0, 
      businessUnit: null, 
      costCenter: null, 
      createdByUser: null 
    }));
  }
};

// Get purchase requests with role-based filtering and pagination
const getAllPurchaseRequests = async (
  request: FastifyRequest<{ Querystring: GetPurchaseRequestsQuery }> & { user?: User, userRoles?: UserRole[] },
  reply: FastifyReply
) => {
  try {
    // Ensure user is authenticated
    if (!request.user) {
      return reply.status(401).send({ error: true, message: 'Authentication required' });
    }

    const { limit = 10, offset = 0, status } = request.query;
    const userId = request.user.id;
    const userRoles = request.userRoles || [];

    // Create query builder
    let queryBuilder = AppDataSource
      .getRepository(PurchaseRequest)
      .createQueryBuilder('pr')
      .leftJoinAndSelect('pr.items', 'items')
      .leftJoinAndSelect('items.itemCategory', 'itemCategory');

    // Apply status filter if provided
    if (status) {
      queryBuilder = queryBuilder.andWhere('pr.status = :status', { status });
    }

    // Apply role-based filtering
    let hasAdminPermission = false;

    // Check if user has ADMIN, PROCUREMENT_MANAGER or BIZ_FIN role
    if (userRoles.length > 0) {
      // First, check if the user has any admin-level role that can see all PRs
      hasAdminPermission = userRoles.some(role => {
        // Check exact role type
        if (role.role === ApprovalRole.PROCUREMENT_MANAGER || role.role === ApprovalRole.FINANCE ||
            role.role === ApprovalRole.BIZ_FIN) {
          return true;
        }
        
        // Also check if role includes 'admin' string for backward compatibility
        if (typeof role.role === 'string' && role.role.toLowerCase().includes('admin')) {
          return true;
        }
        
        return false;
      });

      if (!hasAdminPermission) {
        // Check if user has CC_HEAD role
        const ccHeadRoles = userRoles.filter(role => role.role === ApprovalRole.CC_HEAD);
        
        if (ccHeadRoles.length > 0) {
          // CC_HEAD can see PRs for their cost centers
          const costCenterIds = ccHeadRoles
            .filter(role => role.costCenterId !== null && role.costCenterId !== undefined)
            .map(role => role.costCenterId);
          
          if (costCenterIds.length > 0) {
            queryBuilder = queryBuilder.andWhere('pr.costCenterId IN (:...costCenterIds)', { costCenterIds });
          } else {
            // If CC_HEAD has no assigned cost centers, they can only see their own PRs
            queryBuilder = queryBuilder.andWhere('pr.createdBy = :userId', { userId });
          }
        } else {
          // Regular user can only see their own PRs
          queryBuilder = queryBuilder.andWhere('pr.createdBy = :userId', { userId });
        }
      }
    } else {
      // No roles defined - default to only seeing own PRs
      queryBuilder = queryBuilder.andWhere('pr.createdBy = :userId', { userId });
    }
    
    // Get total count for pagination metadata before applying pagination
    const totalCount = await queryBuilder.getCount();
    
    // Apply pagination
    queryBuilder = queryBuilder
      .orderBy('pr.createdAt', 'DESC')
      .take(limit)
      .skip(offset);
    
    // Execute the query
    const purchaseRequests = await queryBuilder.getMany();
    
    // Get all business units, cost centers, and users for lookup
    const businessUnits = await AppDataSource.getRepository(BusinessUnit).find();
    const costCenters = await AppDataSource.getRepository(CostCenter).find();
    const users = await AppDataSource.getRepository(User).find();
    
    // Create lookup objects for business units, cost centers, and users
    const businessUnitMap: Record<number, BusinessUnit> = {};
    businessUnits.forEach(bu => {
      businessUnitMap[bu.id] = bu;
    });
    
    const costCenterMap: Record<number, CostCenter> = {};
    costCenters.forEach(cc => {
      costCenterMap[cc.id] = cc;
    });
    
    const userMap: Record<number, User> = {};
    users.forEach(user => {
      userMap[user.id] = user;
    });
    
    // Calculate total value for each purchase request and add business unit and cost center
    const enrichedPurchaseRequests = purchaseRequests.map(pr => {
      // Calculate total value
      let totalValue = 0;
      if (pr.items && pr.items.length > 0) {
        totalValue = pr.items.reduce((sum, item) => {
          const quantityStr = typeof item.quantity === 'string' ? item.quantity : String(item.quantity || '0');
          const priceStr = typeof item.estimatedPricePerQuantity === 'string' ? item.estimatedPricePerQuantity : String(item.estimatedPricePerQuantity || '0');
          const quantity = parseFloat(quantityStr);
          const price = parseFloat(priceStr);
          return sum + (quantity * price);
        }, 0);
      }
      
      // Look up business unit, cost center, and created by user
      const businessUnit = pr.businessUnitId ? businessUnitMap[pr.businessUnitId] || null : null;
      const costCenter = pr.costCenterId ? costCenterMap[pr.costCenterId] || null : null;
      const createdByUser = pr.createdBy ? userMap[pr.createdBy] || null : null;
      
      return {
        ...pr,
        totalValue: parseFloat(totalValue.toFixed(2)),
        businessUnit,
        costCenter,
        createdByUser
      };
    });
    
    // Return the results with pagination metadata
    return reply.status(200).send({
      data: enrichedPurchaseRequests,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + purchaseRequests.length < totalCount
      }
    });
  } catch (error) {
    console.error('Error fetching purchase requests:', error);
    return reply.status(500).send({ message: 'Internal server error' });
  }
};

// Get purchase request by ID with role-based access control
const getPurchaseRequestById = async (
  request: FastifyRequest<{ Params: { id: number } }> & { user?: User, userRoles?: UserRole[] },
  reply: FastifyReply
) => {
  try {
    // Ensure user is authenticated
    if (!request.user) {
      return reply.status(401).send({ error: true, message: 'Authentication required' });
    }

    const { id } = request.params;
    const userId = request.user.id;
    const userRoles = request.userRoles || [];

    // Get the purchase request with items and item categories
    const purchaseRequest = await AppDataSource
      .getRepository(PurchaseRequest)
      .createQueryBuilder('pr')
      .leftJoinAndSelect('pr.items', 'items')
      .leftJoinAndSelect('items.itemCategory', 'itemCategory')
      .where('pr.id = :id', { id })
      .getOne();

    if (!purchaseRequest) {
      return reply.status(404).send({ error: true, message: 'Purchase request not found' });
    }

    // Check if user has permission to view this purchase request
    // ADMIN, PROCUREMENT_MANAGER and BIZ_FIN can view all PRs
    let hasAdminPermission = userRoles.some(role => {
      // Check exact role type
      if (role.role === ApprovalRole.PROCUREMENT_MANAGER || 
          role.role === ApprovalRole.BIZ_FIN || 
          role.role === ApprovalRole.FINANCE) {
        return true;
      }
      
      // Also check if role includes 'admin' string for backward compatibility
      if (typeof role.role === 'string' && role.role.toLowerCase().includes('admin')) {
        return true;
      }
      
      return false;
    });

    if (!hasAdminPermission) {
      // Check if user is the creator of the PR
      const isCreator = purchaseRequest.createdBy === userId;
      
      if (!isCreator) {
        // Check if user is CC_HEAD for the PR's cost center
        const isCCHead = userRoles.some(role => 
          role.role === ApprovalRole.CC_HEAD && 
          role.costCenterId === purchaseRequest.costCenterId
        );
        
        if (!isCCHead){
          return reply.status(403).send({ 
            error: true, 
            message: 'You do not have permission to view this purchase request' 
          });
        }
      }
    }

    // Get approvals for the purchase request
    const approvals = await AppDataSource
      .getRepository(Approval)
      .createQueryBuilder('approval')
      .where('approval.requestId = :requestId', { requestId: id })
      .andWhere('approval.requestType = :requestType', { requestType: RequestType.PURCHASE_REQUEST })
      .getMany();

    // Enrich the purchase request with business unit, cost center, user data, and total value
    const [enrichedPurchaseRequest] = await enrichPurchaseRequests([purchaseRequest]);

    // Get document attachments for the purchase request
    // Use string value directly to match what's in the database
    const attachments = await documentService.getDocumentsByEntity('purchase_request', id);
    
    // Ensure all attachments have fileUrl set, with defaults for missing fields
    for (const attachment of attachments) {
      if (!attachment.fileUrl && attachment.fileKey) {
        try {
          // Generate a new pre-signed URL for the file
          const s3Service = await import('../services/s3.service').then(m => m.default);
          attachment.fileUrl = await s3Service.getFileUrl(attachment.fileKey);
        } catch (error) {
          attachment.fileUrl = ''; // Set empty string as fallback
        }
      }
    }

    // Determine if verification dialog should be shown and to which role
    // Find the first pending approval (if any)
    let showPRVerificationDialog = false;
    let showPRVerificationToRole = null;

    const pendingApproval = approvals.find(approval => approval.status === ApprovalStatus.PENDING_APPROVAL && approval.approvalType === 'PR_APPROVAL');
    
    if (pendingApproval) {
      showPRVerificationDialog = true;
      showPRVerificationToRole = pendingApproval.approvalRole;
    }
    
    // Check if a purchase order has been created for this PR
    let poId = null;
    let selectedQuoteId = null;
    let poStatus = null;
    
    const purchaseOrder = await AppDataSource
      .getRepository(PurchaseOrder)
      .createQueryBuilder('po')
      .where('po.prId = :prId', { prId: id })
      .getOne();
      
    if (purchaseOrder) {
      poId = purchaseOrder.id;
      selectedQuoteId = purchaseOrder.quotationId;
      poStatus = purchaseOrder.status;
    }

    // Return the enriched purchase request with approvals, verification dialog info, PO info, and attachments
    const response = {
      ...enrichedPurchaseRequest,
      approvals,
      showPRVerificationDialog,
      showPRVerificationToRole,
      poId,
      selectedQuoteId,
      poStatus,
      attachments: attachments || [] // Explicitly include attachments array
    };
    
    return reply.status(200).send(response);
  } catch (error) {
    console.error('Error fetching purchase request:', error);
    return reply.status(500).send({ message: 'Internal server error' });
  }
};

/**
 * Get counts of all purchase request statuses
 * This endpoint provides counts for each status of purchase requests
 */
const getPurchaseRequestStatusCounts = async (
  request: FastifyRequest & { user?: User, userRoles?: UserRole[] },
  reply: FastifyReply
) => {
  try {
    // Ensure user is authenticated
    if (!request.user) {
      return reply.status(401).send({ error: true, message: 'Authentication required' });
    }

    const userId = request.user.id;
    const userRoles = request.userRoles || [];

    // Create query builder for base query
    let queryBuilder = AppDataSource
      .getRepository(PurchaseRequest)
      .createQueryBuilder('pr');

    // Apply role-based filtering similar to getAllPurchaseRequests
    let hasAdminPermission = false;

    // Check if user has ADMIN, PROCUREMENT_MANAGER or BIZ_FIN role
    if (userRoles.length > 0) {
      hasAdminPermission = userRoles.some(role => {
        // Check exact role type
        if (role.role === ApprovalRole.PROCUREMENT_MANAGER || role.role === ApprovalRole.FINANCE ||
            role.role === ApprovalRole.BIZ_FIN) {
          return true;
        }
        
        // Also check if role includes 'admin' string for backward compatibility
        if (typeof role.role === 'string' && role.role.toLowerCase().includes('admin')) {
          return true;
        }
        
        return false;
      });

      if (!hasAdminPermission) {
        // Check if user has CC_HEAD role
        const ccHeadRoles = userRoles.filter(role => role.role === ApprovalRole.CC_HEAD);
        
        if (ccHeadRoles.length > 0) {
          // CC_HEAD can see PRs for their cost centers
          const costCenterIds = ccHeadRoles
            .filter(role => role.costCenterId !== null && role.costCenterId !== undefined)
            .map(role => role.costCenterId);
          
          if (costCenterIds.length > 0) {
            queryBuilder = queryBuilder.andWhere('pr.costCenterId IN (:...costCenterIds)', { costCenterIds });
          } else {
            // If CC_HEAD has no assigned cost centers, they can only see their own PRs
            queryBuilder = queryBuilder.andWhere('pr.createdBy = :userId', { userId });
          }
        } else {
          // Regular user can only see their own PRs
          queryBuilder = queryBuilder.andWhere('pr.createdBy = :userId', { userId });
        }
      }
    } else {
      // No roles defined - default to only seeing own PRs
      queryBuilder = queryBuilder.andWhere('pr.createdBy = :userId', { userId });
    }

    // Get all the possible statuses from the enum
    const statuses = Object.values(PurchaseRequestStatus);
    
    // Create an array of promises for each status count
    const countPromises = statuses.map(async (status) => {
      const count = await queryBuilder
        .clone()
        .andWhere('pr.status = :status', { status })
        .getCount();
      
      return { status, count };
    });
    
    // Wait for all count queries to complete
    const statusCounts = await Promise.all(countPromises);
    
    // Calculate total count
    const totalCount = statusCounts.reduce((sum, statusCount) => sum + statusCount.count, 0);
    
    // Format the response
    const response = {
      total: totalCount,
      counts: statusCounts.reduce((obj, item) => {
        obj[item.status] = item.count;
        return obj;
      }, {} as Record<string, number>)
    };
    
    return reply.status(200).send(response);
  } catch (error) {
    console.error('Error getting purchase request status counts:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};

export {
  createPurchaseRequest,
  updatePurchaseRequest,
  rejectPurchaseRequest,
  getAllPurchaseRequests,
  getPurchaseRequestById,
  getPurchaseRequestStatusCounts
};
