import { FastifyReply, FastifyRequest } from 'fastify';
import { Static, Type } from '@sinclair/typebox';
import s3Service from '../services/s3.service';
import { documentService } from '../services/document.service';
import { EntityType, FileUploadResponseSchema, CreateDocumentSchema } from '../schemas/document.schema';

// Schema for document attachment to entity request params
const AttachDocumentParamsSchema = Type.Object({
  entityType: Type.Enum(EntityType),
  entityId: Type.Number()
});

// Schema for entity documents request params
const EntityDocumentsParamsSchema = Type.Object({
  entityType: Type.Enum(EntityType),
  entityId: Type.Number()
});

// Schema for document delete request params
const DocumentDeleteParamsSchema = Type.Object({
  id: Type.Number()
});

type AttachDocumentParams = Static<typeof AttachDocumentParamsSchema>;
type EntityDocumentsParams = Static<typeof EntityDocumentsParamsSchema>;
type DocumentDeleteParams = Static<typeof DocumentDeleteParamsSchema>;

// Controllers for document-related operations
const documentController = {
  /**
   * Upload a document to S3
   */
  async uploadDocument(request: FastifyRequest, reply: FastifyReply) {
    try {

      // CRITICAL: Handle the case where multiple files are sent with the same field name 'file'
      // This happens in curl with multiple --form 'file=@file1.jpg' --form 'file=@file2.jpg'
      if (request.body && typeof request.body === 'object' && 'file' in request.body) {
        const fileField = (request.body as any).file;
        
        // Check if it's an array (or array-like structure) which means multiple files with same field name
        if (Array.isArray(fileField) || 
            (typeof fileField === 'object' && '0' in fileField && '1' in fileField)) {
          console.log('Detected multiple files with same field name');
          
          // Use our multiple files upload logic
          const MAX_FILES = 10; // Allow up to 10 files in a single request
          
          // Process the array of files
          const files: Array<{ fileKey: string; fileUrl: string; fileName: string; fileSize: number; contentType: string }> = [];
          const fileKeys: string[] = [];
          
          // Handle both true arrays and object with numeric keys
          const fileArray = Array.isArray(fileField) ? fileField : Object.values(fileField);
          
          // Process each file
          for (const file of fileArray) {
            if (!file || typeof file !== 'object') continue;
            
            try {
              // Extract file data
              const fileData = file as any;
              let filename = '';
              let mimetype = '';
              let fileBuffer: Buffer | null = null;
              
              // Get filename
              if ('filename' in fileData) {
                filename = fileData.filename as string;
              } else if ('name' in fileData) {
                filename = fileData.name as string;
              } else {
                filename = 'unknown-file';
              }
              
              // Get mimetype
              if ('mimetype' in fileData) {
                mimetype = fileData.mimetype as string;
              } else if ('type' in fileData) {
                mimetype = fileData.type as string;
              } else {
                mimetype = 'application/octet-stream';
              }
              
              // Get buffer
              if ('data' in fileData && fileData.data) {
                fileBuffer = fileData.data as Buffer;
              } else if ('buffer' in fileData && fileData.buffer) {
                fileBuffer = fileData.buffer as Buffer;
              } else if ('value' in fileData && fileData.value) {
                if (Buffer.isBuffer(fileData.value)) {
                  fileBuffer = fileData.value;
                }
              }
              
              if (fileBuffer) {
                // Upload to S3
                const fileInfo = await s3Service.uploadBufferToS3(filename, mimetype, fileBuffer);
                files.push(fileInfo);
                fileKeys.push(fileInfo.fileKey);
                console.log(`Uploaded file: ${filename}, size: ${fileBuffer.length} bytes`);
              }
            } catch (err) {
              console.error('Error processing file in array:', err);
            }
          }
          
          if (files.length > 0) {
            // Return the standard multiple files response
            return reply.code(200).send({
              files,
              fileKeys,
              message: `Successfully uploaded ${files.length} files`
            });
          }
        }
      }

      // If we got here, it's not the special multiple-file-same-field case
      // Continue with normal handling of single vs. multiple files
      
      // Automatically detect multiple files, or check if it's explicitly marked as a multi-file request
      let isMultipleFiles = 
        request.headers['x-multiple-files'] === 'true' || 
        (request.query && (request.query as any).multiple === 'true');
      
      // Auto-detect multiple files by checking if the request body contains more than one file field
      if (!isMultipleFiles && request.body && typeof request.body === 'object') {
        // Count potential file fields (numeric indices or fields starting with 'file')
        let fileFieldCount = 0;
        for (const key in request.body) {
          // Check for numeric indices or fields with 'file' prefix
          if (!isNaN(Number(key)) || key === 'file' || key.startsWith('file')) {
            fileFieldCount++;
          }
          // If we found more than one file field, treat as multiple
          if (fileFieldCount > 1) {
            isMultipleFiles = true;
            console.log('Auto-detected multiple files in request');
            break;
          }
        }
      }
      
      
      // If it's a multiple files request, use our improved uploadMultipleFiles method
      if (isMultipleFiles) {
        const MAX_FILES = 10; // Allow up to 10 files in a single request
        const filesInfo = await s3Service.uploadMultipleFiles(request, MAX_FILES);
        
        // Return a format compatible with existing code
        return reply.code(200).send({
          files: filesInfo,
          fileKeys: filesInfo.map(f => f.fileKey), // For backward compatibility
          message: `Successfully uploaded ${filesInfo.length} files`
        });
      } else {
        // Single file upload - use our improved uploadFile method
        const fileInfo = await s3Service.uploadFile(request);
        
        // Return the standard single file response
        return reply.code(200).send({
          fileKey: fileInfo.fileKey,
          fileUrl: fileInfo.fileUrl,
          fileName: fileInfo.fileName,
          fileSize: fileInfo.fileSize,
          contentType: fileInfo.contentType
        });
      }
    } catch (error) {
      console.error('Error in uploadDocument:', error);
      
      // Return a more detailed error response
      const statusCode = error instanceof Error && error.message.includes('No file') ? 400 : 500;
      return reply.code(statusCode).send({ 
        message: statusCode === 400 ? 'No files were uploaded' : 'Error uploading document',
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  },

  /**
   * Attach a document to an entity
   */
  async attachDocument(
    request: FastifyRequest<{ Params: AttachDocumentParams; Body: Static<typeof CreateDocumentSchema> }>,
    reply: FastifyReply
  ) {
    try {
      const { entityType, entityId } = request.params;
      const documentData = request.body;
      
      // Create document record in database
      const document = await documentService.createDocument({
        ...documentData,
        entityType,
        entityId
      });
      
      // Generate a fresh URL
      const fileUrl = await s3Service.getFileUrl(document.fileKey);
      
      return reply.code(201).send({
        document: {
          ...document,
          fileUrl
        }
      });
    } catch (error) {
      console.error('Error in attachDocument:', error);
      return reply.code(500).send({ 
        message: 'Error attaching document to entity',
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  },

  /**
   * Attach multiple documents to an entity
   */
  async attachMultipleDocuments(
    request: FastifyRequest<{ Params: AttachDocumentParams; Body: { documents: Array<Static<typeof CreateDocumentSchema>> } }>,
    reply: FastifyReply
  ) {
    try {
      const { entityType, entityId } = request.params;
      const { documents: documentsData } = request.body;
      
      if (!Array.isArray(documentsData) || documentsData.length === 0) {
        return reply.code(400).send({
          message: 'No documents provided'
        });
      }
      
      // Create document records in database
      const createdDocuments = [];
      
      for (const docData of documentsData) {
        try {
          const document = await documentService.createDocument({
            ...docData,
            entityType,
            entityId
          });
          
          // Generate a fresh URL
          const fileUrl = await s3Service.getFileUrl(document.fileKey);
          
          createdDocuments.push({
            ...document,
            fileUrl
          });
        } catch (error) {
          console.error(`Error creating document ${docData.fileKey}:`, error);
          // Continue with next document instead of failing entire batch
        }
      }
      
      if (createdDocuments.length === 0) {
        return reply.code(500).send({
          message: 'Failed to attach any documents'
        });
      }
      
      return reply.code(201).send({
        documents: createdDocuments,
        message: `Successfully attached ${createdDocuments.length} documents`
      });
    } catch (error) {
      console.error('Error in attachMultipleDocuments:', error);
      return reply.code(500).send({ 
        message: 'Error attaching documents to entity',
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  },

  /**
   * Get all documents for an entity
   */
  async getEntityDocuments(
    request: FastifyRequest<{ Params: EntityDocumentsParams }>,
    reply: FastifyReply
  ) {
    try {
      const { entityType, entityId } = request.params;
      
      const documents = await documentService.getDocumentsByEntity(entityType, entityId);
      
      return reply.code(200).send({
        documents
      });
    } catch (error) {
      console.error('Error in getEntityDocuments:', error);
      return reply.code(500).send({ 
        message: 'Error fetching entity documents',
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  },

  /**
   * Delete a document
   */
  async deleteDocument(
    request: FastifyRequest<{ Params: DocumentDeleteParams }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      
      const success = await documentService.deleteDocument(id);
      
      if (!success) {
        return reply.code(404).send({
          message: 'Document not found'
        });
      }
      
      return reply.code(200).send({
        message: 'Document deleted successfully'
      });
    } catch (error) {
      console.error('Error in deleteDocument:', error);
      return reply.code(500).send({ 
        message: 'Error deleting document',
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }
};

export default documentController;