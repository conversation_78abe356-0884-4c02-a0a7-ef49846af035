import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../db/typeorm.config';
// Using the correct entity names based on the database schema (business_units, cost_centers)
import { BusinessUnit } from '../entities/BusinessUnit.entity';
import { CostCenter } from '../entities/CostCenter.entity';

/**
 * Get all business units with their associated cost centers
 */
export const getBusinessUnitsWithCostCenters = async (
  request: FastifyRequest,
  reply: FastifyReply
) => {
  try {
    // Get all business units
    const businessUnitRepo = AppDataSource.getRepository(BusinessUnit);
    const businessUnits = await businessUnitRepo.find();

    // Get all cost centers
    const costCenterRepo = AppDataSource.getRepository(CostCenter);
    const costCenters = await costCenterRepo.find();

    // We don't need to create sample cost centers as they already exist with names

    // Group cost centers by business unit
    const result = businessUnits.map(businessUnit => {
      const businessUnitCostCenters = costCenters
        .filter(cc => cc.businessUnitId === businessUnit.id)
        .map(cc => ({
          id: cc.id,
          name: cc.name
        }));

      return {
        id: businessUnit.id,
        name: businessUnit.name,
        costCenters: businessUnitCostCenters
      };
    });

    return reply.status(200).send(result);
  } catch (error) {
    console.error('Error getting business units with cost centers:', error);
    return reply.status(500).send({
      error: true,
      message: 'Internal server error'
    });
  }
};
