import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../db/typeorm.config';
import { PurchaseOrder, PurchaseOrderStatus, PurchaseOrderType } from '../entities/PurchaseOrder.entity';
import { PoItem } from '../entities/PoItem.entity';
import { Approval } from '../entities/Approval.entity';
import { PurchaseRequest } from '../entities/PurchaseRequest.entity';
import { Quotation } from '../entities/Quotation.entity';
import { User } from '../entities/User.entity';
import { generatePONumber } from '../utils/po-number-generator';
import { BusinessUnit } from '../entities/BusinessUnit.entity';
import { CostCenter } from '../entities/CostCenter.entity';
import { ApprovalStatus, ApprovalType, EmailRequestType, RequestType } from '../constants/enums';
import { notifyNextApprovers } from '../utils/next-approval-email';

// Interface for request body
interface CreatePurchaseOrderBody {
  prId: number;
  quotationId: number;
  type: PurchaseOrderType;
  vendorId: number;
  businessUnitId: number;
  costCenterId: number;
  paymentTerms: string;
  deliveryTerms?: string;
  expectedDeliveryDate?: Date | string;
  billTo?: string;
  shipToAddress?: string;
  remarks?: string;
  totalAmount: number;
  currency?: string;
  recurrenceFrequency?: string;
  startDate?: string;
  endDate?: string;
  items: {
    itemCategoryId: number;
    itemName: string;
    itemDescription?: string;
    quantity: number;
    uom: string;
    pricePerUnit: number;
    gstPercentage?: number;
    gstAmount?: number;
    totalValue?: number;
  }[];
}

/**
 * Create a new purchase order with items
 */
export const createPurchaseOrder = async (
  request: FastifyRequest<{ Body: CreatePurchaseOrderBody }>,
  reply: FastifyReply
) => {
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    const {
      prId,
      quotationId,
      type = PurchaseOrderType.ONE_TIME,
      vendorId,
      businessUnitId,
      expectedDeliveryDate,
      billTo,
      shipToAddress,
      costCenterId,
      paymentTerms = 'Net 30',
      deliveryTerms,
      remarks,
      totalAmount,
      currency = 'INR',
      recurrenceFrequency,
      startDate,
      endDate,
      items
    } = request.body;

    const userId = (request.user as User).id;

    // Verify purchase request exists
    const purchaseRequest = await queryRunner.manager.findOne(PurchaseRequest, {
      where: { id: prId }
    });

    if (!purchaseRequest) {
      await queryRunner.rollbackTransaction();
      return reply.status(404).send({ 
        error: true,
        message: 'Purchase Request not found' 
      });
    }

    // Generate unique PO number with new format: PO-{BU_CODE}-{YY}{MM}{SEQ}
    const poNumber = await generatePONumber(businessUnitId);

    // Create new purchase order
    const purchaseOrder = new PurchaseOrder();
    purchaseOrder.poNumber = poNumber;
    purchaseOrder.prId = prId;
    
    if (quotationId) {
      purchaseOrder.quotationId = quotationId;
    }

    purchaseOrder.type = type;
    purchaseOrder.status = PurchaseOrderStatus.PENDING_APPROVAL;
    purchaseOrder.vendorId = vendorId;
    purchaseOrder.businessUnitId = businessUnitId;
    purchaseOrder.costCenterId = costCenterId;
    purchaseOrder.paymentTerms = paymentTerms;
    
    // Handle expectedDeliveryDate conversion to Date if it's a string
    if (expectedDeliveryDate) {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      try {
        if (typeof expectedDeliveryDate === 'string' && dateRegex.test(expectedDeliveryDate)) {
          purchaseOrder.expectedDeliveryDate = new Date(expectedDeliveryDate);
        } else if (expectedDeliveryDate instanceof Date && !isNaN(expectedDeliveryDate.getTime())) {
          purchaseOrder.expectedDeliveryDate = expectedDeliveryDate;
        } else {
          await queryRunner.rollbackTransaction();
          return reply.status(400).send({
          error: true,
          message: 'Invalid expectedDeliveryDate format, must be a string in YYYY-MM-DD format',
          });
        }
      } catch (error: unknown) {
        await queryRunner.rollbackTransaction();
        return reply.status(500).send({
          error: true,
          message: 'Error processing expectedDeliveryDate ' + error,
        });
      }
    }
    
    // Only assign billTo if defined
    if (billTo) {
      purchaseOrder.billTo = billTo;
    }
    
    // Only assign shipToAddress if defined
    if (shipToAddress) {
      purchaseOrder.shipToAddress = shipToAddress;
    }
    
    if (deliveryTerms) {
      purchaseOrder.deliveryTerms = deliveryTerms;
    }
    
    if (remarks) {
      purchaseOrder.remarks = remarks;
    }
    
    purchaseOrder.totalAmount = totalAmount;
    purchaseOrder.currency = currency;
    purchaseOrder.createdBy = userId;

    // Handle recurring PO fields if applicable
    if (type === PurchaseOrderType.RECURRING && recurrenceFrequency) {
      purchaseOrder.recurrenceFrequency = recurrenceFrequency as any;
      
      if (startDate) {
        purchaseOrder.startDate = new Date(startDate);
      } else {
        // Default to today
        purchaseOrder.startDate = new Date();
      }
      
      if (endDate) {
        purchaseOrder.endDate = new Date(endDate);
      } else {
        // Default to 1 year from start date
        const defaultEndDate = new Date(purchaseOrder.startDate);
        defaultEndDate.setFullYear(defaultEndDate.getFullYear() + 1);
        purchaseOrder.endDate = defaultEndDate;
      }
    }

    // Save the purchase order first
    const savedPO = await queryRunner.manager.save(purchaseOrder);
    
    // Set the total amount to be calculated after items are saved
    let calculatedTotal = 0;

    // Create PO items
    if (items && items.length > 0) {
      const poItems = items.map(item => {
        const poItem = new PoItem();
        poItem.poId = savedPO.id;
        
        if (item.itemCategoryId) {
          poItem.itemCategoryId = item.itemCategoryId;
        }
        
        poItem.itemName = item.itemName;
        poItem.itemDescription = item.itemDescription || '';
        poItem.quantity = item.quantity;
        poItem.uom = item.uom || 'Nos';
        poItem.pricePerUnit = item.pricePerUnit;
        
        // Handle GST calculation
        const gstPercentage = item.gstPercentage !== undefined ? item.gstPercentage : 18;
        poItem.gstPercentage = gstPercentage;
        
        const priceBeforeTax = poItem.pricePerUnit * poItem.quantity;
        const gstAmount = (priceBeforeTax * gstPercentage) / 100;
        poItem.gstAmount = item.gstAmount !== undefined ? item.gstAmount : gstAmount;
        
        // Calculate total value if not provided
        poItem.totalValue = item.totalValue !== undefined ? 
          item.totalValue : priceBeforeTax + poItem.gstAmount;
          
        return poItem;
      });

      await queryRunner.manager.save(poItems);
      
      // Calculate total from the PO items
      calculatedTotal = poItems.reduce((sum, item) => sum + Number(item.totalValue || 0), 0);
      
      // Update the PO with the calculated total
      savedPO.totalAmount = calculatedTotal;
      await queryRunner.manager.save(savedPO);
    }

    // Find the latest approval sequence for this PR
    const latestApproval = await queryRunner.manager
      .createQueryBuilder(Approval, 'approval')
      .where('approval.requestId = :prId', { prId })
      .andWhere('approval.requestType = :requestType', { requestType: 'PurchaseRequest' })
      .orderBy('approval.approvalSequence', 'DESC')
      .getOne();

    let nextSequence = 1;
    if (latestApproval) {
      nextSequence = (latestApproval.approvalSequence || 0) + 1;
    }

    // Create BizFin approval record
    const bizFinApproval = new Approval();
    bizFinApproval.requestId = savedPO.prId;
    bizFinApproval.requestType = 'PR_APPROVAL';
    bizFinApproval.approvalType = 'QUOTE_APPROVAL';
    bizFinApproval.approvalRole = 'biz_fin';
    bizFinApproval.approvalSequence = nextSequence;
    bizFinApproval.status = 'PENDING_APPROVAL';

    await queryRunner.manager.save(bizFinApproval);

    // Create Procurement Manager approval record
    const ccHeadApproval = new Approval();
    ccHeadApproval.requestId = savedPO.prId;
    ccHeadApproval.requestType = 'PR_APPROVAL';
    ccHeadApproval.approvalType = 'QUOTE_APPROVAL';
    ccHeadApproval.approvalRole = 'cc_head';
    ccHeadApproval.approvalSequence = nextSequence + 1;
    ccHeadApproval.status = 'PENDING_APPROVAL';

    await queryRunner.manager.save(ccHeadApproval);

    // Commit transaction
    await queryRunner.commitTransaction();

    notifyNextApprovers({
      requestId: savedPO.id,
      requestType: "PR_APPROVAL",
      approvalType: ApprovalType.QUOTE_APPROVAL,
      currentApprovalSequence: 4,
      status: ApprovalStatus.PENDING_APPROVAL,
      remarks: '',
      emailRequestType: EmailRequestType.PURCHASE_ORDER,
      costCenterId: costCenterId
    }).catch(err => console.error(`Error notifying next approvers: ${err.message}`));
    
    // Return response with formatted PO number as identifier
    return reply.status(201).send({
      message: 'Purchase Order created successfully',
      purchaseOrder: {
        id: savedPO.id,
        identifier: savedPO.poNumber, // Format: PO-2025-A7001
        poNumber: savedPO.poNumber,
        status: savedPO.status,
        createdAt: savedPO.createdAt,
        businessUnitId: savedPO.businessUnitId,
        costCenterId: savedPO.costCenterId,
        prId: savedPO.prId,
        vendorId: savedPO.vendorId,
        totalAmount: savedPO.totalAmount
      }
    });

  } catch (error: any) {
    // Rollback transaction on error
    await queryRunner.rollbackTransaction();
    console.error('Error creating purchase order:', error);
    
    return reply.status(500).send({ 
      error: true,
      message: `Internal server error: ${error.message || 'Unknown error'}` 
    });
    
  } finally {
    // Release query runner
    await queryRunner.release();
  }
};