import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../db/typeorm.config';
import { PurchaseOrder, PurchaseOrderStatus, PurchaseOrderType, RecurrenceFrequency } from '../entities/PurchaseOrder.entity';
import { User } from '../entities/User.entity';
import { UserRole } from '../entities/UserRole.entity';
import { ApprovalRole, RequestType } from '../constants/enums';
import { Quotation } from '../entities/Quotation.entity';
import { PurchaseRequest } from '../entities/PurchaseRequest.entity';
import { PoItem } from '../entities/PoItem.entity';
import { BusinessUnit } from '../entities/BusinessUnit.entity';
import { CostCenter } from '../entities/CostCenter.entity';
import { generatePONumber } from '../utils/po-number-generator';
import { Approval } from '../entities/Approval.entity';
import { PrItem } from '../entities/PrItem.entity';
import { ApprovalStatus } from '../constants/enums';
import { In } from 'typeorm';
import { GrnItem } from '../entities/GrnItem.entity';

// Interface for query parameters
export interface GetPurchaseOrdersQuery {
  page?: number;
  limit?: number;
  status?: string;
  type?: string;
  businessUnitId?: number;
  costCenterId?: number;
  vendorId?: number;
  startDate?: string;
  endDate?: string;
}

/**
 * Get all purchase orders with pagination and filtering based on user roles
 */
const getAllPurchaseOrders = async (
  request: FastifyRequest<{ Querystring: GetPurchaseOrdersQuery }> & { user?: User, userRoles?: UserRole[] },
  reply: FastifyReply
) => {
  try {
    // Ensure user is authenticated
    if (!request.user) {
      return reply.status(401).send({ error: true, message: 'Authentication required' });
    }

    const {
      page = 1,
      limit = 10,
      status,
      type,
      businessUnitId,
      costCenterId,
      vendorId,
      startDate,
      endDate
    } = request.query;

    // Calculate offset for pagination
    const offset = (page - 1) * limit;
    const userId = request.user.id;
    const userRoles = request.userRoles || [];

    // Create query builder
    let queryBuilder = AppDataSource.getRepository(PurchaseOrder)
      .createQueryBuilder('po')
      .leftJoinAndSelect('po.purchaseRequest', 'pr')
      .leftJoinAndSelect('po.quotation', 'quotation')
      .leftJoinAndSelect('po.businessUnit', 'businessUnit')
      .leftJoinAndSelect('po.costCenter', 'costCenter');

    // Apply role-based filtering
    if (userRoles.length > 0) {
      // Check if user has PROCUREMENT_MANAGER or BIZ_FIN role
      const hasProcurementOrBizFinRole = userRoles.some(role =>
        role.role === ApprovalRole.PROCUREMENT_MANAGER || role.role === ApprovalRole.BIZ_FIN || role.role === ApprovalRole.FINANCE
      );

      if (hasProcurementOrBizFinRole) {
        // PROCUREMENT_MANAGER and BIZ_FIN can see all POs
        // No additional filtering needed
      } else {
        // Check if user has CC_HEAD role
        const ccHeadRoles = userRoles.filter(role => role.role === ApprovalRole.CC_HEAD);

        if (ccHeadRoles.length > 0) {
          // CC_HEAD can see POs for their cost centers or POs created from PRs they initiated
          const costCenterIds = ccHeadRoles
            .filter(role => role.costCenterId !== null && role.costCenterId !== undefined)
            .map(role => role.costCenterId);

          if (costCenterIds.length > 0) {
            // Find POs either with matching cost center OR PRs created by the user
            queryBuilder = queryBuilder.andWhere(
              '(po.costCenterId IN (:...costCenterIds) OR pr.createdBy = :userId)',
              { costCenterIds, userId }
            );
          } else {
            // If CC_HEAD has no assigned cost centers, they can only see POs from PRs they created
            queryBuilder = queryBuilder.andWhere('pr.createdBy = :userId', { userId });
          }
        } else {
          // Regular user can only see POs related to PRs they created
          queryBuilder = queryBuilder.andWhere('pr.createdBy = :userId', { userId });
        }
      }
    } else {
      // Regular user can only see POs related to PRs they created
      queryBuilder = queryBuilder.andWhere('pr.createdBy = :userId', { userId });
    }

    // Apply additional filters if provided
    if (status) {
      queryBuilder.andWhere('po.status = :status', { status });
    }

    if (type) {
      queryBuilder.andWhere('po.type = :type', { type });
    }

    if (businessUnitId) {
      queryBuilder.andWhere('po.businessUnitId = :businessUnitId', { businessUnitId });
    }

    if (costCenterId) {
      queryBuilder.andWhere('po.costCenterId = :costCenterId', { costCenterId });
    }

    if (vendorId) {
      queryBuilder.andWhere('po.vendorId = :vendorId', { vendorId });
    }

    if (startDate) {
      queryBuilder.andWhere('po.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('po.createdAt <= :endDate', { endDate });
    }

    // Get total count for pagination
    const totalCount = await queryBuilder.getCount();

    // Get paginated results
    const purchaseOrders = await queryBuilder
      .orderBy('po.createdAt', 'DESC')
      .skip(offset)
      .take(limit)
      .getMany();

    // Format response
    const formattedPurchaseOrders = purchaseOrders.map(po => ({
      id: po.id,
      poNumber: po.poNumber,
      prId: po.prId,
      type: po.type,
      status: po.status,
      vendorId: po.vendorId,
      businessUnitId: po.businessUnitId,
      costCenterId: po.costCenterId,
      businessUnit: po.businessUnit ? {
        id: po.businessUnit.id,
        name: po.businessUnit.name
      } : null,
      costCenter: po.costCenter ? {
        id: po.costCenter.id,
        name: po.costCenter.name,
        businessUnitId: po.costCenter.businessUnitId
      } : null,
      expectedDeliveryDate: po.expectedDeliveryDate,
      billTo: po.billTo,
      shipToAddress: po.shipToAddress,
      totalAmount: po.totalAmount,
      currency: po.currency,
      createdAt: po.createdAt,
      recurrenceFrequency: po.recurrenceFrequency,
      startDate: po.startDate,
      endDate: po.endDate
    }));

    return reply.status(200).send({
      data: formattedPurchaseOrders,
      pagination: {
        totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error: any) {
    console.error('Error fetching purchase orders:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};

/**
 * Get purchase order by ID with comprehensive details
 */
const getPurchaseOrderById = async (
  request: FastifyRequest<{ Params: { id: number } }>,
  reply: FastifyReply
) => {
  try {
    const { id } = request.params;

    // Find purchase order with basic data
    const purchaseOrder = await AppDataSource.getRepository(PurchaseOrder)
      .createQueryBuilder('po')
      .leftJoinAndSelect('po.purchaseRequest', 'pr')
      .leftJoinAndSelect('po.quotation', 'quotation')
      .leftJoinAndSelect('po.businessUnit', 'businessUnit')
      .leftJoinAndSelect('po.costCenter', 'costCenter')
      .where('po.id = :id', { id })
      .getOne();

    if (!purchaseOrder) {
      return reply.status(404).send({ error: true, message: 'Purchase order not found' });
    }

    // Fetch all related data in parallel for performance
    const [businessUnit, costCenter, poItems, prCreator, approvals, grnItems] = await Promise.all([
      // Get business unit details
      AppDataSource.getRepository(BusinessUnit)
        .findOne({ where: { id: purchaseOrder.businessUnitId } }),

      // Get cost center details including business unit information
      AppDataSource.getRepository(CostCenter)
        .createQueryBuilder('cc')
        .leftJoinAndSelect('cc.businessUnit', 'bu')
        .where('cc.id = :id', { id: purchaseOrder.costCenterId })
        .getOne(),

      // Get PO items with category information
      AppDataSource.getRepository(PoItem)
        .createQueryBuilder('poItem')
        .leftJoinAndSelect('poItem.itemCategory', 'itemCategory')
        .where('poItem.poId = :poId', { poId: purchaseOrder.id })
        .getMany(),

      // Get the creator of the PR
      purchaseOrder.purchaseRequest ?
        AppDataSource.getRepository(User)
          .findOne({ where: { id: purchaseOrder.purchaseRequest.createdBy } })
        : Promise.resolve(null),

      // Get approval workflow details for this PO
      AppDataSource.getRepository(Approval)
        .createQueryBuilder('approval')
        .where('approval.requestId = :prId', { prId: purchaseOrder.prId })
        .andWhere('approval.requestType = :requestType', { requestType: 'PR_APPROVAL' })
        .andWhere('approval.approvalType = :approvalType', { approvalType: 'QUOTE_APPROVAL' })
        .orderBy('approval.approvalSequence', 'ASC')
        .getMany(),

      // Get all GRN items related to this PO's items
      AppDataSource.getRepository(GrnItem)
        .createQueryBuilder('grnItem')
        .where('grnItem.poItemId IN (SELECT poi.id FROM po_items poi WHERE poi.po_id = :poId)', { poId: purchaseOrder.id })
        .getMany()
    ]);

    // Fetch PR items if PR exists
    let prItems: PrItem[] = [];
    if (purchaseOrder.purchaseRequest) {
      prItems = await AppDataSource.getRepository(PrItem)
        .createQueryBuilder('prItem')
        .leftJoinAndSelect('prItem.itemCategory', 'itemCategory')
        .where('prItem.prId = :prId', { prId: purchaseOrder.purchaseRequest.id })
        .getMany();
    }

    // Build enhanced quotation object if it exists
    let enhancedQuotation = null;
    if (purchaseOrder.quotation) {
      enhancedQuotation = {
        id: purchaseOrder.quotation.id,
        vendorId: purchaseOrder.quotation.vendorId,
        vendorName: purchaseOrder.quotation.vendorName,
        paymentTerms: purchaseOrder.quotation.paymentTerms,
        createdAt: purchaseOrder.quotation.createdAt,
        // Add more quotation fields as needed
      };
    }

    // Format creator information to protect sensitive data
    const formattedCreator = prCreator ? {
      id: prCreator.id,
      username: prCreator.username,
      name: prCreator.name || prCreator.username,
      email: prCreator.email,
      // Remove sensitive fields like password, etc.
    } : null;

    // Determine if verification dialog should be shown and to which role
    let showPOVerificationDialog = false;
    let showPOVerificationToRole = null;

    // Find pending approvals with requestType='PR_APPROVAL' and approvalType='QUOTE_APPROVAL'
    const pendingApproval = approvals.find(approval =>
      approval.status === ApprovalStatus.PENDING_APPROVAL
    );

    if (pendingApproval) {
      showPOVerificationDialog = true;
      showPOVerificationToRole = pendingApproval.approvalRole;
    }

    // Format response with comprehensive details
    const formattedPO = {
      message: 'Purchase order retrieved successfully',
      purchaseOrder: {
        id: purchaseOrder.id,
        poNumber: purchaseOrder.poNumber,
        identifier: purchaseOrder.poNumber, // Format: PO-2025-A7001 or PO-2025-RR000
        prId: purchaseOrder.prId,
        type: purchaseOrder.type,
        status: purchaseOrder.status,
        vendorId: purchaseOrder.vendorId,
        expectedDeliveryDate: purchaseOrder.expectedDeliveryDate,
        billTo: purchaseOrder.billTo,
        shipToAddress: purchaseOrder.shipToAddress,
        remarks: purchaseOrder.remarks,
        totalAmount: purchaseOrder.totalAmount,
        currency: purchaseOrder.currency,
        recurrenceFrequency: purchaseOrder.recurrenceFrequency,
        startDate: purchaseOrder.startDate,
        endDate: purchaseOrder.endDate,

        // Business unit details
        businessUnit: businessUnit ? {
          id: businessUnit.id,
          name: businessUnit.name
        } : null,

        // Cost center details
        costCenter: costCenter ? {
          id: costCenter.id,
          name: costCenter.name,
          businessUnitId: costCenter.businessUnitId,
          businessUnit: costCenter.businessUnit
        } : null,

        // Financial details
        paymentTerms: purchaseOrder.paymentTerms,
        deliveryTerms: purchaseOrder.deliveryTerms,

        // Dates and timestamps
        createdBy: purchaseOrder.createdBy,
        createdAt: purchaseOrder.createdAt,
        updatedAt: purchaseOrder.updatedAt,

        // PO items with complete details and GRN quantity tracking
        items: poItems.map(item => {
          // Calculate GRN quantities for this PO item
          const itemGrnItems = grnItems.filter(grnItem => grnItem.poItemId === item.id);
          const grnQuantity = itemGrnItems.reduce((total, grnItem) => total + Number(grnItem.grnQuantity || 0), 0);
          const remainingGrnQuantity = Number(item.quantity) - grnQuantity;

          return {
            id: item.id,
            poId: item.poId,
            itemName: item.itemName,
            itemDescription: item.itemDescription,
            quantity: item.quantity,
            uom: item.uom,
            pricePerUnit: item.pricePerUnit,
            gstPercentage: item.gstPercentage,
            gstAmount: item.gstAmount,
            totalValue: item.totalValue,
            category: item.itemCategory,
            grnQuantity: grnQuantity,
            remainingGrnQuantity: remainingGrnQuantity
          };
        }),

        // Purchase request with complete details
        purchaseRequest: purchaseOrder.purchaseRequest ? {
          id: purchaseOrder.purchaseRequest.id,
          type: purchaseOrder.purchaseRequest.prType,
          status: purchaseOrder.purchaseRequest.status,
          remarks: purchaseOrder.purchaseRequest.remarks,
          createdAt: purchaseOrder.purchaseRequest.createdAt,
          createdBy: purchaseOrder.purchaseRequest.createdBy,
          creator: formattedCreator,
          items: prItems.map(item => {
            // For consistency, also add GRN quantities to PR items
            // Look for matching PO item to get the calculated GRN quantities
            const matchingPoItem = poItems.find(poItem =>
              poItem.itemName === item.itemName && Number(poItem.quantity) === Number(item.quantity));

            // Get GRN quantities from matching PO item or default to 0
            const grnQuantity = matchingPoItem ?
              grnItems
                .filter(grnItem => grnItem.poItemId === matchingPoItem.id)
                .reduce((total, grnItem) => total + Number(grnItem.grnQuantity || 0), 0) : 0;

            const remainingGrnQuantity = Number(item.quantity) - grnQuantity;

            return {
              id: item.id,
              itemName: item.itemName,
              itemDescription: item.itemDescription,
              quantity: item.quantity,
              uom: item.uom,
              estimatedPricePerQuantity: item.estimatedPricePerQuantity,
              category: item.itemCategory,
              grnQuantity: grnQuantity,
              remainingGrnQuantity: remainingGrnQuantity
            };
          })
        } : null,

        // Quotation details
        quotation: enhancedQuotation,

        // Approval workflow
        approvals: approvals.map(approval => ({
          id: approval.id,
          role: approval.approvalRole,
          sequence: approval.approvalSequence,
          status: approval.status,
          remarks: approval.remarks,
          verifiedBy: approval.verifiedBy,
          verifiedAt: approval.verifiedAt
        })),

        // Verification dialog flags
        showPOVerificationDialog,
        showPOVerificationToRole
      }
    };

    return reply.status(200).send(formattedPO);
  } catch (error: any) {
    console.error('Error fetching purchase order:', error);
    return reply.status(500).send({
      error: true,
      message: error.message || 'Internal server error'
    });
  }
};

/**
 * Create a purchase order from an approved quotation
 * This would typically be called by a workflow or trigger
 */
const createPurchaseOrderFromQuotation = async (
  quotationId: number,
  userId: number
): Promise<PurchaseOrder> => {
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Get the quotation with related PR
    const quotation = await queryRunner.manager.findOne(Quotation, {
      where: { id: quotationId },
      relations: ['purchaseRequest']
    });

    if (!quotation) {
      throw new Error(`Quotation with ID ${quotationId} not found`);
    }

    // Get the purchase request with items
    const purchaseRequest = await queryRunner.manager.findOne(PurchaseRequest, {
      where: { id: quotation.purchaseRequest.id },
      relations: ['items', 'items.itemCategory']
    });

    if (!purchaseRequest) {
      throw new Error(`Purchase request with ID ${quotation.purchaseRequest.id} not found`);
    }

    // Verify that there are at least 3 quotations for this PR
    const quotationsCount = await queryRunner.manager.count(Quotation, {
      where: { prId: quotation.purchaseRequest.id }
    });

    if (quotationsCount < 3) {
      throw new Error(`At least 3 vendor quotes are required before creating a purchase order. Current count: ${quotationsCount}`);
    }

    // Generate PO number
    const poNumber = await generatePONumber(purchaseRequest.businessUnitId);

    // Determine PO type based on PR type
    const poType = purchaseRequest.prType.toLowerCase().includes('recurring')
      ? PurchaseOrderType.RECURRING
      : PurchaseOrderType.ONE_TIME;

    // Create new purchase order
    const purchaseOrder = new PurchaseOrder();
    purchaseOrder.poNumber = poNumber;
    purchaseOrder.prId = purchaseRequest.id;
    purchaseOrder.quotationId = quotation.id;
    purchaseOrder.type = poType;
    purchaseOrder.status = PurchaseOrderStatus.PENDING_APPROVAL;
    purchaseOrder.vendorId = quotation.vendorId || 0;
    purchaseOrder.businessUnitId = purchaseRequest.businessUnitId;
    purchaseOrder.costCenterId = purchaseRequest.costCenterId;
    purchaseOrder.paymentTerms = quotation.paymentTerms || 'Net 30';
    // Set a default total amount, will update after items are created
    purchaseOrder.totalAmount = 0;
    purchaseOrder.currency = 'INR'; // Default to INR, can be made configurable
    purchaseOrder.createdBy = userId;

    // If recurring, set additional fields
    if (poType === PurchaseOrderType.RECURRING) {
      // Default to monthly if not specified
      purchaseOrder.recurrenceFrequency = RecurrenceFrequency.MONTHLY;

      // Set start date to today
      purchaseOrder.startDate = new Date();

      // Set end date to 1 year from now by default
      const endDate = new Date();
      endDate.setFullYear(endDate.getFullYear() + 1);
      purchaseOrder.endDate = endDate;
    }

    // Save the purchase order first
    const savedPO = await queryRunner.manager.save(purchaseOrder);

    // Set the total amount to be calculated after items are saved
    let calculatedTotal = 0;

    // Create PO items from PR items
    if (purchaseRequest.items && purchaseRequest.items.length > 0) {
      const poItems = purchaseRequest.items.map(prItem => {
        const poItem = new PoItem();
        poItem.poId = savedPO.id;

        // Handle nullable fields safely
        if (prItem.itemCategoryId) {
          poItem.itemCategoryId = prItem.itemCategoryId;
        }

        poItem.itemName = prItem.itemName || '';
        poItem.itemDescription = prItem.itemDescription || '';
        poItem.quantity = prItem.quantity || 0;
        poItem.uom = prItem.uom || '';
        poItem.pricePerUnit = prItem.estimatedPricePerQuantity || 0;
        poItem.gstPercentage = 18; // Default GST rate, can be made configurable
        poItem.gstAmount = (poItem.pricePerUnit * poItem.quantity * poItem.gstPercentage) / 100;
        poItem.totalValue = (poItem.pricePerUnit * poItem.quantity) + poItem.gstAmount;
        return poItem;
      });

      await queryRunner.manager.save(poItems);

      // Calculate total from the PO items
      calculatedTotal = poItems.reduce((sum, item) => sum + Number(item.totalValue || 0), 0);

      // Update the PO with the calculated total
      savedPO.totalAmount = calculatedTotal;
      await queryRunner.manager.save(savedPO);
    }
    // Commit transaction
    await queryRunner.commitTransaction();
    
    return savedPO;
  } catch (error: any) {
    // Rollback transaction on error
    await queryRunner.rollbackTransaction();
    console.error('Error creating purchase order:', error);
    throw error;
  } finally {
    // Release query runner
    await queryRunner.release();
  }
};

/**
 * Update purchase order status
 */
const updatePurchaseOrderStatus = async (
  request: FastifyRequest<{
    Params: { id: number },
    Body: { status: PurchaseOrderStatus, remarks?: string }
  }>,
  reply: FastifyReply
) => {
  try {
    const { id } = request.params;
    const { status, remarks } = request.body;
    const userId = (request.user as User).id;

    // Find purchase order
    const purchaseOrder = await AppDataSource.getRepository(PurchaseOrder)
      .findOne({ where: { id } });

    if (!purchaseOrder) {
      return reply.status(404).send({ message: 'Purchase order not found' });
    }

    // Update status
    purchaseOrder.status = status;

    // Save updated purchase order with optional remarks
    const updateData = {
      ...purchaseOrder,
      status,
      // Store the remarks in the existing remarks field
      ...(remarks && { remarks: remarks })
    };

    await AppDataSource.getRepository(PurchaseOrder).save(updateData);

    return reply.status(200).send({
      message: `Purchase order status updated to ${status}`,
      purchaseOrder: {
        id: purchaseOrder.id,
        poNumber: purchaseOrder.poNumber,
        status: purchaseOrder.status,
        updatedAt: purchaseOrder.updatedAt
      }
    });
  } catch (error: any) {
    console.error('Error updating purchase order status:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};

/**
 * Get counts of all purchase order statuses
 * This endpoint provides counts for each status of purchase orders
 */
const getPurchaseOrderStatusCounts = async (
  request: FastifyRequest & { user?: User, userRoles?: UserRole[] },
  reply: FastifyReply
) => {
  try {
    // Ensure user is authenticated
    if (!request.user) {
      return reply.status(401).send({ error: true, message: 'Authentication required' });
    }

    const userId = request.user.id;
    const userRoles = request.userRoles || [];

    // Create query builder for base query
    let queryBuilder = AppDataSource
      .getRepository(PurchaseOrder)
      .createQueryBuilder('po')
      .leftJoin('po.purchaseRequest', 'pr');

    // Apply role-based filtering similar to getAllPurchaseOrders
    if (userRoles.length > 0) {
      // Check if user has PROCUREMENT_MANAGER or BIZ_FIN role
      const hasProcurementOrBizFinRole = userRoles.some(role =>
        role.role === ApprovalRole.PROCUREMENT_MANAGER || role.role === ApprovalRole.BIZ_FIN || role.role === ApprovalRole.FINANCE
      );

      if (hasProcurementOrBizFinRole) {
        // PROCUREMENT_MANAGER, BIZ_FIN, and FINANCE can see all POs
        // No additional filtering needed
      } else {
        // Check if user has CC_HEAD role
        const ccHeadRoles = userRoles.filter(role => role.role === ApprovalRole.CC_HEAD);

        if (ccHeadRoles.length > 0) {
          // CC_HEAD can see POs for their cost centers or POs created from PRs they initiated
          const costCenterIds = ccHeadRoles
            .filter(role => role.costCenterId !== null && role.costCenterId !== undefined)
            .map(role => role.costCenterId);

          if (costCenterIds.length > 0) {
            // Find POs either with matching cost center OR PRs created by the user
            queryBuilder = queryBuilder.andWhere(
              '(po.costCenterId IN (:...costCenterIds) OR pr.createdBy = :userId)',
              { costCenterIds, userId }
            );
          } else {
            // If CC_HEAD has no assigned cost centers, they can only see POs from PRs they created
            queryBuilder = queryBuilder.andWhere('pr.createdBy = :userId', { userId });
          }
        } else {
          // Regular user can only see POs related to PRs they created
          queryBuilder = queryBuilder.andWhere('pr.createdBy = :userId', { userId });
        }
      }
    } else {
      // Regular user can only see POs related to PRs they created
      queryBuilder = queryBuilder.andWhere('pr.createdBy = :userId', { userId });
    }

    // Get all the possible statuses from the enum
    const statuses = Object.values(PurchaseOrderStatus);
    
    // Create an array of promises for each status count
    const countPromises = statuses.map(async (status) => {
      const count = await queryBuilder
        .clone()
        .andWhere('po.status = :status', { status })
        .getCount();
      
      return { status, count };
    });
    
    // Wait for all count queries to complete
    const statusCounts = await Promise.all(countPromises);
    
    // Calculate total count
    const totalCount = statusCounts.reduce((sum, statusCount) => sum + statusCount.count, 0);
    
    // Format the response
    const response = {
      total: totalCount,
      counts: statusCounts.reduce((obj, item) => {
        obj[item.status] = item.count;
        return obj;
      }, {} as Record<string, number>)
    };
    
    return reply.status(200).send(response);
  } catch (error) {
    console.error('Error getting purchase order status counts:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};

export {
  getAllPurchaseOrders,
  getPurchaseOrderById,
  createPurchaseOrderFromQuotation,
  updatePurchaseOrderStatus,
  getPurchaseOrderStatusCounts
};
