import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../db/typeorm.config';
import { Quotation } from '../entities/Quotation.entity';
import { Approval } from '../entities/Approval.entity';
import { PurchaseRequest } from '../entities/PurchaseRequest.entity';
import { PurchaseOrder, PurchaseOrderStatus, PurchaseOrderType } from '../entities/PurchaseOrder.entity';
import { PoItem } from '../entities/PoItem.entity';
import { User } from '../entities/User.entity';
import { Document } from '../entities/document.entity';
import { ApprovalRole, ApprovalStatus, ApprovalType, RequestType } from '../constants/enums';
import { documentService } from '../services/document.service';
import s3Service from '../services/s3.service';
import { EntityType } from '../schemas/document.schema';
import { IsNull } from 'typeorm';

// Get all quotations
export const getAllQuotations = async (
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> => {
  try {
    const quotationRepo = AppDataSource.getRepository(Quotation);
    const quotations = await quotationRepo.find({
      where: {
        deletedAt: IsNull()
      },
      order: {
        createdAt: 'DESC'
      }
    });

    // Prepare enhanced quotations with file URLs and documents
    const enhancedQuotations: Record<string, any>[] = [];
    
    // Get all quotation IDs
    const quotationIds = quotations.map(q => q.id);
    
    // Batch fetch all documents for all quotations in a single query
    let documentsByQuotationId: Record<number, Document[]> = {};
    
    if (quotationIds.length > 0) {
      try {
        const documentRepo = AppDataSource.getRepository(Document);
        const allDocuments = await documentRepo
          .createQueryBuilder('doc')
          .where('doc.entityType = :entityType AND doc.entityId IN (:...quotationIds)', {
            entityType: EntityType.QUOTATION,
            quotationIds
          })
          .getMany();
        
        // Generate pre-signed URLs for all documents in parallel
        await Promise.all(allDocuments.map(async (doc) => {
          if (doc.fileKey) {
            try {
              doc.fileUrl = await s3Service.getFileUrl(doc.fileKey);
            } catch (error) {
              console.error(`Error generating URL for file ${doc.fileKey}:`, error);
              doc.fileUrl = ''; // Use empty string instead of null
            }
          }
        }));
        
        // Group documents by quotation ID
        documentsByQuotationId = allDocuments.reduce<Record<number, Document[]>>((acc, doc) => {
          const quotationId = doc.entityId;
          if (!acc[quotationId]) {
            acc[quotationId] = [];
          }
          acc[quotationId].push(doc);
          return acc;
        }, {});
      } catch (error) {
        console.error('Error batch fetching documents for quotations:', error);
      }
    }
    
    // Create enhanced quotation objects using the pre-fetched documents
    for (const quotation of quotations) {
      const documents = documentsByQuotationId[quotation.id] || [];
      
      const enhancedQuotation: Record<string, any> = {
        ...quotation,
        fileUrl: documents.length > 0 ? documents[0].fileUrl : null,
        documents: documents,
        attachments: documents
      };
      
      enhancedQuotations.push(enhancedQuotation);
    }

    return reply.status(200).send(enhancedQuotations);
  } catch (error) {
    console.error('Error fetching quotations:', error);
    return reply.status(500).send({
      error: true,
      message: 'Failed to fetch quotations'
    });
  }
};

// Get quotations by purchase request ID
export const getQuotationsByPrId = async (
  request: FastifyRequest<{
    Params: {
      prId: string;
    };
  }>,
  reply: FastifyReply
): Promise<void> => {
  try {
    const { prId } = request.params;
    const prIdNum = parseInt(prId, 10);

    // Validate if PR exists
    const prRepo = AppDataSource.getRepository(PurchaseRequest);
    const pr = await prRepo.findOne({
      where: { id: prIdNum }
    });

    if (!pr) {
      return reply.status(404).send({
        error: true,
        message: 'Purchase request not found'
      });
    }

    // Get quotations for this PR
    const quotationRepo = AppDataSource.getRepository(Quotation);
    const quotations = await quotationRepo.find({
      where: {
        prId: prIdNum,
        deletedAt: IsNull()
      },
      order: {
        createdAt: 'DESC'
      }
    });

    // Separately fetch all documents for these quotations to avoid N+1 queries
    const quotationIds = quotations.map(q => q.id);

    // Create a map to efficiently look up documents by quotation ID
    const documentsByQuotationId = new Map();

    // Fetch all documents for all quotations in a single query
    try {
      if (quotationIds.length > 0) {
        // Use the Document repository directly
        const documentRepo = AppDataSource.getRepository('Document');

        // Check if there are any documents in the database for any entity type
        const allDocuments = await documentRepo.query(
          `SELECT entityType, COUNT(*) as count FROM documents GROUP BY entityType`
        );

        // Check if there are any documents specifically for these quotations
        const quotationDocuments = await documentRepo.query(
          `SELECT entityId, COUNT(*) as count FROM documents WHERE entityType = ? GROUP BY entityId`,
          [EntityType.QUOTATION]
        );

        // Use a more reliable query format for MySQL when dealing with IN clauses
        let query = `SELECT * FROM documents WHERE entityType = ? AND entityId IN (`;
        query += quotationIds.map(() => '?').join(',');
        query += `)`;


        const rawDocuments = await documentRepo.query(
          query,
          [EntityType.QUOTATION, ...quotationIds]
        );
        console.log(`Raw SQL query found ${rawDocuments?.length || 0} documents:`,
          rawDocuments.map((d: any) => ({ id: d.id, fileKey: d.fileKey, fileName: d.fileName, entityId: d.entityId })));

        // Group documents by quotation ID
        for (const doc of rawDocuments) {
          const quotationId = doc.entityId;
          if (!documentsByQuotationId.has(quotationId)) {
            documentsByQuotationId.set(quotationId, []);
          }

          // Generate pre-signed URL for the document
          try {
            doc.fileUrl = await s3Service.getFileUrl(doc.fileKey);
          } catch (urlError) {
            console.error(`Error generating URL for file key ${doc.fileKey}:`, urlError);
            doc.fileUrl = null;
          }

          // Convert snake_case to camelCase for consistency and ensure all required fields are present
          const formattedDoc = {
            id: doc.id,
            fileKey: doc.fileKey,
            fileName: doc.fileName,
            fileSize: doc.fileSize || 0, // Ensure fileSize is a number
            contentType: doc.contentType || 'application/octet-stream', // Provide default content type
            fileUrl: doc.fileUrl || '', // Ensure fileUrl is a string
            entityType: doc.entityType, // This should match one of the EntityType enum values
            entityId: doc.entityId,
            displayName: doc.displayName || doc.fileName, // Use fileName as fallback
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt
          };

          documentsByQuotationId.get(quotationId).push(formattedDoc);
        }
      }
    } catch (error) {
      console.error('Error fetching documents for quotations:', error);
    }

    // Now create enhanced quotation objects
    const enhancedQuotations = await Promise.all(quotations.map(async quotation => {
      // Get any documents for this quotation
      let documents = documentsByQuotationId.get(quotation.id) || [];

      // If no documents were found using the map, try fetching them directly
      if (documents.length === 0) {
        try {
          console.log(`No documents found in map for quotation ${quotation.id}, fetching directly...`);
          documents = await documentService.getDocumentsByEntity(EntityType.QUOTATION, quotation.id);
          console.log(`Directly fetched ${documents.length} documents for quotation ${quotation.id}`);
        } catch (error) {
          console.error(`Error fetching documents directly for quotation ${quotation.id}:`, error);
          documents = [];
        }
      }

      // Log document details for debugging
      if (documents.length > 0) {
        console.log(`Documents for quotation ${quotation.id}:`,
          documents.map((d: any) => ({
            id: d.id,
            fileKey: d.fileKey,
            fileUrl: d.fileUrl ? d.fileUrl.substring(0, 30) + '...' : 'null'
          }))
        );
      }

      // Create a plain JS object (not an entity)
      return {
        id: quotation.id,
        prId: quotation.prId,
        vendorId: quotation.vendorId,
        vendorName: quotation.vendorName,
        paymentTerms: quotation.paymentTerms,
        deliveryTerms: quotation.deliveryTerms,
        remarks: quotation.remarks,
        createdAt: quotation.createdAt,
        updatedAt: quotation.updatedAt,
        fileUrl: documents.length > 0 ? documents[0].fileUrl : null,
        // Include all attachment details, ensuring they match the schema
        attachments: documents.map((doc: any) => ({
          id: doc.id,
          fileKey: doc.fileKey,
          fileUrl: doc.fileUrl || '', // Ensure fileUrl is a string
          fileName: doc.fileName,
          fileSize: doc.fileSize || 0, // Ensure fileSize is a number
          contentType: doc.contentType || 'application/octet-stream', // Provide default content type
          entityType: doc.entityType, // This should be one of the EntityType enum values
          entityId: doc.entityId,
          displayName: doc.displayName || doc.fileName, // Use fileName as fallback
          createdAt: doc.createdAt,
          updatedAt: doc.updatedAt
        })),
        // Keep documents for backward compatibility, ensuring they match the schema
        documents: documents.map((doc: any) => ({
          id: doc.id,
          fileKey: doc.fileKey,
          fileUrl: doc.fileUrl || '', // Ensure fileUrl is a string
          fileName: doc.fileName,
          fileSize: doc.fileSize || 0, // Ensure fileSize is a number
          contentType: doc.contentType || 'application/octet-stream', // Provide default content type
          entityType: doc.entityType, // This should be one of the EntityType enum values
          entityId: doc.entityId,
          displayName: doc.displayName || doc.fileName, // Use fileName as fallback
          createdAt: doc.createdAt,
          updatedAt: doc.updatedAt
        }))
      };
    }));

    // Log the enhanced quotations for debugging
    console.log(`Sending ${enhancedQuotations.length} quotations with attachments`);

    // Send the response directly without JSON.stringify
    // This allows Fastify to handle the serialization properly
    return reply.status(200).send(enhancedQuotations);
  } catch (error) {
    console.error('Error fetching quotations by PR ID:', error);
    return reply.status(500).send({
      error: true,
      message: 'Failed to fetch quotations'
    });
  }
};

// Get quotation by ID
export const getQuotationById = async (
  request: FastifyRequest<{
    Params: {
      id: string;
    };
  }>,
  reply: FastifyReply
): Promise<void> => {
  try {
    const { id } = request.params;
    const idNum = parseInt(id, 10);

    // Get the quotation
    const quotationRepo = AppDataSource.getRepository(Quotation);
    const quotation = await quotationRepo.findOne({
      where: { 
        id: idNum
      }
    });

    if (!quotation) {
      return reply.status(404).send({
        error: true,
        message: 'Quotation not found'
      });
    }

    // Create enhanced quotation with file URLs and documents
    const enhancedQuotation: Record<string, any> = {
      ...quotation,
      fileUrl: null,
      documents: [],
      attachments: []
    };

    try {
      // Use direct SQL query for better performance and reliability
      const documentRepo = AppDataSource.getRepository('Document');
      const rawDocuments = await documentRepo.query(
        `SELECT * FROM documents WHERE entityType = ? AND entityId = ?`,
        [EntityType.QUOTATION, quotation.id]
      );
      console.log(`Raw SQL query found ${rawDocuments?.length || 0} documents for quotation ${quotation.id}`);

      if (rawDocuments && rawDocuments.length > 0) {
        // Process the documents
        const processedDocuments = [];

        for (const doc of rawDocuments) {
          // Generate pre-signed URL for the document
          try {
            doc.fileUrl = await s3Service.getFileUrl(doc.file_key);
          } catch (urlError) {
            console.error(`Error generating URL for file key ${doc.file_key}:`, urlError);
            doc.fileUrl = null;
          }

          // Convert snake_case to camelCase for consistency
          const formattedDoc = {
            id: doc.id,
            fileKey: doc.file_key,
            fileName: doc.file_name,
            fileSize: doc.file_size,
            contentType: doc.content_type,
            fileUrl: doc.fileUrl,
            entityType: doc.entityType,
            entityId: doc.entityId,
            displayName: doc.displayName,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt
          };

          processedDocuments.push(formattedDoc);
        }

        // Set the first document's URL as the main fileUrl
        enhancedQuotation.fileUrl = processedDocuments[0].fileUrl;

        // Add documents to both attachments and documents fields, ensuring they match the schema
        enhancedQuotation.attachments = processedDocuments.map(doc => ({
          id: doc.id,
          fileKey: doc.fileKey,
          fileUrl: doc.fileUrl || '', // Ensure fileUrl is a string
          fileName: doc.fileName,
          fileSize: doc.fileSize || 0, // Ensure fileSize is a number
          contentType: doc.contentType || 'application/octet-stream', // Provide default content type
          entityType: doc.entityType, // This should be one of the EntityType enum values
          entityId: doc.entityId,
          displayName: doc.displayName || doc.fileName, // Use fileName as fallback
          createdAt: doc.createdAt,
          updatedAt: doc.updatedAt
        }));

        // Keep documents for backward compatibility, ensuring they match the schema
        enhancedQuotation.documents = processedDocuments.map(doc => ({
          id: doc.id,
          fileKey: doc.fileKey,
          fileUrl: doc.fileUrl || '', // Ensure fileUrl is a string
          fileName: doc.fileName,
          fileSize: doc.fileSize || 0, // Ensure fileSize is a number
          contentType: doc.contentType || 'application/octet-stream', // Provide default content type
          entityType: doc.entityType, // This should be one of the EntityType enum values
          entityId: doc.entityId,
          displayName: doc.displayName || doc.fileName, // Use fileName as fallback
          createdAt: doc.createdAt,
          updatedAt: doc.updatedAt
        }));
      }
    } catch (error) {
      console.error(`Error fetching documents for quotation ${quotation.id}:`, error);
    }

    return reply.status(200).send(enhancedQuotation);
  } catch (error) {
    console.error('Error fetching quotation by ID:', error);
    return reply.status(500).send({
      error: true,
      message: 'Failed to fetch quotation'
    });
  }
};

// Create a new quotation
export const createQuotation = async (
  request: FastifyRequest<{
    Body: {
      prId: number;
      vendorId?: number;
      vendorName: string;
      paymentTerms?: string;
      deliveryTerms?: string;
      remarks?: string;
      fileKey?: string; // For backward compatibility (single file)
      fileKeys?: string[]; // For multiple files
    };
  }> & { user?: User },
  reply: FastifyReply
): Promise<void> => {
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    const userId = request.user?.id;

    if (!userId) {
      return reply.status(401).send({
        error: true,
        message: 'User not authenticated'
      });
    }

    // Validate if PR exists and is in the right state (after procurement manager approval)
    const prRepo = queryRunner.manager.getRepository(PurchaseRequest);
    const pr = await prRepo.findOne({
      where: { id: request.body.prId }
    });

    if (!pr) {
      return reply.status(404).send({
        error: true,
        message: 'Purchase request not found'
      });
    }

    // Check if procurement manager has approved this PR
    const approvalRepo = queryRunner.manager.getRepository(Approval);
    const procurementApproval = await approvalRepo.findOne({
      where: {
        requestId: pr.id,
        requestType: RequestType.PURCHASE_REQUEST,
        approvalType: ApprovalType.PR_APPROVAL,
        approvalRole: ApprovalRole.PROCUREMENT_MANAGER,
        status: ApprovalStatus.APPROVED
      }
    });

    if (!procurementApproval) {
      return reply.status(400).send({
        error: true,
        message: 'Cannot create quotation: Purchase request has not been approved by procurement manager'
      });
    }

    // Create new quotation
    const quotation = new Quotation();
    quotation.prId = request.body.prId;
    quotation.vendorId = request.body.vendorId || null;
    quotation.vendorName = request.body.vendorName;
    quotation.paymentTerms = request.body.paymentTerms || '';
    quotation.deliveryTerms = request.body.deliveryTerms || '';
    quotation.remarks = request.body.remarks || '';

    const savedQuotation = await queryRunner.manager.getRepository(Quotation).save(quotation);

    // Handle file attachments
    try {
      console.log("Processing file attachments for quotation:", savedQuotation.id);

      // If this is a multipart form request, try to upload the file directly
      if (request.headers['content-type']?.includes('multipart/form-data')) {
        try {
          console.log("Multipart form data detected, attempting to upload file");
          const uploadResult = await s3Service.uploadFile(request);

          if (uploadResult) {
            console.log("File uploaded successfully:", uploadResult.fileKey);

            // Create document record
            await documentService.createDocument({
              entityType: EntityType.QUOTATION,
              entityId: savedQuotation.id,
              fileKey: uploadResult.fileKey,
              fileName: uploadResult.fileName,
              displayName: `Quotation document for ${quotation.vendorName}`,
              contentType: uploadResult.contentType,
              fileSize: uploadResult.fileSize
            });

            console.log("Document record created successfully for uploaded file");
          }
        } catch (uploadError) {
          console.error("Error uploading file:", uploadError);
          // Continue with other file handling methods
        }
      }

      // Handle file keys from the request body (for backward compatibility)
      let allFileKeys: string[] = [];

      // Process fileKey (string) - single file for backward compatibility
      if (request.body.fileKey && typeof request.body.fileKey === 'string') {
        allFileKeys.push(request.body.fileKey);
      }

      // Process fileKeys (array) - for multiple files
      if (request.body.fileKeys && Array.isArray(request.body.fileKeys)) {
        allFileKeys = [...allFileKeys, ...request.body.fileKeys];
      }

      // Remove duplicates if any
      allFileKeys = [...new Set(allFileKeys)];

      // Create document records for each file key
      if (allFileKeys.length > 0) {
        console.log(`Processing ${allFileKeys.length} file keys for quotation ${savedQuotation.id}`);

        try {
          // Find existing document records to get file metadata
          const existingDocuments = await AppDataSource.getRepository(Document)
            .createQueryBuilder('doc')
            .where('doc.fileKey IN (:...fileKeys)', { fileKeys: allFileKeys })
            .getMany();

          console.log(`Found ${existingDocuments.length} existing document records`);

          // Create a map of file keys to document metadata
          const documentMap: Record<string, { fileName: string; fileSize: number; contentType: string }> = {};
          existingDocuments.forEach(doc => {
            documentMap[doc.fileKey] = {
              fileName: doc.fileName || 'Attachment',
              fileSize: doc.fileSize || 0,
              contentType: doc.contentType || 'application/octet-stream'
            };
          });

          const documentRepo = queryRunner.manager.getRepository(Document);
          const documentPromises = allFileKeys.map(async fileKey => {
            // Create document record with proper metadata if available
            const metadata = documentMap[fileKey] || {
              fileName: fileKey.split('/').pop() || 'quotation-document',
              fileSize: 0,
              contentType: 'application/pdf'
            };

            const document = documentRepo.create({
              entityType: EntityType.QUOTATION,
              entityId: savedQuotation.id,
              fileKey,
              fileName: metadata.fileName,
              displayName: `Quotation document for ${quotation.vendorName}`,
              fileSize: metadata.fileSize,
              contentType: metadata.contentType
            });

            return await queryRunner.manager.save(document);
          });

          await Promise.all(documentPromises);
          console.log(`Attached ${allFileKeys.length} files to quotation #${savedQuotation.id}`);
        } catch (error) {
          console.error('Error attaching files to quotation:', error);
          // Continue with quotation creation even if file attachment fails
        }
      }
    } catch (error) {
      console.error('Error creating document for quotation:', error);
      // We don't want to fail the quotation creation if document fails
    }
    await queryRunner.commitTransaction();

    // Get documents with file URLs for the response
    try {
      const documents = await documentService.getDocumentsByEntity(EntityType.QUOTATION, savedQuotation.id);

      // Add documents to the response
      return reply.status(201).send({
        message: 'Quotation created successfully',
        quotation: {
          ...savedQuotation,
          fileUrl: documents.length > 0 ? documents[0].fileUrl : null,
          attachments: documents
        }
      });
    } catch (error) {
      console.error(`Error fetching documents for quotation ${savedQuotation.id}:`, error);

      // Return response without documents if there was an error
      return reply.status(201).send({
        message: 'Quotation created successfully',
        quotation: savedQuotation
      });
    }
  } catch (error) {
    await queryRunner.rollbackTransaction();
    console.error('Error creating quotation:', error);
    return reply.status(500).send({
      error: true,
      message: 'Failed to create quotation'
    });
  } finally {
    await queryRunner.release();
  }
};

// Update an existing quotation
export const updateQuotation = async (
  request: FastifyRequest<{
    Params: {
      id: string;
    };
    Body: {
      vendorId?: number | null;
      vendorName?: string;
      paymentTerms?: string;
      deliveryTerms?: string;
      remarks?: string;
      fileKey?: string; // For backward compatibility (single file)
      fileKeys?: string[]; // For multiple files
    };
  }> & { user?: User },
  reply: FastifyReply
): Promise<void> => {
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    const { id } = request.params;
    const idNum = parseInt(id, 10);
    const userId = request.user?.id;

    if (!userId) {
      return reply.status(401).send({
        error: true,
        message: 'User not authenticated'
      });
    }

    // Check if quotation exists and is not deleted
    const quotationRepo = queryRunner.manager.getRepository(Quotation);
    const quotation = await quotationRepo.findOne({
      where: { 
        id: idNum,
        deletedAt: IsNull()
      }
    });

    if (!quotation) {
      return reply.status(404).send({
        error: true,
        message: 'Quotation not found'
      });
    }

    // Update fields if provided
    if (request.body.vendorId !== undefined) quotation.vendorId = request.body.vendorId;
    if (request.body.vendorName) quotation.vendorName = request.body.vendorName;
    if (request.body.paymentTerms !== undefined) quotation.paymentTerms = request.body.paymentTerms;
    if (request.body.deliveryTerms !== undefined) quotation.deliveryTerms = request.body.deliveryTerms;
    if (request.body.remarks !== undefined) quotation.remarks = request.body.remarks;

    await quotationRepo.save(quotation);

    // Handle file attachments
    try {
      console.log("Processing file attachments for quotation update:", quotation.id);

      // If this is a multipart form request, try to upload the file directly
      if (request.headers['content-type']?.includes('multipart/form-data')) {
        try {
          console.log("Multipart form data detected, attempting to upload file");
          const uploadResult = await s3Service.uploadFile(request);

          if (uploadResult) {
            console.log("File uploaded successfully:", uploadResult.fileKey);

            // Create document record
            await documentService.createDocument({
              entityType: EntityType.QUOTATION,
              entityId: quotation.id,
              fileKey: uploadResult.fileKey,
              fileName: uploadResult.fileName,
              displayName: `Updated quotation document for ${quotation.vendorName}`,
              contentType: uploadResult.contentType,
              fileSize: uploadResult.fileSize
            });

            console.log("Document record created successfully for uploaded file");
          }
        } catch (uploadError) {
          console.error("Error uploading file:", uploadError);
          // Continue with other file handling methods
        }
      }

      // Handle file keys from the request body (for backward compatibility)
      let allFileKeys: string[] = [];

      // Process fileKey (string) - single file for backward compatibility
      if (request.body.fileKey && typeof request.body.fileKey === 'string') {
        allFileKeys.push(request.body.fileKey);
      }

      // Process fileKeys (array) - for multiple files
      if (request.body.fileKeys && Array.isArray(request.body.fileKeys)) {
        allFileKeys = [...allFileKeys, ...request.body.fileKeys];
      }

      // Remove duplicates if any
      allFileKeys = [...new Set(allFileKeys)];

      // Create document records for each file key
      if (allFileKeys.length > 0) {

        try {
          // Find existing document records to get file metadata
          const existingDocuments = await AppDataSource.getRepository(Document)
            .createQueryBuilder('doc')
            .where('doc.fileKey IN (:...fileKeys)', { fileKeys: allFileKeys })
            .getMany();

          // Create a map of file keys to document metadata
          const documentMap: Record<string, { fileName: string; fileSize: number; contentType: string }> = {};
          existingDocuments.forEach(doc => {
            documentMap[doc.fileKey] = {
              fileName: doc.fileName || 'Attachment',
              fileSize: doc.fileSize || 0,
              contentType: doc.contentType || 'application/octet-stream'
            };
          });

          const documentRepo = queryRunner.manager.getRepository(Document);
          const documentPromises = allFileKeys.map(async fileKey => {
            // Create document record with proper metadata if available
            const metadata = documentMap[fileKey] || {
              fileName: fileKey.split('/').pop() || 'updated-quotation-document',
              fileSize: 0,
              contentType: 'application/pdf'
            };

            const document = documentRepo.create({
              entityType: EntityType.QUOTATION,
              entityId: quotation.id,
              fileKey,
              fileName: metadata.fileName,
              displayName: `Updated quotation document for ${quotation.vendorName}`,
              fileSize: metadata.fileSize,
              contentType: metadata.contentType
            });

            return await queryRunner.manager.save(document);
          });

          await Promise.all(documentPromises);
          console.log(`Attached ${allFileKeys.length} files to quotation #${quotation.id}`);
        } catch (error) {
          console.error('Error attaching files to quotation:', error);
          // Continue with quotation update even if file attachment fails
        }
      }
    } catch (error) {
      console.error('Error updating document for quotation:', error);
      // We don't want to fail the quotation update if document fails
    }

    await queryRunner.commitTransaction();

    // Get updated quotation with documents
    try {
      const updatedQuotation = await quotationRepo.findOne({
        where: { id: idNum }
      });

      if (!updatedQuotation) {
        return reply.status(404).send({
          error: true,
          message: 'Quotation not found after update'
        });
      }

      // Get documents with file URLs
      const documents = await documentService.getDocumentsByEntity(EntityType.QUOTATION, idNum);

      // Add documents to the response
      return reply.status(200).send({
        message: 'Quotation updated successfully',
        quotation: {
          ...updatedQuotation,
          fileUrl: documents.length > 0 ? documents[0].fileUrl : null,
          attachments: documents
        }
      });
    } catch (error) {
      console.error(`Error fetching documents for updated quotation ${idNum}:`, error);

      // Return basic response if there was an error
      return reply.status(200).send({
        message: 'Quotation updated successfully',
        quotation: { id: idNum }
      });
    }
  } catch (error) {
    await queryRunner.rollbackTransaction();
    console.error('Error updating quotation:', error);
    return reply.status(500).send({
      error: true,
      message: 'Failed to update quotation'
    });
  } finally {
    await queryRunner.release();
  }
};

// Delete a quotation (soft delete)
export const deleteQuotation = async (
  request: FastifyRequest<{
    Params: {
      id: string;
    };
  }> & { user?: User },
  reply: FastifyReply
): Promise<void> => {
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    const { id } = request.params;
    const idNum = parseInt(id, 10);
    const userId = request.user?.id;

    if (!userId) {
      return reply.status(401).send({
        error: true,
        message: 'User not authenticated'
      });
    }

    // Check if quotation exists and is not already deleted
    const quotationRepo = queryRunner.manager.getRepository(Quotation);
    const quotation = await quotationRepo.findOne({
      where: { 
        id: idNum,
        deletedAt: IsNull()
      }
    });

    if (!quotation) {
      return reply.status(404).send({
        error: true,
        message: 'Quotation not found or already deleted'
      });
    }

    // Check if any purchase orders reference this quotation
    const poRepo = queryRunner.manager.getRepository(PurchaseOrder);
    const linkedPO = await poRepo.findOne({
      where: { quotationId: idNum }
    });

    if (linkedPO) {
      return reply.status(400).send({
        error: true,
        message: 'Cannot delete quotation: linked Purchase Order(s) exist',
      });
    }

    // Soft delete the quotation by updating deletedAt timestamp
    await quotationRepo.update(idNum, {
      deletedAt: new Date()
    });
    
    await queryRunner.commitTransaction();

    return reply.status(200).send({
      message: 'Quotation deleted successfully'
    });
  } catch (error) {
    await queryRunner.rollbackTransaction();
    console.error('Error deleting quotation:', error);
    return reply.status(500).send({
      error: true,
      message: 'Failed to delete quotation'
    });
  } finally {
    await queryRunner.release();
  }
};

// Approve a quotation
export const approveQuotation = async (
  request: FastifyRequest<{
    Params: {
      id: string;
    };
    Body: {
      status: ApprovalStatus;
      remarks: string;
      poItems: {
        prItemId: number;
        itemName: string;
        itemType: string;
        description: string;
        quantity: number;
        finalizedQuantity: number;
        uom: string;
        pricePerUnit: number;
        finalizedPricePerUnit: number;
        gstPercentage: number;
        gstAmount: number;
        totalAmount: number;
      }[];
    };
  }> & { user?: User },
  reply: FastifyReply
): Promise<void> => {
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    const { id } = request.params;
    const idNum = parseInt(id, 10);
    const { status } = request.body;
    const userId = request.user?.id;

    if (!userId) {
      return reply.status(401).send({
        error: true,
        message: 'User not authenticated'
      });
    }

    // Check if quotation exists and is not deleted
    const quotationRepo = queryRunner.manager.getRepository(Quotation);
    const quotation = await quotationRepo.findOne({
      where: { 
        id: idNum,
        deletedAt: IsNull()
      }
    });

    if (!quotation) {
      return reply.status(404).send({
        error: true,
        message: 'Quotation not found or already deleted'
      });
    }

    // Verify that the quotation has a valid vendor ID
    if (!quotation.vendorId) {
      return reply.status(400).send({
        error: true,
        message: 'Vendor is not yet onboarded in Velynk. Cannot approve a quotation without a valid vendor ID.'
      });
    }

    // Check if there are at least 3 quotations for this PR
    const quotationsCount = await quotationRepo.count({
      where: { prId: quotation.prId }
    });

    if (quotationsCount < 3) {
      return reply.status(400).send({
        error: true,
        message: `Minimum 3 quotations required for approval. Currently only ${quotationsCount} quotation(s) available.`
      });
    }

    // Get PR details for PO creation
    const prRepo = queryRunner.manager.getRepository(PurchaseRequest);
    const purchaseRequest = await prRepo.findOne({
      where: { id: quotation.prId }
    });

    if (!purchaseRequest) {
      return reply.status(404).send({
        error: true,
        message: 'Purchase request not found'
      });
    }

    // Create or update approval record
    const approvalRepo = queryRunner.manager.getRepository(Approval);

    // Check if approval already exists
    let approval = await approvalRepo.findOne({
      where: {
        requestId: quotation.id,
        requestType: RequestType.PURCHASE_REQUEST,
        approvalType: ApprovalType.QUOTE_APPROVAL,
        approvalRole: ApprovalRole.PROCUREMENT_MANAGER
      }
    });

    if (!approval) {
      approval = approvalRepo.create({
        requestId: quotation.id,
        requestType: RequestType.PURCHASE_REQUEST,
        approvalType: ApprovalType.QUOTE_APPROVAL,
        approvalRole: ApprovalRole.PROCUREMENT_MANAGER,
        status,
        remarks: request.body.remarks,
        verifiedBy: userId,
        verifiedAt: new Date()
      });
    } else {
      approval.status = status;
      approval.remarks = request.body.remarks;
      approval.verifiedBy = userId;
      approval.verifiedAt = new Date();
    }

    await approvalRepo.save(approval);

    // 2. Create approval for BIZ_FIN
    const bizfinApproval = new Approval();
    bizfinApproval.requestId = quotation.id;
    bizfinApproval.requestType = RequestType.PURCHASE_REQUEST;
    bizfinApproval.approvalType = ApprovalType.QUOTE_APPROVAL;
    bizfinApproval.approvalRole = ApprovalRole.BIZ_FIN;
    bizfinApproval.status = ApprovalStatus.PENDING_APPROVAL;
    bizfinApproval.remarks = 'Pending approval from BizFin';
    // Use 0 as a placeholder for no verifier yet
    bizfinApproval.verifiedBy = 0;
    bizfinApproval.approvalSequence = 2;

    await approvalRepo.save(bizfinApproval);

    // 3. Create approval for CC_HEAD
    const ccHeadApproval = new Approval();
    ccHeadApproval.requestId = quotation.id;
    ccHeadApproval.requestType = RequestType.PURCHASE_REQUEST;
    ccHeadApproval.approvalType = ApprovalType.QUOTE_APPROVAL;
    ccHeadApproval.approvalRole = ApprovalRole.CC_HEAD;
    ccHeadApproval.status = ApprovalStatus.PENDING_APPROVAL;
    ccHeadApproval.remarks = 'Pending approval from Cost Center Head';
    // Use 0 as a placeholder for no verifier yet
    ccHeadApproval.verifiedBy = 0;
    ccHeadApproval.approvalSequence = 3;

    await approvalRepo.save(ccHeadApproval);

    // Create Purchase Order with PO Items
    const poRepo = queryRunner.manager.getRepository(PurchaseOrder);

    // Generate PO number
    const poNumber = `PO-${new Date().getFullYear()}-${Math.floor(1000 + Math.random() * 9000)}`;

    // Calculate total amount from PO items
    const totalAmount = request.body.poItems.reduce(
      (sum, item) => sum + item.totalAmount,
      0
    );

    // Create PO
    const purchaseOrder = poRepo.create({
      poNumber,
      prId: quotation.prId,
      quotationId: quotation.id,
      type: PurchaseOrderType.ONE_TIME, // Default to ONE_TIME type
      status: PurchaseOrderStatus.PENDING_APPROVAL,
      vendorId: quotation.vendorId,
      businessUnitId: purchaseRequest.businessUnitId,
      costCenterId: purchaseRequest.costCenterId,
      paymentTerms: quotation.paymentTerms || 'Net 30',
      totalAmount,
      currency: 'INR',
      createdBy: userId
    });

    const savedPO = await poRepo.save(purchaseOrder);

    // Create PO Items
    const poItemRepo = queryRunner.manager.getRepository(PoItem);

    for (const item of request.body.poItems) {
      const poItem = new PoItem();
      poItem.poId = savedPO.id;
      poItem.itemName = item.itemName;
      poItem.itemDescription = item.description;
      poItem.quantity = item.finalizedQuantity;
      poItem.uom = item.uom;
      poItem.pricePerUnit = item.finalizedPricePerUnit;
      // Add GST fields if they exist in the PoItem entity
      // If not, you may need to add these fields to the PoItem entity
      (poItem as any).gstPercentage = item.gstPercentage;
      (poItem as any).gstAmount = item.gstAmount;
      (poItem as any).totalAmount = item.totalAmount;

      await poItemRepo.save(poItem);
    }

    await queryRunner.commitTransaction();

    return reply.status(200).send({
      message: 'Quote approved and PO created in pending approval stage',
      purchaseOrderId: savedPO.id,
      purchaseOrderNumber: poNumber
    });
  } catch (error) {
    await queryRunner.rollbackTransaction();
    console.error('Error approving quotation:', error);
    return reply.status(500).send({
      error: true,
      message: 'Failed to approve quotation'
    });
  } finally {
    await queryRunner.release();
  }
};
