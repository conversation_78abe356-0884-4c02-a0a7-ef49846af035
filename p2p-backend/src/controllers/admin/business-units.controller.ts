import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../../db/typeorm.config';
import { BusinessUnit } from '../../entities/BusinessUnit.entity';
import { CostCenter } from '../../entities/CostCenter.entity';

// Get all business units
export const getAllBusinessUnits = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const businessUnitRepository = AppDataSource.getRepository(BusinessUnit);
    
    const businessUnits = await businessUnitRepository.find({
      order: {
        name: 'ASC'
      }
    });
    
    return reply.send(businessUnits);
  } catch (error: any) {
    console.error('Error getting all business units:', error);
    return reply.status(500).send({ message: 'Error getting business units', error: error.message });
  }
};

// Get business unit by ID
export const getBusinessUnitById = async (request: FastifyRequest<{ Params: { id: number } }>, reply: FastifyReply) => {
  try {
    const { id } = request.params;
    
    const businessUnitRepository = AppDataSource.getRepository(BusinessUnit);
    
    const businessUnit = await businessUnitRepository.findOne({
      where: { id },
      relations: ['costCenters']
    });
    
    if (!businessUnit) {
      return reply.status(404).send({ message: 'Business unit not found' });
    }
    
    return reply.send(businessUnit);
  } catch (error: any) {
    console.error(`Error getting business unit with ID ${request.params.id}:`, error);
    return reply.status(500).send({ message: 'Error getting business unit', error: error.message });
  }
};

// Create business unit
export const createBusinessUnit = async (
  request: FastifyRequest<{
    Body: {
      name: string;
    }
  }>,
  reply: FastifyReply
) => {
  try {
    const { name } = request.body;
    
    const businessUnitRepository = AppDataSource.getRepository(BusinessUnit);
    
    // Create business unit
    const newBusinessUnit = businessUnitRepository.create({
      name
    });
    
    // Save business unit
    const savedBusinessUnit = await businessUnitRepository.save(newBusinessUnit);
    
    return reply.status(201).send(savedBusinessUnit);
  } catch (error: any) {
    console.error('Error creating business unit:', error);
    return reply.status(500).send({ message: 'Error creating business unit', error: error.message });
  }
};

// Update business unit
export const updateBusinessUnit = async (
  request: FastifyRequest<{
    Params: { id: number };
    Body: {
      name?: string;
    }
  }>,
  reply: FastifyReply
) => {
  try {
    const { id } = request.params;
    const { name } = request.body;
    
    const businessUnitRepository = AppDataSource.getRepository(BusinessUnit);
    
    // Get business unit
    const businessUnit = await businessUnitRepository.findOne({
      where: { id }
    });
    
    if (!businessUnit) {
      return reply.status(404).send({ message: 'Business unit not found' });
    }
    
    // Update business unit fields
    if (name) businessUnit.name = name;
    
    // Save updated business unit
    const updatedBusinessUnit = await businessUnitRepository.save(businessUnit);
    
    return reply.send(updatedBusinessUnit);
  } catch (error: any) {
    console.error(`Error updating business unit with ID ${request.params.id}:`, error);
    return reply.status(500).send({ message: 'Error updating business unit', error: error.message });
  }
};

// Delete business unit
export const deleteBusinessUnit = async (request: FastifyRequest<{ Params: { id: number } }>, reply: FastifyReply) => {
  try {
    const { id } = request.params;
    
    const businessUnitRepository = AppDataSource.getRepository(BusinessUnit);
    const costCenterRepository = AppDataSource.getRepository(CostCenter);
    
    // Check if business unit exists
    const businessUnit = await businessUnitRepository.findOne({
      where: { id }
    });
    
    if (!businessUnit) {
      return reply.status(404).send({ message: 'Business unit not found' });
    }
    
    // Check if there are any cost centers associated with this business unit
    const costCenters = await costCenterRepository.find({
      where: { businessUnitId: id }
    });
    
    if (costCenters.length > 0) {
      return reply.status(400).send({ 
        message: 'Cannot delete business unit with associated cost centers. Please delete the cost centers first.' 
      });
    }
    
    // Delete business unit
    await businessUnitRepository.delete(id);
    
    return reply.send({ message: 'Business unit deleted successfully' });
  } catch (error: any) {
    console.error(`Error deleting business unit with ID ${request.params.id}:`, error);
    return reply.status(500).send({ message: 'Error deleting business unit', error: error.message });
  }
};
