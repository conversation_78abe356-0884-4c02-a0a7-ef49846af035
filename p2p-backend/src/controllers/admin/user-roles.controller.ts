import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../../db/typeorm.config';
import { UserRole } from '../../entities/UserRole.entity';
import { User } from '../../entities/User.entity';
import { CostCenter } from '../../entities/CostCenter.entity';

// Get all user roles
export const getAllUserRoles = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const userRoleRepository = AppDataSource.getRepository(UserRole);
    const userRepository = AppDataSource.getRepository(User);
    const costCenterRepository = AppDataSource.getRepository(CostCenter);
    
    // Get all user roles
    const userRoles = await userRoleRepository.find({
      order: {
        id: 'ASC'
      }
    });
    
    // Add user and cost center details to each role
    const userRolesWithDetails = await Promise.all(userRoles.map(async (userRole) => {
      // Get user details
      const user = await userRepository.findOne({
        where: { id: userRole.userId },
        select: ['id', 'username', 'email', 'firstName', 'lastName']
      });
      
      // Get cost center details if applicable
      let costCenter = null;
      if (userRole.costCenterId) {
        costCenter = await costCenterRepository.findOne({
          where: { id: userRole.costCenterId }
        });
      }
      
      return {
        ...userRole,
        user: user ? {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username
        } : null,
        costCenter: costCenter ? {
          id: costCenter.id,
          name: costCenter.name
        } : null
      };
    }));
    
    return reply.send(userRolesWithDetails);
  } catch (error: any) {
    console.error('Error getting all user roles:', error);
    return reply.status(500).send({ message: 'Error getting user roles', error: error.message });
  }
};

// Get user role by ID
export const getUserRoleById = async (request: FastifyRequest<{ Params: { id: number } }>, reply: FastifyReply) => {
  try {
    const { id } = request.params;
    
    const userRoleRepository = AppDataSource.getRepository(UserRole);
    const userRepository = AppDataSource.getRepository(User);
    const costCenterRepository = AppDataSource.getRepository(CostCenter);
    
    // Get user role
    const userRole = await userRoleRepository.findOne({
      where: { id }
    });
    
    if (!userRole) {
      return reply.status(404).send({ message: 'User role not found' });
    }
    
    // Get user details
    const user = await userRepository.findOne({
      where: { id: userRole.userId },
      select: ['id', 'username', 'email', 'firstName', 'lastName']
    });
    
    // Get cost center details if applicable
    let costCenter = null;
    if (userRole.costCenterId) {
      costCenter = await costCenterRepository.findOne({
        where: { id: userRole.costCenterId }
      });
    }
    
    const userRoleWithDetails = {
      ...userRole,
      user: user ? {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username
      } : null,
      costCenter: costCenter ? {
        id: costCenter.id,
        name: costCenter.name
      } : null
    };
    
    return reply.send(userRoleWithDetails);
  } catch (error: any) {
    console.error(`Error getting user role with ID ${request.params.id}:`, error);
    return reply.status(500).send({ message: 'Error getting user role', error: error.message });
  }
};

// Create user role
export const createUserRole = async (
  request: FastifyRequest<{
    Body: {
      userId: number;
      role: string;
      costCenterId?: number;
    }
  }>,
  reply: FastifyReply
) => {
  try {
    const { userId, role, costCenterId } = request.body;
    
    const userRoleRepository = AppDataSource.getRepository(UserRole);
    const userRepository = AppDataSource.getRepository(User);
    const costCenterRepository = AppDataSource.getRepository(CostCenter);
    
    // Check if user exists
    const user = await userRepository.findOne({
      where: { id: userId }
    });
    
    if (!user) {
      return reply.status(404).send({ message: 'User not found' });
    }
    
    // Check if cost center exists if provided
    if (costCenterId) {
      const costCenter = await costCenterRepository.findOne({
        where: { id: costCenterId }
      });
      
      if (!costCenter) {
        return reply.status(404).send({ message: 'Cost center not found' });
      }
    }
    
    // Only allow costCenterId if role is cc_head
    if (costCenterId && role !== 'cc_head') {
      return reply.status(400).send({ 
        message: 'Cost center can only be assigned to users with the Cost Center Head role' 
      });
    }
    
    // If role is cc_head, costCenterId is required
    if (role === 'cc_head' && !costCenterId) {
      return reply.status(400).send({ 
        message: 'Cost center is required for Cost Center Head role' 
      });
    }
    
    // Create user role
    const newUserRole = userRoleRepository.create({
      userId,
      role,
      costCenterId: costCenterId || null,
      businessUnitId: null // Not exposing business unit in user roles
    });
    
    // Save user role
    const savedUserRole = await userRoleRepository.save(newUserRole);
    
    // Get user role with details
    return getUserRoleById(
      { params: { id: savedUserRole.id } } as FastifyRequest<{ Params: { id: number } }>,
      reply
    );
  } catch (error: any) {
    console.error('Error creating user role:', error);
    return reply.status(500).send({ message: 'Error creating user role', error: error.message });
  }
};

// Update user role
export const updateUserRole = async (
  request: FastifyRequest<{
    Params: { id: number };
    Body: {
      userId?: number;
      role?: string;
      costCenterId?: number | null;
    }
  }>,
  reply: FastifyReply
) => {
  try {
    const { id } = request.params;
    const { userId, role, costCenterId } = request.body;
    
    const userRoleRepository = AppDataSource.getRepository(UserRole);
    const userRepository = AppDataSource.getRepository(User);
    const costCenterRepository = AppDataSource.getRepository(CostCenter);
    
    // Get user role
    const userRole = await userRoleRepository.findOne({
      where: { id }
    });
    
    if (!userRole) {
      return reply.status(404).send({ message: 'User role not found' });
    }
    
    // Check if user exists if provided
    if (userId) {
      const user = await userRepository.findOne({
        where: { id: userId }
      });
      
      if (!user) {
        return reply.status(404).send({ message: 'User not found' });
      }
      
      userRole.userId = userId;
    }
    
    // Update role if provided
    if (role) {
      userRole.role = role;
    }
    
    // Check if cost center exists if provided and not null
    if (costCenterId) {
      const costCenter = await costCenterRepository.findOne({
        where: { id: costCenterId }
      });
      
      if (!costCenter) {
        return reply.status(404).send({ message: 'Cost center not found' });
      }
    }
    
    // Only allow costCenterId if role is cc_head
    const updatedRole = role || userRole.role;
    if (costCenterId && updatedRole !== 'cc_head') {
      return reply.status(400).send({ 
        message: 'Cost center can only be assigned to users with the Cost Center Head role' 
      });
    }
    
    // If role is cc_head, costCenterId is required
    if (updatedRole === 'cc_head' && costCenterId === null) {
      return reply.status(400).send({ 
        message: 'Cost center is required for Cost Center Head role' 
      });
    }
    
    // Update cost center ID if provided
    if (costCenterId !== undefined) {
      userRole.costCenterId = costCenterId;
    }
    
    // Save updated user role
    const updatedUserRole = await userRoleRepository.save(userRole);
    
    // Get updated user role with details
    return getUserRoleById(
      { params: { id: updatedUserRole.id } } as FastifyRequest<{ Params: { id: number } }>,
      reply
    );
  } catch (error: any) {
    console.error(`Error updating user role with ID ${request.params.id}:`, error);
    return reply.status(500).send({ message: 'Error updating user role', error: error.message });
  }
};

// Delete user role
export const deleteUserRole = async (request: FastifyRequest<{ Params: { id: number } }>, reply: FastifyReply) => {
  try {
    const { id } = request.params;
    
    const userRoleRepository = AppDataSource.getRepository(UserRole);
    
    // Get user role
    const userRole = await userRoleRepository.findOne({
      where: { id }
    });
    
    if (!userRole) {
      return reply.status(404).send({ message: 'User role not found' });
    }
    
    // Delete user role
    await userRoleRepository.delete(id);
    
    return reply.send({ message: 'User role deleted successfully' });
  } catch (error: any) {
    console.error(`Error deleting user role with ID ${request.params.id}:`, error);
    return reply.status(500).send({ message: 'Error deleting user role', error: error.message });
  }
};
