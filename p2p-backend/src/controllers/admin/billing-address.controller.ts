import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../../db/typeorm.config';
import { BillingAddress } from '../../entities/BillingAddress.entity';

/**
 * Get all billing addresses
 */
export const getAllBillingAddresses = async (
  request: FastifyRequest<{
    Querystring: {
      page?: number;
      limit?: number;
      search?: string;
      includeDeleted?: boolean;
    }
  }>,
  reply: FastifyReply
) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      includeDeleted = false
    } = request.query;

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Create query builder
    let queryBuilder = AppDataSource.getRepository(BillingAddress)
      .createQueryBuilder('billingAddress');
      
    // Include or exclude soft-deleted records based on the parameter
    if (includeDeleted) {
      queryBuilder = queryBuilder.withDeleted();
    }

    // Apply search filter if provided
    if (search) {
      queryBuilder = queryBuilder.andWhere(
        '(billingAddress.address LIKE :search OR billingAddress.company_name LIKE :search OR billingAddress.gstin LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Get total count for pagination
    const totalCount = await queryBuilder.getCount();

    // Get paginated results
    const billingAddresses = await queryBuilder
      .select([
        'billingAddress.id',
        'billingAddress.address',
        'billingAddress.company_name',
        'billingAddress.gstin',
        'billingAddress.createdAt',
        'billingAddress.updatedAt',
        'billingAddress.deletedAt'
      ])
      .orderBy('billingAddress.id', 'DESC')
      .skip(offset)
      .take(limit)
      .getMany();

    return reply.status(200).send({
      data: billingAddresses,
      pagination: {
        totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    request.log.error('Error fetching billing addresses:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};

/**
 * Get a billing address by ID
 */
export const getBillingAddressById = async (
  request: FastifyRequest<{ Params: { id: number } }>,
  reply: FastifyReply
) => {
  try {
    const { id } = request.params;

    const billingAddress = await AppDataSource.getRepository(BillingAddress)
      .createQueryBuilder('billingAddress')
      .select([
        'billingAddress.id',
        'billingAddress.address',
        'billingAddress.company_name',
        'billingAddress.gstin',
        'billingAddress.createdAt',
        'billingAddress.updatedAt',
        'billingAddress.deletedAt'
      ])
      .where('billingAddress.id = :id', { id })
      .getOne();

    if (!billingAddress) {
      return reply.status(404).send({ error: true, message: 'Billing address not found' });
    }

    return reply.status(200).send(billingAddress);
  } catch (error) {
    request.log.error('Error fetching billing address:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};

/**
 * Create a new billing address
 */
export const createBillingAddress = async (
  request: FastifyRequest<{
    Body: {
      address: string;
      company_name?: string;
      gstin?: string;
    }
  }>,
  reply: FastifyReply
) => {
  try {
    const { address, company_name, gstin } = request.body;

    const billingAddressRepository = AppDataSource.getRepository(BillingAddress);
    const newBillingAddress = billingAddressRepository.create({
      address,
      company_name,
      gstin
    });
    await billingAddressRepository.save(newBillingAddress);

    return reply.status(201).send(newBillingAddress);
  } catch (error) {
    request.log.error('Error creating billing address:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};

/**
 * Update a billing address
 */
export const updateBillingAddress = async (
  request: FastifyRequest<{
    Params: { id: number };
    Body: {
      address: string;
      company_name?: string;
      gstin?: string;
    }
  }>,
  reply: FastifyReply
) => {
  try {
    const { id } = request.params;
    const { address, company_name, gstin } = request.body;

    const billingAddressRepository = AppDataSource.getRepository(BillingAddress);
    const billingAddress = await billingAddressRepository.findOne({ where: { id } });

    if (!billingAddress) {
      return reply.status(404).send({ error: true, message: 'Billing address not found' });
    }

    billingAddress.address = address;
    
    // Update company_name and gstin if provided
    if (company_name !== undefined) {
      billingAddress.company_name = company_name;
    }
    
    if (gstin !== undefined) {
      billingAddress.gstin = gstin;
    }
    
    await billingAddressRepository.save(billingAddress);

    return reply.status(200).send(billingAddress);
  } catch (error) {
    request.log.error('Error updating billing address:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};

/**
 * Soft delete a billing address
 */
export const deleteBillingAddress = async (
  request: FastifyRequest<{ Params: { id: number } }>,
  reply: FastifyReply
) => {
  try {
    const { id } = request.params;

    // Check if billing address exists
    const billingAddress = await AppDataSource.getRepository(BillingAddress)
      .createQueryBuilder('billingAddress')
      .select([
        'billingAddress.id',
        'billingAddress.address',
        'billingAddress.company_name',
        'billingAddress.gstin',
        'billingAddress.createdAt',
        'billingAddress.updatedAt',
        'billingAddress.deletedAt'
      ])
      .where('billingAddress.id = :id', { id })
      .getOne();

    if (!billingAddress) {
      return reply.status(404).send({ error: true, message: 'Billing address not found' });
    }

    // Soft delete billing address
    await AppDataSource.getRepository(BillingAddress).softDelete(id);

    return reply.status(200).send({ success: true, message: 'Billing address deleted successfully' });
  } catch (error) {
    request.log.error('Error deleting billing address:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};

/**
 * Permanently delete a billing address (hard delete)
 */
export const permanentlyDeleteBillingAddress = async (
  request: FastifyRequest<{ Params: { id: number } }>,
  reply: FastifyReply
) => {
  try {
    const { id } = request.params;

    // Check if billing address exists (including soft-deleted)
    const billingAddress = await AppDataSource.getRepository(BillingAddress)
      .createQueryBuilder('billingAddress')
      .withDeleted()
      .where('billingAddress.id = :id', { id })
      .getOne();

    if (!billingAddress) {
      return reply.status(404).send({ error: true, message: 'Billing address not found' });
    }

    // Permanently delete billing address
    await AppDataSource.getRepository(BillingAddress).delete(id);

    return reply.status(200).send({ success: true, message: 'Billing address permanently deleted' });
  } catch (error) {
    request.log.error('Error permanently deleting billing address:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};

/**
 * Restore a soft-deleted billing address
 */
export const restoreBillingAddress = async (
  request: FastifyRequest<{ Params: { id: number } }>,
  reply: FastifyReply
) => {
  try {
    const { id } = request.params;

    // Check if billing address exists in soft-deleted items
    const billingAddress = await AppDataSource.getRepository(BillingAddress)
      .createQueryBuilder('billingAddress')
      .withDeleted()
      .where('billingAddress.id = :id', { id })
      .andWhere('billingAddress.deletedAt IS NOT NULL')
      .getOne();

    if (!billingAddress) {
      return reply.status(404).send({ error: true, message: 'Deleted billing address not found' });
    }

    // Restore billing address
    await AppDataSource.getRepository(BillingAddress).restore(id);

    // Get the restored address
    const restoredAddress = await AppDataSource.getRepository(BillingAddress).findOne({
      where: { id }
    });

    return reply.status(200).send({
      success: true, 
      message: 'Billing address restored successfully',
      data: restoredAddress
    });
  } catch (error) {
    request.log.error('Error restoring billing address:', error);
    return reply.status(500).send({ error: true, message: 'Internal server error' });
  }
};
