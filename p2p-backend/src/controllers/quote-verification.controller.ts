import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../db/typeorm.config';
import { Approval } from '../entities/Approval.entity';
import { PurchaseOrder, PurchaseOrderStatus } from '../entities/PurchaseOrder.entity';
import { User } from '../entities/User.entity';
import { EmailRequestType } from '../constants/enums';
import { UserRole } from '../entities/UserRole.entity';
import { ApprovalRole, ApprovalStatus, ApprovalType, RequestType } from '../constants/enums';
import { ApprovalNotificationHandler } from '../services/approval-notification.handler';
import { notifyNextApprovers } from '../utils/next-approval-email';

// Interface for verification request body
interface QuoteVerificationBody {
  action: 'APPROVE' | 'REJECT';
  remarks?: string;
}

// Extend FastifyRequest to include userRoles
// declare module 'fastify' {
//   interface FastifyRequest {
//     userRoles?: {role: string}[];
//   }
// }

/**
 * Verify a quote (approve or reject) for a purchase order
 * This is used by BizFin and CC_HEAD roles to approve or reject PO quotes
 */
export const verifyQuote = async (
  request: FastifyRequest<{
    Params: { id: number }, // PO ID
    Body: QuoteVerificationBody
  }>,
  reply: FastifyReply
) => {
  // Start a transaction to ensure data consistency
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Ensure user is authenticated
    if (!request.user) {
      await queryRunner.rollbackTransaction();
      return reply.status(401).send({ error: true, message: 'Authentication required' });
    }

    const poId = request.params.id;
    const { action, remarks } = request.body;
    const userId = (request.user as User).id;
    const userRoles = request.userRoles || [];

    // Check if user has necessary role to verify
    const hasBizFinRole = userRoles.some((role: {role: string}) => role.role === ApprovalRole.BIZ_FIN);
    const hasCcHeadRole = userRoles.some((role: {role: string}) => role.role === ApprovalRole.CC_HEAD);

    if (!hasBizFinRole && !hasCcHeadRole) {
      await queryRunner.rollbackTransaction();
      return reply.status(403).send({
        error: true,
        message: 'You are not authorized to verify quotes'
      });
    }

    // Step 1: Find the purchase order by ID with FOR UPDATE lock to prevent race conditions
    const purchaseOrder = await queryRunner.manager
      .createQueryBuilder(PurchaseOrder, 'po')
      .setLock('pessimistic_write')
      .where('po.id = :id', { id: poId })
      .getOne();

    if (!purchaseOrder) {
      await queryRunner.rollbackTransaction();
      return reply.status(404).send({
        error: true,
        message: 'Purchase order not found'
      });
    }

    // Step 2: Get the PR ID from the purchase order
    const prId = purchaseOrder.prId;
    if (!prId) {
      await queryRunner.rollbackTransaction();
      return reply.status(400).send({
        error: true,
        message: 'Purchase order has no associated purchase request'
      });
    }

    // Step 3: Find the first pending approval for this PR with QUOTE_APPROVAL type with FOR UPDATE lock
    const approval = await queryRunner.manager
      .createQueryBuilder(Approval, 'approval')
      .setLock('pessimistic_write')
      .where('approval.requestId = :requestId', { requestId: prId })
      .andWhere('approval.approvalType = :approvalType', { approvalType: 'QUOTE_APPROVAL' })
      .andWhere('approval.status = :status', { status: ApprovalStatus.PENDING_APPROVAL })
      .orderBy('approval.approvalSequence', 'ASC')
      .getOne();

    if (!approval) {
      await queryRunner.rollbackTransaction();
      return reply.status(404).send({
        error: true,
        message: 'No pending quote approval found for this purchase order'
      });
    }

    // Step 4: Verify that the user has the correct role to approve this specific approval
    if (approval.approvalRole === ApprovalRole.BIZ_FIN && !hasBizFinRole) {
      await queryRunner.rollbackTransaction();
      return reply.status(403).send({
        error: true,
        message: 'Only BizFin can approve this quote'
      });
    }

    if (approval.approvalRole === ApprovalRole.CC_HEAD && !hasCcHeadRole) {
      await queryRunner.rollbackTransaction();
      return reply.status(403).send({
        error: true,
        message: 'Only CC_HEAD can approve this quote'
      });
    }

    // Step 5: Update approval status based on action
    if (action === 'APPROVE') {
      approval.status = ApprovalStatus.APPROVED;
      approval.remarks = remarks || 'Approved';
      
      // Check if this was the last approval in sequence with FOR UPDATE lock
      const nextApproval = await queryRunner.manager
        .createQueryBuilder(Approval, 'approval')
        .setLock('pessimistic_write')
        .where('approval.requestId = :requestId', { requestId: prId })
        .andWhere('approval.approvalType = :approvalType', { approvalType: 'QUOTE_APPROVAL' })
        .andWhere('approval.status = :status', { status: ApprovalStatus.PENDING_APPROVAL })
        .andWhere('approval.approvalSequence = :sequence', { sequence: approval.approvalSequence + 1 })
        .getOne();

      // If no more approvals are needed, update PO status to OPEN
      if (!nextApproval) {
        purchaseOrder.status = PurchaseOrderStatus.OPEN;
      }
    } else {
      // Reject the approval and set PO status to REJECTED
      approval.status = ApprovalStatus.REJECTED;
      approval.remarks = remarks || 'Rejected';
      purchaseOrder.status = PurchaseOrderStatus.REJECTED;
      
      // Get all subsequent approvals in the sequence and mark them as SKIPPED with FOR UPDATE lock
      const subsequentApprovals = await queryRunner.manager
        .createQueryBuilder(Approval, 'approval')
        .setLock('pessimistic_write')
        .where('approval.requestId = :requestId', { requestId: prId })
        .andWhere('approval.approvalType = :approvalType', { approvalType: 'QUOTE_APPROVAL' })
        .andWhere('approval.approvalSequence > :sequence', { sequence: approval.approvalSequence })
        .getMany();
      
      if (subsequentApprovals.length > 0) {
        for (const subApproval of subsequentApprovals) {
          subApproval.status = ApprovalStatus.SKIPPED;  // Using ON_HOLD as we don't have a SKIPPED status
          subApproval.remarks = 'Skipped due to prior rejection';
        }
        
        await queryRunner.manager.save(subsequentApprovals);
      }
    }

    // Set verification metadata
    approval.verifiedBy = userId;
    approval.verifiedAt = new Date();

    // Save changes
    await queryRunner.manager.save(approval);
    await queryRunner.manager.save(purchaseOrder);

    // Store data needed for notifications after transaction
    const approvalData = {
      poId,
      prId,
      status: approval.status as ApprovalStatus,
      approvalRole: approval.approvalRole as ApprovalRole,
      remarks: approval.remarks || '',
      approvalSequence: approval.approvalSequence,
      costCenterId: purchaseOrder.costCenterId
    };

    // Get the creator of the purchase order
    const creator = await queryRunner.manager.findOne(User, {
      where: { id: purchaseOrder.createdBy },
      relations: ['roles']
    });

    // Commit transaction
    await queryRunner.commitTransaction();

    // Send notifications outside of transaction to reduce overhead
    try {
      // Send notification to creator
      if (creator) {
        // Use non-blocking notification to avoid waiting for email to be sent
        ApprovalNotificationHandler.handlePurchaseOrderApproval(
          approvalData.poId,
          creator,
          approvalData.status,
          approvalData.approvalRole,
          approvalData.remarks,
          EmailRequestType.PURCHASE_ORDER
        ).catch(err => console.error(`Error sending creator notification: ${err.message}`));
      } else {
        console.error(`Creator not found for purchase order ${approvalData.poId}`);
      }
      
      // Send notification to next approvers if approved
      if (approvalData.status === ApprovalStatus.APPROVED) {
        notifyNextApprovers({
          requestId: approvalData.poId,
          requestType: "PR_APPROVAL",
          approvalType: ApprovalType.QUOTE_APPROVAL,
          currentApprovalSequence: approvalData.approvalSequence + 1,
          status: ApprovalStatus.PENDING_APPROVAL,
          remarks: '',
          emailRequestType: EmailRequestType.PURCHASE_ORDER,
          costCenterId: approvalData.costCenterId
        }).catch(err => console.error(`Error notifying next approvers: ${err.message}`));
      }
    } catch (notificationError) {
      // Log notification errors but don't fail the request
      console.error(`Notification error for purchase order ${poId}:`, notificationError);
    }

    return reply.status(200).send({
      message: `Quote ${action === 'APPROVE' ? 'approved' : 'rejected'} successfully`,
      approval: {
        id: approval.id,
        role: approval.approvalRole,
        status: approval.status,
        verifiedAt: approval.verifiedAt
      },
      purchaseOrder: {
        id: purchaseOrder.id,
        poNumber: purchaseOrder.poNumber,
        status: purchaseOrder.status
      }
    });
  } catch (error: any) {
    // Rollback transaction on error
    await queryRunner.rollbackTransaction();
    console.error('Error verifying quote:', error);
    return reply.status(500).send({
      error: true,
      message: error.message || 'Internal server error'
    });
  } finally {
    // Release query runner
    await queryRunner.release();
  }
};

