import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../db/typeorm.config';
import { Invoice, PaymentRequestStatus, InvoiceStatus } from '../entities/Invoice.entity';
import { InvoiceItem } from '../entities/InvoiceItem.entity';
import { Grn } from '../entities/Grn.entity';
import { GrnItem } from '../entities/GrnItem.entity';
import { PurchaseOrder, PurchaseOrderStatus } from '../entities/PurchaseOrder.entity';
import { PoItem } from '../entities/PoItem.entity';
import { Document } from '../entities/document.entity';
import { User } from '../entities/User.entity';
import { ApprovalRole, ApprovalStatus, ApprovalType, EmailRequestType, RequestType } from '../constants/enums';
import { In } from 'typeorm';
import { velynkPaymentService } from '../services/velynk-payment.service';

// Custom interface for request with authentication data
interface RequestWithUser {
  user?: {
    id: number;
    [key: string]: any;
  };
  userRoles?: Array<{ role: string; [key: string]: any; }>;
}

// Type for authenticated request
type AuthenticatedRequest<T extends Record<string, any> = {}> = FastifyRequest<T> & RequestWithUser;

// Interface for creating an invoice

// --- GET ALL INVOICES FOR LISTING ---
import { getAllInvoices } from '../services/invoice.service';
import { Approval } from '../entities/Approval.entity';
import { ApprovalNotificationHandler } from '../services/approval-notification.handler';
import { notifyNextApprovers } from '../utils/next-approval-email';
import { documentService } from '../services/document.service';
import { extractUniqueItemCategories } from '../utils/invoice.utils';

export const getAllInvoicesController = async (
  request: AuthenticatedRequest<{
    Querystring: { page?: string; limit?: string }
  }>,
  reply: FastifyReply
) => {
  try {
    // Parse pagination parameters
    const page = parseInt(request.query.page || '1');
    const limit = parseInt(request.query.limit || '10');
    
    // Calculate offset
    const skip = (page - 1) * limit;
    
    // Create query builder
    const queryBuilder = AppDataSource.getRepository(Invoice)
      .createQueryBuilder('invoice')
      .leftJoinAndSelect('invoice.grn', 'grn')
      .leftJoinAndSelect('grn.purchaseOrder', 'po')
      .leftJoinAndSelect('po.items', 'poItems')
      .leftJoinAndSelect('poItems.itemCategory', 'itemCategory')
      .leftJoinAndSelect('po.quotation', 'quotation')
      .orderBy('invoice.id', 'DESC');
    
    // Get total count
    const totalCount = await queryBuilder.getCount();
    
    // Get paginated data
    const invoices = await queryBuilder
      .skip(skip)
      .take(limit)
      .getMany();
    
    // Format response
    const data = invoices.map(inv => {
      const poItems = inv.grn?.purchaseOrder?.items || [];
      return {
        id: inv.id,
        invoiceNumber: inv.invoiceNumber,
        invoiceDate: inv.invoiceDate,
        invoiceValue: inv.invoiceValue,
        status: inv.status || 'PENDING',
        vendorName: inv.grn?.purchaseOrder?.quotation?.vendorName || '',
        vendorId: inv.grn?.purchaseOrder?.quotation?.vendorId || '',
        grn_id: inv.grn?.id,
        po_id: inv.grn?.poId,
        pr_id: inv.grn?.purchaseOrder?.prId,
        item_categories: extractUniqueItemCategories(poItems)
      };
    });
    
    // Return data with pagination in the format expected by the frontend
    return reply.send({
      invoices: data,
      pagination: {
        totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (e) {
    console.error('Error fetching invoices:', e);
    return reply.status(500).send({ error: true, message: 'Failed to fetch invoices' });
  }
};


export const getInvoiceByIdController = async (
  request: AuthenticatedRequest<{ Params: { id: number } }>,
  reply: FastifyReply
) => {
  try {
    const invoiceId = Number(request.params.id);
    if (isNaN(invoiceId)) {
      return reply.status(400).send({ error: true, message: 'Invalid invoice ID' });
    }
    
    // Load invoice with all necessary relations in a single query
    const invoice = await getInvoiceById(invoiceId);
    if (!invoice) {
      return reply.status(404).send({ error: true, message: 'Invoice not found' });
    }
    
    // Get prId from the loaded relations
    let prId: number | undefined = undefined;
    if (invoice.grn && (invoice.grn as any).purchaseOrder) {
      prId = (invoice.grn as any).purchaseOrder.prId;
    }

    // Load approvals and documents in a single query
    const [approvals, attachments] = await Promise.all([
      AppDataSource.getRepository(Approval)
        .createQueryBuilder('approval')
        .where('approval.requestId = :requestId', { requestId: invoiceId })
        .andWhere('approval.requestType = :requestType', { requestType: RequestType.INVOICE })
        .getMany(),
      documentService.getDocumentsByEntity('invoice', invoiceId)
    ]);
    
    // Process approval status
    let showInvoiceVerificationDialog = false;
    let showInvoiceVerificationToRole = null;
    const pendingApproval = approvals.find(approval => 
      approval.status === ApprovalStatus.PENDING_APPROVAL && 
      approval.approvalType === ApprovalType.INVOICE_APPROVAL
    );
    
    if (pendingApproval) {
      showInvoiceVerificationDialog = true;
      showInvoiceVerificationToRole = pendingApproval.approvalRole;
    }
    
    // Generate URLs for attachments in parallel
    if (attachments.length > 0) {
      const s3Service = await import('../services/s3.service').then(m => m.default);
      await Promise.all(attachments.map(async (attachment) => {
        if (!attachment.fileUrl && attachment.fileKey) {
          try {
            attachment.fileUrl = await s3Service.getFileUrl(attachment.fileKey);
          } catch (error) {
            console.error(`Failed to generate URL for file ${attachment.fileKey}:`, error);
            attachment.fileUrl = ''; // Set empty string as fallback
          }
        }
      }));
    }

    const result = {
      id: invoice.id,
      invoiceNumber: invoice.invoiceNumber,
      invoiceDate: invoice.invoiceDate,
      invoiceValue: invoice.invoiceValue,
      status: invoice.status as InvoiceStatus,
      vendorName: invoice.vendorName || '',
      vendorId: invoice.vendorId || '',
      remarks: invoice.remarks,
      items: invoice.items || [],
      grnId: invoice.grnId,
      prId: invoice.prId,
      paymentRequestStatus: invoice.paymentRequestStatus,
      attachmentsCount: invoice.attachments ? invoice.attachments.length : 0,
      itemsCount: invoice.items ? invoice.items.length : 0,
      showInvoiceVerificationDialog: showInvoiceVerificationDialog,
      showInvoiceVerificationToRole: showInvoiceVerificationToRole,
      poDetails: invoice.poDetails,
      attachments: attachments
    };

    return reply.send({ message: 'Invoice fetched successfully', invoice: result });
  } catch (e) {
    console.error('Error fetching invoice by ID:', e);
    return reply.status(500).send({ error: true, message: 'Failed to fetch invoice' });
  }
};

interface CreateInvoiceBody {
  grnId: number;
  invoiceNumber: string;
  invoiceDate: string;
  invoiceValue: number;
  remarks?: string;
  invoiceItems: {
    grnItemId: number;
    amount: number;
    tax?: number;
    quantity: number;
    pricePerUnit?: number;
    totalPrice?: number;
    gstOption?: string;
    poItem?: {
      id: number;
      itemName: string;
      quantity: string;
      uom: string;
      unitPrice: number;
    };
  }[];
  fileKey?: string;     // For backward compatibility (single file)
  fileKeys?: string[];  // For multiple files
}

/**
 * Create a new Invoice for a GRN
 */
export const createInvoice = async (
  request: AuthenticatedRequest<{ Body: CreateInvoiceBody }>,
  reply: FastifyReply
) => {
  // Start a transaction to ensure data consistency
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Ensure user is authenticated
    if (!request.user) {
      return reply.status(401).send({ error: true, message: 'Authentication required' });
    }

    // Extract request data
    const {
      grnId,
      invoiceNumber,
      invoiceDate,
      invoiceValue,
      remarks,
      invoiceItems,
      fileKey,
      fileKeys
    } = request.body;

    // Process file attachments (at least one file is required)
    const allFileKeys = [];
    if (fileKey && typeof fileKey === 'string') {
      allFileKeys.push(fileKey);
    }
    if (fileKeys && Array.isArray(fileKeys)) {
      allFileKeys.push(...fileKeys);
    }

    // Validate that at least one file is attached
    if (allFileKeys.length === 0) {
      return reply.status(400).send({
        error: true,
        message: 'At least one invoice file attachment is required'
      });
    }

    // Get user ID for records
    const userId = (request.user as User).id;
    const userRoles = request.userRoles || [];

    // Verify the GRN exists and its PO is not closed
    const grn = await queryRunner.manager.findOne(Grn, {
      where: { id: grnId },
      relations: ['purchaseOrder']
    });

    if (!grn) {
      return reply.status(404).send({
        error: true,
        message: 'GRN not found'
      });
    }

    // Check if PO is closed
    if (grn.purchaseOrder?.status === PurchaseOrderStatus.CLOSED) {
      return reply.status(400).send({
        error: true,
        message: 'Cannot create invoice for a closed purchase order'
      });
    }

    // Check if user is authorized to create an invoice for this GRN
    const isProcurementManager = userRoles.some(role => role.role === ApprovalRole.PROCUREMENT_MANAGER);
    const isBizFin = userRoles.some(role => role.role === ApprovalRole.BIZ_FIN);

    if (!isProcurementManager && !isBizFin) {
      return reply.status(403).send({
        error: true,
        message: 'You are not authorized to create an invoice for this GRN'
      });
    }

    // Verify that all GRN items exist
    const grnItemIds = invoiceItems.map(item => item.grnItemId);
    const grnItems = await queryRunner.manager.find(GrnItem, {
      where: {
        id: In(grnItemIds),
        grnId: grnId
      },
      relations: ['poItem']
    });

    if (grnItems.length !== grnItemIds.length) {
      return reply.status(400).send({
        error: true,
        message: 'One or more GRN items do not exist in the specified GRN'
      });
    }

    // Create and save the Invoice
    const invoice = new Invoice();
    invoice.grnId = grnId;
    invoice.invoiceNumber = invoiceNumber;
    invoice.invoiceDate = new Date(invoiceDate);
    invoice.invoiceValue = invoiceValue;
    if (remarks) {
      invoice.remarks = remarks;
    }
    invoice.createdBy = userId;

    const savedInvoice = await queryRunner.manager.save(invoice);

    // Create and save the Invoice items
    const invoiceItemsToSave = invoiceItems.map(item => {
      const newInvoiceItem = new InvoiceItem();
      newInvoiceItem.invoiceId = savedInvoice.id;
      newInvoiceItem.grnItemId = item.grnItemId;
      newInvoiceItem.amount = item.amount;
      newInvoiceItem.tax = item.tax || 0;
      newInvoiceItem.quantity = item.quantity;
      newInvoiceItem.pricePerUnit = item.pricePerUnit || (item.poItem?.unitPrice || 0);
      newInvoiceItem.totalPrice = item.totalPrice || item.amount;
      newInvoiceItem.createdBy = userId;
      return newInvoiceItem;
    });

    const savedInvoiceItems = await queryRunner.manager.save(invoiceItemsToSave);

    // Handle file attachments (if any)
    // Remove duplicates if any
    const uniqueFileKeys = [...new Set(allFileKeys)];

    if (uniqueFileKeys.length > 0) {
      // Create document records for each file key
      const documentRepo = queryRunner.manager.getRepository(Document);
      const documentPromises = uniqueFileKeys.map(fileKey => {
        const document = documentRepo.create({
          entityType: 'invoice',
          entityId: savedInvoice.id,
          fileKey,
          fileName: 'Invoice Document',
          fileSize: 0,
          contentType: 'application/octet-stream'
        });

        return queryRunner.manager.save(document);
      });

      await Promise.all(documentPromises);
    }

    // Create approval entries for invoice
    const approvalRepo = queryRunner.manager.getRepository(Approval);
    const allApprovalRoles = [
      { role: ApprovalRole.BIZ_FIN, sequence: 6 },
      { role: ApprovalRole.FINANCE, sequence: 7 }
    ];

    const approvalEntries = allApprovalRoles.map(({ role, sequence }) => {
      return approvalRepo.create({
        requestId: savedInvoice.id,
        approvalRole: role,
        requestType: RequestType.INVOICE,
        approvalType: ApprovalType.INVOICE_APPROVAL,
        approvalSequence: sequence,
        status: ApprovalStatus.PENDING_APPROVAL
      });
    });

    await queryRunner.manager.save(approvalEntries);

    // Commit the transaction after all operations are complete
    await queryRunner.commitTransaction();

    // Get user details from the transaction manager to avoid N+1 query
    const userDetails = await queryRunner.manager.getRepository(User)
      .findOne({ where: { id: userId } });

    // Prepare the response object
    const invoiceResponse = {
      id: savedInvoice.id,
      grnId: savedInvoice.grnId,
      invoiceNumber: savedInvoice.invoiceNumber,
      invoiceDate: savedInvoice.invoiceDate,
      invoiceValue: savedInvoice.invoiceValue,
      vendorName: grn.purchaseOrder?.quotation?.vendorName || '',
      createdBy: userDetails ? {
        id: userDetails.id,
        username: userDetails.username,
        email: userDetails.email,
        name: userDetails.name || userDetails.username
      } : userId,
      itemsCount: savedInvoiceItems.length,
      attachmentsCount: uniqueFileKeys.length,
      paymentRequestStatus: savedInvoice.paymentRequestStatus
    };

    notifyNextApprovers({
      requestId: savedInvoice.id,
      requestType: "Invoice",
      approvalType: ApprovalType.INVOICE_APPROVAL,
      currentApprovalSequence: 6,
      status: ApprovalStatus.PENDING_APPROVAL,
      remarks: '',
      emailRequestType: EmailRequestType.INVOICE
    }).catch(err => console.error(`Error notifying next approvers: ${err.message}`));

    // NOTE: Automatic payment request creation is disabled until Velynk API integration is confirmed
    // Uncomment the code below when ready to enable automatic payment request creation
    /*
    try {
      // Only attempt to create a payment request if the invoice is successfully created
      const paymentRequestResult = await velynkPaymentService.createPaymentRequestForInvoice(savedInvoice.id);

      // If payment request creation is successful, add payment request info to the response
      if (paymentRequestResult && !paymentRequestResult.error) {
        invoiceResponse.paymentRequestStatus = paymentRequestResult.invoice.paymentRequestStatus;

        return reply.status(201).send({
          message: 'Invoice created successfully with payment request',
          invoice: invoiceResponse
        });
      }
    } catch (paymentError) {
      // Log the error but don't fail the invoice creation
      console.error('Error creating payment request during invoice creation:', paymentError);
      // We'll continue and return the invoice without payment request
    }
    */

    // Return the invoice response even if payment request creation failed
    return reply.status(201).send({
      message: 'Invoice created successfully',
      invoice: invoiceResponse
    });
  } catch (error: any) {
    // Rollback the transaction on error
    await queryRunner.rollbackTransaction();
    console.error('Error creating Invoice:', error);
    return reply.status(500).send({
      error: true,
      message: 'Internal server error'
    });
  } finally {
    // Release the query runner
    await queryRunner.release();
  }
};

/**
 * Approve an invoice at the current approval step
 */
export const approveInvoice = async (
  request: AuthenticatedRequest<{ Params: { id: number }; Body: { remarks?: string } }>,
  reply: FastifyReply
) => {
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    const invoiceId = request.params.id;
    const userId = request.user?.id;
    const userRoles = request.userRoles || [];
    const remarks = request.body?.remarks;

    // Verify the invoice exists and its PO is not closed
    const invoice = await queryRunner.manager.findOne(Invoice, {
      where: { id: invoiceId },
      relations: ['grn', 'grn.purchaseOrder']
    });

    if (!invoice) {
      await queryRunner.rollbackTransaction();
      return reply.status(404).send({
        error: true,
        message: 'Invoice not found'
      });
    }

    // Check if PO is closed
    if (invoice.grn?.purchaseOrder?.status === PurchaseOrderStatus.CLOSED) {
      await queryRunner.rollbackTransaction();
      return reply.status(400).send({
        error: true,
        message: 'Cannot approve invoice for a closed purchase order'
      });
    }

    // Get all approval entries for this invoice
    const approvalRepo = queryRunner.manager.getRepository('Approval');
    const approvals = await approvalRepo.find({ where: { requestId: invoiceId, requestType: 'INVOICE', approvalType: 'INVOICE_APPROVAL' }, order: { approvalSequence: 'ASC' } });
    const pendingApproval = approvals.find(a => a.status === 'PENDING_APPROVAL');
    if (!pendingApproval) {
      await queryRunner.rollbackTransaction();
      return reply.status(400).send({ error: true, message: 'No pending approval for this invoice.' });
    }
    // Check user role matches current approval step
    const userHasRole = userRoles.some(ur => ur.role === pendingApproval.approvalRole);
    if (!userHasRole) {
      await queryRunner.rollbackTransaction();
      return reply.status(403).send({ error: true, message: `You do not have the role (${pendingApproval.approvalRole}) required for this approval step.` });
    }
    // Approve current step
    pendingApproval.status = 'APPROVED';
    pendingApproval.remarks = remarks;
    pendingApproval.verifiedBy = userId;
    pendingApproval.verifiedAt = new Date();
    await approvalRepo.save(pendingApproval);
    
    // Move next step to PENDING_APPROVAL if exists
    const nextApproval = approvals.find(a => a.status === 'PENDING' && a.approvalSequence === pendingApproval.approvalSequence + 1);
    if (nextApproval) {
      nextApproval.status = 'PENDING_APPROVAL';
      await approvalRepo.save(nextApproval);
    }
    
    // Commit transaction first
    await queryRunner.commitTransaction();
    
    // Send notification after successful commit
    await ApprovalNotificationHandler.handleInvoiceApproval(
      invoiceId,
      request.user as User,
      ApprovalStatus.APPROVED,
      pendingApproval.approvalRole as ApprovalRole,
      remarks
    );
    
    return reply.status(200).send({ message: 'Invoice approved for this step.' });
  } catch (error: any) {
    await queryRunner.rollbackTransaction();
    return reply.status(500).send({ error: true, message: error.message });
  } finally {
    await queryRunner.release();
  }
};

/**
 * Reject an invoice at the current approval step
 */
export const rejectInvoice = async (
  request: AuthenticatedRequest<{ Params: { id: number }; Body: { remarks?: string } }>,
  reply: FastifyReply
) => {
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();
  try {
    const invoiceId = request.params.id;
    const userId = request.user?.id;
    const userRoles = request.userRoles || [];
    const remarks = request.body?.remarks;

    // Verify the invoice exists and its PO is not closed
    const invoice = await queryRunner.manager.findOne(Invoice, {
      where: { id: invoiceId },
      relations: ['grn', 'grn.purchaseOrder']
    });

    if (!invoice) {
      await queryRunner.rollbackTransaction();
      return reply.status(404).send({
        error: true,
        message: 'Invoice not found'
      });
    }

    // Check if PO is closed
    if (invoice.grn?.purchaseOrder?.status === PurchaseOrderStatus.CLOSED) {
      await queryRunner.rollbackTransaction();
      return reply.status(400).send({
        error: true,
        message: 'Cannot reject invoice for a closed purchase order'
      });
    }

    // Get all approval entries for this invoice
    const invoiceRepo = queryRunner.manager.getRepository(Invoice);
    const approvalRepo = queryRunner.manager.getRepository('Approval');
    const approvals = await approvalRepo.find({ where: { requestId: invoiceId, requestType: 'INVOICE', approvalType: 'INVOICE_APPROVAL' }, order: { approvalSequence: 'ASC' } });
    const pendingApproval = approvals.find(a => a.status === 'PENDING_APPROVAL');
    if (!pendingApproval) {
      await queryRunner.rollbackTransaction();
      return reply.status(400).send({ error: true, message: 'No pending approval for this invoice.' });
    }
    const userHasRole = userRoles.some(ur => ur.role === pendingApproval.approvalRole);
    if (!userHasRole) {
      await queryRunner.rollbackTransaction();
      return reply.status(403).send({ error: true, message: `You do not have the role (${pendingApproval.approvalRole}) required for this approval step.` });
    }
    // Reject the invoice
    pendingApproval.status = 'REJECTED';
    pendingApproval.remarks = remarks;
    pendingApproval.verifiedBy = userId;
    pendingApproval.verifiedAt = new Date();
    await approvalRepo.save(pendingApproval);
    
    // Update invoice status
    invoice.status = InvoiceStatus.REJECTED;
    await queryRunner.manager.save(invoice);
    
    // Commit transaction first
    await queryRunner.commitTransaction();
    
    // Send notification after successful commit
    await ApprovalNotificationHandler.handleInvoiceApproval(
      invoiceId,
      request.user as User,
      ApprovalStatus.REJECTED,
      pendingApproval.approvalRole as ApprovalRole,
      remarks
    );
    
    return reply.status(200).send({ message: 'Invoice rejected successfully.' });
  } catch (error: any) {
    await queryRunner.rollbackTransaction();
    return reply.status(500).send({ error: true, message: error.message });
  } finally {
    await queryRunner.release();
  }
};

/**
 * Get approval history for an invoice
 */
export const getInvoiceApprovalHistory = async (
  request: AuthenticatedRequest<{ Params: { id: number } }>,
  reply: FastifyReply
) => {
  try {
    const invoiceId = request.params.id;
    const approvalRepo = AppDataSource.getRepository('Approval');
    const approvals = await approvalRepo.find({ where: { requestId: invoiceId, requestType: 'INVOICE', approvalType: 'INVOICE_APPROVAL' }, order: { approvalSequence: 'ASC' } });
    return reply.status(200).send({ approvals });
  } catch (error: any) {
    return reply.status(500).send({ error: true, message: error.message });
  }
};

/**
 * Get Invoice details by ID (function only, not exposed as route)
 */
export const getInvoiceById = async (invoiceId: number) => {
  try {
    // Find the invoice with related data and documents in a single query
    const [invoice, documents] = await Promise.all([
      AppDataSource.getRepository(Invoice)
        .createQueryBuilder('invoice')
        .leftJoinAndSelect('invoice.grn', 'grn')
        .leftJoinAndSelect('grn.purchaseOrder', 'po')
        .leftJoinAndSelect('po.quotation', 'quotation')
        .leftJoinAndSelect('po.businessUnit', 'businessUnit')
        .leftJoinAndSelect('po.costCenter', 'costCenter')
        .leftJoinAndSelect('po.items', 'poItems')
        .leftJoinAndSelect('invoice.invoiceItems', 'invoiceItems')
        .leftJoinAndSelect('invoiceItems.grnItem', 'grnItem')
        .leftJoinAndSelect('grnItem.poItem', 'poItem')
        .leftJoinAndSelect('invoice.creator', 'creator')
        .where('invoice.id = :invoiceId', { invoiceId })
        .getOne(),
        
      AppDataSource.getRepository(Document)
        .createQueryBuilder('doc')
        .where('doc.entityType = :entityType AND doc.entityId = :invoiceId', {
          entityType: 'invoice',
          invoiceId
        })
        .getMany()
    ]);

    if (!invoice) {
      return { error: true, message: 'Invoice not found' };
    }

    // Map invoice to response format with related data
    return {
      id: invoice.id,
      grnId: invoice.grnId,
      invoiceNumber: invoice.invoiceNumber,
      invoiceDate: invoice.invoiceDate,
      invoiceValue: invoice.invoiceValue,
      remarks: invoice.remarks,
      vendorName: invoice.grn?.purchaseOrder?.quotation?.vendorName || '',
      vendorId: invoice.grn?.purchaseOrder?.quotation?.vendorId || '',
      createdBy: invoice.creator ? {
        id: invoice.creator.id,
        username: invoice.creator.username,
        email: invoice.creator.email,
        name: invoice.creator.name || invoice.creator.username
      } : invoice.createdBy,
      createdAt: invoice.createdAt,
      updatedAt: invoice.updatedAt,
      // Include payment request information
      paymentRequestStatus: invoice.paymentRequestStatus,
      paymentRequestId: invoice.paymentRequestId,
      paymentRequestExternalId: invoice.paymentRequestExternalId,
      paymentRequestCreatedAt: invoice.paymentRequestCreatedAt,
      paymentRequestUpdatedAt: invoice.paymentRequestUpdatedAt,
      paymentRequestRemarks: invoice.paymentRequestRemarks,
      attachments: documents || [],
      prId: invoice.grn.purchaseOrder.prId,
      grn: invoice.grn ? {
        id: invoice.grn.id,
        poId: invoice.grn.poId,
        createdAt: invoice.grn.createdAt
      } : null,
      status: invoice.status as InvoiceStatus,
      poDetails: invoice.grn?.purchaseOrder ? {
        id: invoice.grn.purchaseOrder.id,
        poNumber: invoice.grn.purchaseOrder.poNumber,
        status: invoice.grn.purchaseOrder.status,
        type: invoice.grn.purchaseOrder.type,
        totalAmount: invoice.grn.purchaseOrder.totalAmount,
        currency: invoice.grn.purchaseOrder.currency,
        businessUnit: invoice.grn.purchaseOrder.businessUnit ? invoice.grn.purchaseOrder.businessUnit.name : null,
        costCenter: invoice.grn.purchaseOrder.costCenter ? invoice.grn.purchaseOrder.costCenter.name : null,
        paymentTerms: invoice.grn.purchaseOrder.paymentTerms,
        deliveryTerms: invoice.grn.purchaseOrder.deliveryTerms,
        expectedDeliveryDate: invoice.grn.purchaseOrder.expectedDeliveryDate,
        billTo: invoice.grn.purchaseOrder.billTo,
        shipToAddress: invoice.grn.purchaseOrder.shipToAddress,
        remarks: invoice.grn.purchaseOrder.remarks,
        prId: invoice.grn.purchaseOrder.prId,
        items: invoice.grn.purchaseOrder.items?.map(poItem => ({
          id: poItem.id,
          itemName: poItem.itemName,
          itemDescription: poItem.itemDescription,
          quantity: poItem.quantity,
          uom: poItem.uom,
          pricePerUnit: poItem.pricePerUnit,
          gstPercentage: poItem.gstPercentage,
          gstAmount: poItem.gstAmount,
          totalValue: poItem.totalValue,
          itemCategory: poItem.itemCategory?.name || null,
        })) || []
      } : null,
      items: invoice.invoiceItems ? invoice.invoiceItems.map(item => ({
        id: item.id,
        amount: item.amount,
        tax: item.tax,
        grnItem: item.grnItem ? {
          id: item.grnItem.id,
          grnQuantity: item.grnItem.grnQuantity,
          poItem: item.grnItem.poItem ? {
            id: item.grnItem.poItem.id,
            itemName: item.grnItem.poItem.itemName,
            quantity: item.grnItem.poItem.quantity,
            uom: item.grnItem.poItem.uom,
            unitPrice: (item.grnItem.poItem as any).unitPrice || 0
          } : null
        } : null
      })) : []
    };
  } catch (error: any) {
    console.error('Error retrieving invoice details:', error);
    return { error: true, message: 'Failed to retrieve invoice details' };
  }
};

/**
 * Create a payment request for an invoice
 */
export const createPaymentRequest = async (
  request: AuthenticatedRequest<{ Params: { id: number } }>,
  reply: FastifyReply
) => {
  try {
    // Ensure user is authenticated
    if (!request.user) {
      return reply.status(401).send({ error: true, message: 'Authentication required' });
    }

    // Get user roles
    const userRoles = request.userRoles || [];

    // Check if user is authorized to create a payment request
    const isProcurementManager = userRoles.some(role => role.role === ApprovalRole.PROCUREMENT_MANAGER);
    const isBizFin = userRoles.some(role => role.role === ApprovalRole.BIZ_FIN);

    if (!isProcurementManager && !isBizFin) {
      return reply.status(403).send({
        error: true,
        message: 'You are not authorized to create a payment request'
      });
    }

    // Get invoice ID from params
    const invoiceId = request.params.id;

    // Create payment request
    const result = await velynkPaymentService.createPaymentRequestForInvoice(invoiceId);

    return reply.status(200).send(result);
  } catch (error: any) {
    console.error('Error creating payment request:', error);
    return reply.status(500).send({
      error: true,
      message: error.message || 'Internal server error'
    });
  }
};

/**
 * Check payment request status for an invoice
 */
export const checkPaymentRequestStatus = async (
  request: AuthenticatedRequest<{ Params: { id: number } }>,
  reply: FastifyReply
) => {
  try {
    // Ensure user is authenticated
    if (!request.user) {
      return reply.status(401).send({ error: true, message: 'Authentication required' });
    }

    // Get invoice ID from params
    const invoiceId = request.params.id;

    // Check payment request status
    const result = await velynkPaymentService.updatePaymentRequestStatus(invoiceId);

    return reply.status(200).send(result);
  } catch (error: any) {
    console.error('Error checking payment request status:', error);
    return reply.status(500).send({
      error: true,
      message: error.message || 'Internal server error'
    });
  }
};

/**
 * Valid status transitions for invoices
 */
const VALID_STATUS_TRANSITIONS: Record<InvoiceStatus, InvoiceStatus[]> = {
  [InvoiceStatus.PENDING]: [InvoiceStatus.APPROVED, InvoiceStatus.REJECTED, InvoiceStatus.VOID],
  [InvoiceStatus.APPROVED]: [InvoiceStatus.PAYMENT_COMPLETED],
  [InvoiceStatus.REJECTED]: [InvoiceStatus.PENDING, InvoiceStatus.VOID],
  [InvoiceStatus.PAYMENT_COMPLETED]: [],
  [InvoiceStatus.VOID]: [InvoiceStatus.PENDING]
};

export const updateInvoiceStatus = async (
  request: AuthenticatedRequest<{
    Params: { id: number };
    Body: { status: InvoiceStatus; remarks?: string };
  }>,
  reply: FastifyReply
) => {
  const { id: invoiceId } = request.params;
  const { status: rawStatus, remarks } = request.body;
  const status = rawStatus?.toUpperCase() as InvoiceStatus;

  const validStatuses = Object.values(InvoiceStatus);
  if (!validStatuses.includes(status)) {
    return reply.status(400).send({
      error: true,
      message: `Invalid status: ${status}. Valid values are: ${validStatuses.join(', ')}`
    });
  }

  const invoiceRepo = AppDataSource.getRepository(Invoice);
  const invoice = await invoiceRepo.findOne({
    where: { id: invoiceId },
    relations: ['grn', 'grn.purchaseOrder']
  });

  if (!invoice) {
    return reply.status(404).send({ error: true, message: 'Invoice not found' });
  }

  if (invoice.grn?.purchaseOrder?.status === PurchaseOrderStatus.CLOSED) {
    return reply.status(400).send({ error: true, message: 'Cannot update invoice for a closed PO' });
  }

  const currentStatus = invoice.status;
  const allowedTransitions = VALID_STATUS_TRANSITIONS[currentStatus] || [];
  if (currentStatus !== status && !allowedTransitions.includes(status)) {
    return reply.status(400).send({
      error: true,
      message: `Invalid status transition from ${currentStatus} to ${status}. Allowed transitions: ${allowedTransitions.join(', ') || 'none'}`
    });
  }

  const isFinanceUser = request.userRoles?.some(r => r.role === ApprovalRole.FINANCE);
  if (status === InvoiceStatus.PAYMENT_COMPLETED && !isFinanceUser) {
    return reply.status(403).send({
      error: true,
      message: 'Only users with FINANCE role can mark an invoice as PAYMENT_COMPLETED'
    });
  }

  if (status === InvoiceStatus.REJECTED && !remarks?.trim()) {
    return reply.status(400).send({
      error: true,
      message: 'Remarks are required when rejecting an invoice'
    });
  }

  try {
    invoice.status = status;
    invoice.remarks = remarks?.trim() || '';
    await invoiceRepo.save(invoice);

    return reply.status(200).send({
      success: true,
      message: `Invoice status updated to ${status} successfully`,
      data: {
        id: invoice.id,
        status: invoice.status,
        remarks: invoice.remarks,
        updatedAt: invoice.updatedAt
      }
    });
  } catch (error: any) {
    console.error('Database error:', error);
    return reply.status(500).send({ error: true, message: 'Failed to update invoice status' });
  }
};

export const deleteInvoice = async (
  request: AuthenticatedRequest<{ Params: { id: number,  } }>,
  reply: FastifyReply
) => {
  const { id: invoiceId } = request.params;

  const invoiceRepo = AppDataSource.getRepository(Invoice);

  const invoice = await invoiceRepo.findOne({
    where: { id: invoiceId },
    relations: ['invoiceItems'],
  });

  if (!invoice) {
    return reply.status(404).send({ error: true, message: 'Invoice not found' });
  }

  if (invoice.status !== InvoiceStatus.VOID) {
    return reply.status(400).send({
      error: true,
      message: 'Invoice can be deleted only in VOID state',
    });
  }

  try {
    await AppDataSource.transaction(async (transactionalEntityManager) => {
      for (const item of invoice.invoiceItems) {
        await transactionalEntityManager.remove(item);
      }
      await transactionalEntityManager.remove(invoice);
    });

    return reply.status(200).send({
      success: true,
      message: 'Invoice deleted successfully',
    });
  } catch (error: any) {
    console.error('Transaction error:', error);
    return reply.status(500).send({
      error: true,
      message: 'Failed to delete invoice',
    });
  }
};