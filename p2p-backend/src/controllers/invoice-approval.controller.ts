import { FastifyRequest, FastifyReply } from 'fastify';
import { AppDataSource } from '../db/typeorm.config';
import { Approval } from '../entities/Approval.entity';
import { InvoiceStatus } from '../entities/Invoice.entity';
import { PurchaseRequest } from '../entities/PurchaseRequest.entity';
import { Invoice } from '../entities/Invoice.entity';
import { PurchaseOrderStatus } from '../entities/PurchaseOrder.entity';
import { ApprovalRole, ApprovalStatus, ApprovalType, EmailRequestType, RequestType } from '../constants/enums';
import { UserRole } from '../entities/UserRole.entity';
import { User } from '../entities/User.entity';
import { ApprovalNotificationHandler } from '../services/approval-notification.handler';
import { notifyNextApprovers } from '../utils/next-approval-email';

// Interface for approval action request body
interface ApprovalActionBody {
  status: ApprovalStatus;
  remarks?: string;
}

// Interface for approval action request params
interface ApprovalActionParams {
  id: number;
}

/**
 * Process approval for a specific role
 * @param role The approval role (CC_HEAD, BIZ_FIN, PROCUREMENT_MANAGER)
 * @param request The FastifyRequest object
 * @param reply The FastifyReply object
 */


/**
 * Processes an invoice approval action for a specific role
 */
const processInvoiceApproval = async (
  role: ApprovalRole,
  request: FastifyRequest<{
    Body: ApprovalActionBody;
    Params: { id: number };
  }>,
  reply: FastifyReply
) => {
  const { id } = request.params;
  const { status, remarks } = request.body;
  const user = (request as any).user;

  if (!user) {
    return reply.status(401).send({
      error: true,
      message: 'Unauthorized'
    });
  }

  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Check if the user has the required role
    const userRoleRepo = queryRunner.manager.getRepository(UserRole);
    const userRoles = await userRoleRepo.find({
        where: { userId: user.id }
    });

    const hasRequiredRole = userRoles.some((userRole: UserRole) => userRole.role === role);
    if (!hasRequiredRole) {
        await queryRunner.rollbackTransaction();
        return reply.status(403).send({
        error: true,
        message: `You don't have the ${role} role required to perform this action`
        });
    }

    // Get the invoice with FOR UPDATE lock to prevent race conditions
    const invoice = await queryRunner.manager
      .createQueryBuilder(Invoice, 'invoice')
      .setLock('pessimistic_write')
      .leftJoinAndSelect('invoice.grn', 'grn')
      .leftJoinAndSelect('grn.purchaseOrder', 'purchaseOrder')
      .where('invoice.id = :id', { id })
      .getOne();

    if (!invoice) {
      await queryRunner.rollbackTransaction();
      return reply.status(404).send({
        error: true,
        message: 'Invoice not found'
      });
    }

    // Check if PO is closed
    if (invoice.grn?.purchaseOrder?.status === PurchaseOrderStatus.CLOSED) {
      await queryRunner.rollbackTransaction();
      return reply.status(400).send({
        error: true,
        message: 'Cannot approve invoice for a closed purchase order'
      });
    }

    // For Finance approval, check if Business Finance has approved first
    if (role === ApprovalRole.FINANCE) {
      // Get BizFin approval with FOR UPDATE lock
      const bizFinApproval = await queryRunner.manager
        .createQueryBuilder(Approval, 'approval')
        .setLock('pessimistic_write')
        .where('approval.requestId = :requestId', { requestId: id })
        .andWhere('approval.requestType = :requestType', { requestType: RequestType.INVOICE })
        .andWhere('approval.approvalType = :approvalType', { approvalType: ApprovalType.INVOICE_APPROVAL })
        .andWhere('approval.approvalRole = :approvalRole', { approvalRole: ApprovalRole.BIZ_FIN })
        .getOne();

      if (!bizFinApproval || bizFinApproval.status !== ApprovalStatus.APPROVED) {
        await queryRunner.rollbackTransaction();
        return reply.status(400).send({
          error: true,
          message: 'Business Finance approval is required before Finance approval'
        });
      }

      invoice.status = InvoiceStatus.APPROVED;
      await queryRunner.manager.save(invoice);
    }

    // Get approval entry with FOR UPDATE lock
    const approval = await queryRunner.manager
      .createQueryBuilder(Approval, 'approval')
      .setLock('pessimistic_write')
      .where('approval.requestId = :requestId', { requestId: id })
      .andWhere('approval.requestType = :requestType', { requestType: RequestType.INVOICE })
      .andWhere('approval.approvalType = :approvalType', { approvalType: ApprovalType.INVOICE_APPROVAL })
      .andWhere('approval.approvalRole = :approvalRole', { approvalRole: role })
      .getOne();

    if (!approval) {
      await queryRunner.rollbackTransaction();
      return reply.status(404).send({
        error: true,
        message: `No approval entry found for ${role} role`
      });
    }

    // Check if approval is already processed to prevent duplicate processing
    if (approval.status !== ApprovalStatus.PENDING_APPROVAL) {
      await queryRunner.rollbackTransaction();
      return reply.status(400).send({
        error: true,
        message: `This invoice has already been ${approval.status.toLowerCase()} by ${role}`
      });
    }

    // Update the approval status
    approval.status = status;
    if (remarks !== undefined) {
      approval.remarks = remarks;
    }
    approval.verifiedBy = user.id;
    approval.verifiedAt = new Date();
    await queryRunner.manager.save(approval);

    // Fetch the creator before committing the transaction
    const creator = await queryRunner.manager.findOne(User, {
      where: { id: invoice.createdBy },
      relations: ['roles']
    });
    
    // Store data needed for notifications after transaction
    const approvalData = {
      id,
      status,
      role,
      remarks,
      creator,
      approvalSequence: approval.approvalSequence
    };
    
    // Commit the transaction
    await queryRunner.commitTransaction();
    
    // Send notifications outside of transaction to reduce overhead
    try {
      // Send notification to creator
      if (approvalData.creator) {
        // Use queueService directly instead of waiting for notification to complete
        ApprovalNotificationHandler.handleInvoiceApproval(
          approvalData.id,
          approvalData.creator,  
          approvalData.status,
          approvalData.role as ApprovalRole,
          approvalData.remarks,
          EmailRequestType.INVOICE
        ).catch(err => console.error(`Error sending creator notification: ${err.message}`));
      } else {
        console.error(`Creator not found for invoice ${approvalData.id}`);
      }
      
      // Notify next approvers if approved
      if (approvalData.status === ApprovalStatus.APPROVED) {
        notifyNextApprovers({
          requestId: approvalData.id,
          requestType: "Invoice",
          approvalType: ApprovalType.INVOICE_APPROVAL,
          currentApprovalSequence: approvalData.approvalSequence + 1,
          status: ApprovalStatus.PENDING_APPROVAL,
          remarks: '',
          emailRequestType: EmailRequestType.INVOICE
        }).catch(err => console.error(`Error notifying next approvers: ${err.message}`));
      }
    } catch (notificationError) {
      // Log notification errors but don't fail the request
      console.error(`Notification error for invoice ${id}:`, notificationError);
    }
    
    return reply.status(200).send({
      success: true,
      message: `Invoice ${status === ApprovalStatus.APPROVED ? 'approved' : 'rejected'} by ${role === ApprovalRole.BIZ_FIN ? 'Business Finance' : 'Finance'}`,
      role: role
    });
  } catch (error: any) {
    await queryRunner.rollbackTransaction();
    return reply.status(500).send({
      error: true,
      message: error.message
    });
  } finally {
    await queryRunner.release();
  }
};

/**
 * Process Business Finance approval for an invoice
 */
export const processInvoiceBizFinApproval = async (
  request: FastifyRequest<{
    Body: ApprovalActionBody;
    Params: { id: number };
  }>,
  reply: FastifyReply
) => {
  return processInvoiceApproval(ApprovalRole.BIZ_FIN, request, reply);
};

/**
 * Process Finance approval for an invoice
 */
export const processInvoiceFinanceApproval = async (
  request: FastifyRequest<{
    Body: ApprovalActionBody;
    Params: { id: number };
  }>,
  reply: FastifyReply
) => {
  return processInvoiceApproval(ApprovalRole.FINANCE, request, reply);
};

/**
 * Get all approvals for a purchase request
 */
export const getPurchaseRequestApprovals = async (
  request: FastifyRequest<{
    Params: ApprovalActionParams;
  }>,
  reply: FastifyReply
) => {
  const { id } = request.params;

  try {
    // Get the purchase request
    const prRepo = AppDataSource.getRepository(PurchaseRequest);
    const purchaseRequest = await prRepo.findOne({
      where: { id }
    });

    if (!purchaseRequest) {
      return reply.status(404).send({
        error: true,
        message: 'Purchase request not found'
      });
    }

    // Get all approvals for this purchase request
    const approvalRepo = AppDataSource.getRepository(Approval);
    const approvals = await approvalRepo.find({
      where: {
        requestId: id,
        requestType: RequestType.INVOICE,
        approvalType: ApprovalType.INVOICE_APPROVAL
      },
      order: {
        approvalSequence: 'ASC'
      }
    });

    return reply.status(200).send(approvals);
  } catch (error) {
    console.error('Error getting purchase request approvals:', error);
    return reply.status(500).send({
      error: true,
      message: 'Internal server error'
    });
  }
};

/**
 * Get all approvals for an invoice
 */
export const getInvoiceApprovals = async (
  request: FastifyRequest<{ Params: { id: number } }>,
  reply: FastifyReply
) => {
  try {
    const id = request.params.id;

    const invoiceRepo = AppDataSource.getRepository(Invoice);
    const invoice = await invoiceRepo.findOne({
      where: { id }
    });

    if (!invoice) {
      return reply.status(404).send({
        error: true,
        message: 'Invoice not found'
      });
    }
    
    // Get all approvals for this invoice
    const approvals = await AppDataSource.getRepository(Approval).find({
      where: {
        requestId: id,
        requestType: RequestType.INVOICE,
        approvalType: ApprovalType.INVOICE_APPROVAL
      },
      relations: ['verifiedBy'],
      order: {
        approvalSequence: 'ASC'
      }
    });

    return reply.status(200).send(approvals);
  } catch (error: any) {
    return reply.status(500).send({
      error: true,
      message: error.message
    });
  }
};
