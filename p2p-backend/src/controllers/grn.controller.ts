import { FastifyRequest, FastifyReply } from 'fastify';
import { In } from 'typeorm';
import { AppDataSource } from '../db/typeorm.config';
import { Grn } from '../entities/Grn.entity';
import { GrnItem } from '../entities/GrnItem.entity';
import { PurchaseOrder, PurchaseOrderStatus } from '../entities/PurchaseOrder.entity';
import { PoItem } from '../entities/PoItem.entity';
import { Document } from '../entities/document.entity';
import { Invoice, InvoiceStatus } from '../entities/Invoice.entity';
import { EntityType } from '../schemas/document.schema';
import { User } from '../entities/User.entity';
import { ApprovalRole } from '../constants/enums';
import { documentService } from '../services/document.service';

// Custom interface for request with authentication data
interface RequestWithUser {
  user?: {
    id: number;
    [key: string]: any;
  };
  userRoles?: Array<{ role: string; [key: string]: any; }>;
}

// Type for authenticated request
type AuthenticatedRequest<T extends Record<string, any> = {}> = FastifyRequest<T> & RequestWithUser;

// Interface for creating a GRN with items
interface CreateGrnBody {
  poId: number;
  grnItems: {
    poItemId: number;
    grnQuantity: number;
  }[];
  fileKey?: string;     // For backward compatibility (single file)
  fileKeys?: string[];  // For multiple files (preferred for invoice file upload)
}

// Interface for listing GRNs by PO ID
interface GetGrnsByPoIdParams {
  poId: number;
}

/**
 * Create a new GRN with items for a purchase order
 * Requires invoice file upload
 */
export const createGrn = async (
  request: AuthenticatedRequest<{ Body: CreateGrnBody }>,
  reply: FastifyReply
) => {
  // Start a transaction to ensure data consistency
  const queryRunner = AppDataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Ensure user is authenticated
    if (!request.user) {
      return reply.status(401).send({ error: true, message: 'Authentication required' });
    }

    // Extract request data
    const { poId, grnItems, fileKey, fileKeys } = request.body;
    
    // Process file attachments if provided (invoice file is optional)
    const allFileKeys = [];
    if (fileKey && typeof fileKey === 'string') {
      allFileKeys.push(fileKey);
    }
    if (fileKeys && Array.isArray(fileKeys)) {
      allFileKeys.push(...fileKeys);
    }
    
    // No validation for mandatory file attachment

    // Get user ID for records
    const userId = (request.user as User).id;
    const userRoles = request.userRoles || [];

    // Verify the PO exists and is not closed
    const purchaseOrder = await queryRunner.manager.findOne(PurchaseOrder, {
      where: { id: poId },
      relations: ['purchaseRequest']
    });

    if (!purchaseOrder) {
      return reply.status(404).send({ 
        error: true, 
        message: 'Purchase order not found' 
      });
    }

    // Check if PO is closed
    if (purchaseOrder.status === PurchaseOrderStatus.CLOSED) {
      return reply.status(400).send({
        error: true,
        message: 'Cannot create GRN for a closed purchase order'
      });
    }
    
    // Check if user is authorized to create a GRN for this PO
    const isProcurementManager = userRoles.some(role => role.role === ApprovalRole.PROCUREMENT_MANAGER);
    const isPrCreator = purchaseOrder.purchaseRequest && String(purchaseOrder.purchaseRequest.createdBy) === String(userId);
    
    if (!isProcurementManager && !isPrCreator) {
      return reply.status(403).send({
        error: true,
        message: 'You are not authorized to create a GRN for this purchase order'
      });
    }

    // Verify that all PO items exist and get their details
    const poItemIds = grnItems.map(item => item.poItemId);
    const poItems = await queryRunner.manager.find(PoItem, {
      where: { 
        id: In(poItemIds),
        poId: poId 
      }
    });

    if (poItems.length !== poItemIds.length) {
      return reply.status(400).send({
        error: true,
        message: 'One or more PO items do not exist in the specified purchase order'
      });
    }

    // Create and save the GRN
    const grn = new Grn();
    grn.poId = poId;
    grn.createdBy = userId;

    const savedGrn = await queryRunner.manager.save(grn);

    // Create and save the GRN items
    const grnItemsToSave = grnItems.map(grnItem => {
      const newGrnItem = new GrnItem();
      newGrnItem.grnId = savedGrn.id;
      newGrnItem.poItemId = grnItem.poItemId;
      newGrnItem.grnQuantity = grnItem.grnQuantity;
      newGrnItem.createdBy = userId; // Set the createdBy field to the user ID
      return newGrnItem;
    });

    const savedGrnItems = await queryRunner.manager.save(grnItemsToSave);

    // Handle file attachments (if any)
    // Remove duplicates if any
    const uniqueFileKeys = [...new Set(allFileKeys)];
    
    // Only create document records if files were uploaded
    if (uniqueFileKeys.length > 0) {
      // Create document records for each file key
      const documentRepo = queryRunner.manager.getRepository(Document);
      const documentPromises = uniqueFileKeys.map(fileKey => {
        const document = documentRepo.create({
          entityType: 'grn',
          entityId: savedGrn.id,
          fileKey,
          fileName: 'Invoice',
          fileSize: 0,
          contentType: 'application/octet-stream'
        });
        
        return queryRunner.manager.save(document);
      });
      
      await Promise.all(documentPromises);
    }

    // Commit the transaction
    await queryRunner.commitTransaction();

    return reply.status(201).send({
      message: 'GRN created successfully',
      grn: {
        id: savedGrn.id,
        poId: savedGrn.poId,
        createdAt: savedGrn.createdAt,
        itemsCount: savedGrnItems.length,
        attachmentsCount: uniqueFileKeys.length
      }
    });
  } catch (error: any) {
    // Rollback the transaction on error
    await queryRunner.rollbackTransaction();
    console.error('Error creating GRN:', error);
    return reply.status(500).send({ 
      error: true, 
      message: 'Internal server error' 
    });
  } finally {
    // Release the query runner
    await queryRunner.release();
  }
};

/**
 * Get all GRNs for a specific purchase order
 */
export const getGrnsByPoId = async (
  request: AuthenticatedRequest<{ Params: GetGrnsByPoIdParams }>,
  reply: FastifyReply
) => {
  try {
    const { poId } = request.params;
    
    // Check user roles for permission to view invoice details
    const userRoles = request.userRoles || [];
    const isProcurementManager = userRoles.some(role => role.role === ApprovalRole.PROCUREMENT_MANAGER);
    const isBizFin = userRoles.some(role => role.role === ApprovalRole.BIZ_FIN);
    const canViewInvoiceDetails = isProcurementManager || isBizFin;

    // Verify the PO exists
    const purchaseOrder = await AppDataSource.getRepository(PurchaseOrder)
      .findOne({
        where: { id: poId }
      });

    if (!purchaseOrder) {
      return reply.status(404).send({ 
        error: true, 
        message: 'Purchase order not found' 
      });
    }

    // Find GRNs with related data - directly include invoices in the same query
    const grns = await AppDataSource.getRepository(Grn)
      .createQueryBuilder('grn')
      .leftJoinAndSelect('grn.purchaseOrder', 'po')
      .leftJoinAndSelect('po.businessUnit', 'businessUnit')
      .leftJoinAndSelect('po.costCenter', 'costCenter')
      .leftJoinAndSelect('po.quotation', 'quotation')
      .leftJoinAndSelect('grn.grnItems', 'grnItems')
      .leftJoinAndSelect('grnItems.poItem', 'poItem')
      .leftJoinAndSelect('poItem.itemCategory', 'itemCategory') // Include item category details
      .leftJoinAndSelect('grn.creator', 'creator') // Include creator details
      .leftJoinAndMapMany(
        'grn.invoices', // Property to map to
        'grn.invoices', // Relation path
        'invoices',     // Alias
        'invoices.status != :voidStatus',
        { voidStatus: InvoiceStatus.REJECTED }
      )
      .leftJoinAndSelect('invoices.creator', 'invoiceCreator') // Include invoice creator details
      .leftJoinAndSelect('invoices.invoiceItems', 'invoiceItems') // Include invoice items
      .leftJoinAndSelect('invoiceItems.grnItem', 'invoiceGrnItem') // Include related GRN item
      .leftJoinAndSelect('invoiceGrnItem.poItem', 'invoicePoItem') // Include related PO item
      .leftJoinAndSelect('invoicePoItem.itemCategory', 'invoiceItemCategory') // Include item category
      .where('grn.poId = :poId', { poId })
      .getMany();
    
    // Get GRN IDs to fetch documents
    const grnIds = grns.map(grn => grn.id);
    
    // Initialize empty documents array
    let documents: Document[] = [];
    
    // Only query documents if we have GRN IDs to search for
    if (grnIds.length > 0) {
      documents = await AppDataSource.getRepository(Document)
        .createQueryBuilder('doc')
        .where('doc.entityType = :entityType AND doc.entityId IN (:...grnIds)', {
          entityType: 'grn',
          grnIds
        })
        .getMany();
    }

    // Group documents by GRN ID
    const documentsByGrnId = documents.reduce<Record<number, Document[]>>((acc, doc) => {
      const grnId = doc.entityId;
      if (!acc[grnId]) {
        acc[grnId] = [];
      }
      acc[grnId].push(doc);
      return acc;
    }, {});
    
    // We don't need to fetch invoices separately anymore as they're already loaded
    // with the GRN query using the leftJoinAndSelect

    // Map GRNs to response format with related data
    const response = await Promise.all(grns.map(async grn => {
      const baseResponse = {
        id: grn.id,
        poId: grn.poId,
        createdBy: grn.creator ? {
          id: grn.creator.id,
          username: grn.creator.username,
          email: grn.creator.email,
          name: grn.creator.name || grn.creator.username
        } : grn.createdBy,
        createdAt: grn.createdAt,
        updatedAt: grn.updatedAt,
        purchaseOrder: {
          id: grn.purchaseOrder.id,
          poNumber: grn.purchaseOrder.poNumber,
          status: grn.purchaseOrder.status,
          vendorId: grn.purchaseOrder.vendorId
        },
        items: grn.grnItems.map(item => ({
          id: item.id,
          grnQuantity: item.grnQuantity,
          poItem: {
            id: item.poItem.id,
            itemName: item.poItem.itemName,
            itemDescription: item.poItem.itemDescription || '',
            quantity: item.poItem.quantity,
            uom: item.poItem.uom,
            unitPrice: item.poItem.pricePerUnit || 0, // Use pricePerUnit from PoItem
            totalPrice: item.grnQuantity * (item.poItem.pricePerUnit || 0), // Calculate total price
            gstPercentage: item.poItem.gstPercentage || 0,
            gstAmount: item.poItem.gstAmount || 0,
            itemCategory: item.poItem.itemCategory ? {
              id: item.poItem.itemCategory.id,
              name: item.poItem.itemCategory.name,
              description: item.poItem.itemCategory.description,
              type: item.poItem.itemCategory.type
            } : null
          }
        })),
        attachments: documentsByGrnId[grn.id] || []
      };
      
      // Add invoice details if the user has permission and an invoice exists for this GRN
      if (canViewInvoiceDetails && grn.invoices && grn.invoices.length > 0) {
        // Use the first invoice - typically there should be one invoice per GRN
        const invoice = grn.invoices[0];

        // Get all invoice IDs to fetch documents in batch
        const invoiceIds = grns
          .filter(g => g.invoices && g.invoices.length > 0)
          .map(g => g.invoices[0].id);
          
        // Fetch all documents for all invoices in a single query
        const allInvoiceDocuments = invoiceIds.length > 0 ? 
          await AppDataSource.getRepository(Document)
            .createQueryBuilder('doc')
            .where('doc.entityType = :entityType AND doc.entityId IN (:...invoiceIds)', {
              entityType: 'invoice',
              invoiceIds
            })
            .getMany() : [];
            
        // Group documents by invoice ID
        const documentsByInvoiceId = allInvoiceDocuments.reduce<Record<number, Document[]>>((acc, doc) => {
          const invoiceId = doc.entityId;
          if (!acc[invoiceId]) {
            acc[invoiceId] = [];
          }
          acc[invoiceId].push(doc);
          return acc;
        }, {});
        
        // Get attachments for this specific invoice
        const attachments = documentsByInvoiceId[invoice.id] || [];
        
        // Generate URLs in parallel if needed
        if (attachments.length > 0) {
          const s3Service = await import('../services/s3.service').then(m => m.default);
          await Promise.all(attachments.map(async (attachment: Document) => {
            if (!attachment.fileUrl && attachment.fileKey) {
              try {
                attachment.fileUrl = await s3Service.getFileUrl(attachment.fileKey);
              } catch (error) {
                console.error(`Failed to generate URL for file ${attachment.fileKey}:`, error);
                attachment.fileUrl = '';
              }
            }
          }));
        }
        
        // Format invoice items for the response
        const formattedInvoiceItems = invoice.invoiceItems ? invoice.invoiceItems.map(item => {
          const poItem = item.grnItem?.poItem;
          return {
            id: item.id,
            invoiceQuantity: item.quantity,
            pricePerUnit: item.pricePerUnit,
            totalPrice: item.totalPrice,
            tax: item.tax,
            amount: item.amount,
            itemCategoryDetails: poItem?.itemCategory ? {
              id: poItem.itemCategory.id,
              name: poItem.itemCategory.name,
              description: poItem.itemCategory.description
            } : null,
            itemName: poItem?.itemName,
            itemDescription: poItem?.itemDescription,
          };
        }) : [];
        
        return {
          ...baseResponse,
          invoice: {
            id: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            invoiceDate: invoice.invoiceDate,
            invoiceValue: invoice.invoiceValue,
            createdBy: invoice.creator ? {
              id: invoice.creator.id,
              username: invoice.creator.username,
              email: invoice.creator.email,
              name: invoice.creator.name || invoice.creator.username
            } : invoice.createdBy,
            createdAt: invoice.createdAt,
            items: formattedInvoiceItems,
            businessUnit: grn.purchaseOrder.businessUnit ? grn.purchaseOrder.businessUnit.name : null,
            costCenter: grn.purchaseOrder.costCenter ? grn.purchaseOrder.costCenter.name : null, 
            vendorName: grn?.purchaseOrder?.quotation?.vendorName,
            attachments: attachments,
          }
        };
      }
      
      return baseResponse;
    }));

    return reply.status(200).send({
      message: `Found ${response.length} GRNs for purchase order #${poId}`,
      grns: response
    });
  } catch (error: any) {
    console.error('Error retrieving GRNs:', error);
    return reply.status(500).send({ 
      error: true, 
      message: 'Internal server error' 
    });
  }
};


export const deleteGrn = async (
  request: AuthenticatedRequest<{ Params: { id: number } }>,
  reply: FastifyReply
) => {
  const { id: grnId } = request.params;

  const grnRepo = AppDataSource.getRepository(Grn);
  const grn = await grnRepo.findOne({ 
    where: { id: grnId },
    relations: ['grnItems', 'invoices']
  });

  if (!grn) {
    return reply.status(404).send({ error: true, message: 'GRN not found' });
  }

  if (grn.invoices && grn.invoices.length > 0) {
    return reply.status(400).send({ error: true, message: 'GRN cannot be deleted as it has invoices' });
  }

  try {
    await AppDataSource.transaction(async (transactionalEntityManager) => {
      for (const item of grn.grnItems) {
        await transactionalEntityManager.remove(item);
      }
      await transactionalEntityManager.remove(grn);
    });

    return reply.status(200).send({
      success: true,
      message: 'GRN deleted successfully',
    });
  } catch (error: any) {
    console.error('Transaction error:', error);
    return reply.status(500).send({
      error: true,
      message: 'Failed to delete GRN',
    });
  }
};
