// src/scripts/deletePOsAndPRs.ts
import { AppDataSource } from '../db/typeorm.config';

interface QueryResult {
  id: number;
}

// DANGER: This script will delete ALL purchase orders, purchase requests, approvals, quotations, and related data
// This is a destructive operation that cannot be undone!
// To prevent accidental execution, this script is disabled by default.
// If you really need to run this script, set ALLOW_DESTRUCTIVE_OPERATION=true in environment variables
// or uncomment the line below and set it to true:
const ALLOW_DESTRUCTIVE_OPERATION = false; // Set to true only if you really want to delete all data

async function deletePOsAndPRs() {
  // Safety check to prevent accidental execution
  if (!ALLOW_DESTRUCTIVE_OPERATION && process.env.ALLOW_DESTRUCTIVE_OPERATION !== 'true') {
    throw new Error(`
❌ SAFETY CHECK FAILED: This script is disabled to prevent accidental data loss.

This script will DELETE ALL:
- Purchase Orders and their items
- Purchase Requests and their items
- GRNs and their items
- Invoices and their items
- Approvals
- Quotations
- Documents

This operation is IRREVERSIBLE and will cause PERMANENT DATA LOSS!

To run this script, you must explicitly enable it by either:
1. Setting environment variable: ALLOW_DESTRUCTIVE_OPERATION=true
2. Or modifying the ALLOW_DESTRUCTIVE_OPERATION constant in this file to true

⚠️  WARNING: Only do this if you are absolutely certain you want to delete all data!
    `);
  }

  console.log('⚠️  WARNING: Starting destructive operation in 5 seconds...');
  console.log('⚠️  This will delete ALL purchase orders, requests, and related data!');
  console.log('⚠️  Press Ctrl+C to cancel if this was not intended!');

  // Give user 5 seconds to cancel
  await new Promise(resolve => setTimeout(resolve, 5000));
  try {
    const ds = await AppDataSource.initialize();
    const queryRunner = ds.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const poIds = await queryRunner.query('SELECT id FROM purchase_orders') as QueryResult[];
      const prIds = await queryRunner.query('SELECT id FROM purchase_request') as QueryResult[];
      const approvalIds = await queryRunner.query('SELECT id FROM approvals') as QueryResult[];
      const quotationIds = await queryRunner.query('SELECT id FROM quotations') as QueryResult[];
      const documentIds = await queryRunner.query('SELECT id FROM documents') as QueryResult[];

      if (poIds.length === 0 && prIds.length === 0 && approvalIds.length === 0 && quotationIds.length === 0 && documentIds.length === 0) {
        console.log('No purchase orders, requests, approvals, quotations, or documents found.');
        return;
      }

      console.log(`Found ${poIds.length} POs, ${prIds.length} PRs, ${approvalIds.length} approvals, ${quotationIds.length} quotations, and ${documentIds.length} documents`);

      const poIdList = poIds.map(p => p.id);
      const prIdList = prIds.map(p => p.id);
      const approvalIdList = approvalIds.map(a => a.id);
      const quotationIdList = quotationIds.map(q => q.id);
      const documentIdList = documentIds.map(d => d.id);

      let grnIds: QueryResult[] = [];
      if (poIdList.length > 0) {
        grnIds = await queryRunner.query(
          `SELECT DISTINCT id FROM grns WHERE po_id IN (${poIdList.join(',')})`
        );
      }

      const grnIdList = grnIds.map(g => g.id);

      let invoiceIds: QueryResult[] = [];
      if (grnIdList.length > 0) {
        invoiceIds = await queryRunner.query(
          `SELECT DISTINCT id FROM invoices WHERE grn_id IN (${grnIdList.join(',')})`
        );
      }

      const invoiceIdList = invoiceIds.map(i => i.id);

      if (invoiceIdList.length > 0) {
        console.log(`Deleting ${invoiceIdList.length} invoices and items`);
        await queryRunner.query(`DELETE FROM invoice_items WHERE invoice_id IN (${invoiceIdList.join(',')})`);
        await queryRunner.query(`DELETE FROM invoices WHERE id IN (${invoiceIdList.join(',')})`);
      }

      if (grnIdList.length > 0) {
        console.log(`Deleting ${grnIdList.length} GRNs and items`);
        await queryRunner.query(`DELETE FROM grn_items WHERE grn_id IN (${grnIdList.join(',')})`);
        await queryRunner.query(`DELETE FROM grns WHERE id IN (${grnIdList.join(',')})`);
      }

      if (poIdList.length > 0) {
        console.log(`Deleting ${poIdList.length} POs and items`);
        await queryRunner.query(`DELETE FROM po_items WHERE po_id IN (${poIdList.join(',')})`);
        await queryRunner.query(`DELETE FROM purchase_orders WHERE id IN (${poIdList.join(',')})`);
      }

      if (prIdList.length > 0) {
        console.log(`Deleting ${prIdList.length} PRs and items`);
        await queryRunner.query(`DELETE FROM quotations WHERE pr_id IN (${prIdList.join(',')})`);
        await queryRunner.query(`DELETE FROM pr_items WHERE pr_id IN (${prIdList.join(',')})`);
        await queryRunner.query(`DELETE FROM purchase_request WHERE id IN (${prIdList.join(',')})`);
      }

      if (approvalIdList.length > 0) {
        console.log(`Deleting ${approvalIdList.length} approvals`);
        await queryRunner.query(`DELETE FROM approvals WHERE id IN (${approvalIdList.join(',')})`);
      }

      if (quotationIdList.length > 0) {
        console.log(`Deleting ${quotationIdList.length} quotations`);
        await queryRunner.query(`DELETE FROM quotations WHERE id IN (${quotationIdList.join(',')})`);
      }

      if (documentIdList.length > 0) {
        console.log(`Deleting ${documentIdList.length} documents`);
        await queryRunner.query(`DELETE FROM documents WHERE id IN (${documentIdList.join(',')})`);
      }

      await queryRunner.commitTransaction();
      console.log('✅ All related data deleted successfully.');

    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error('❌ Deletion error:', err);
      throw err;
    } finally {
      await queryRunner.release();
      await ds.destroy();
      console.log('✅ Database connection closed');
    }

  } catch (error) {
    console.error('❌ Error initializing database:', error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

void deletePOsAndPRs().catch(error => {
  console.error('❌ Unhandled error in deletePOsAndPRs:', error);
  process.exit(1);
});
