// scripts/import-business-units.ts

import fs from 'fs';
import path from 'path';
import { parse, Options } from 'csv-parse';
import { AppDataSource } from '../db/typeorm.config';

type BusinessUnitRecord = {
  businessUnit: string;
  costCenters: string[];
};

async function importBusinessUnits(filename: string): Promise<void> {
  try {
    await AppDataSource.initialize();

    const csvFilePath = path.join(__dirname, 'csv', filename);
    if (!fs.existsSync(csvFilePath)) {
      console.error(`Error: File ${filename} not found in the csv folder`);
      process.exit(1);
    }

    const parserOptions: Options = {
      skip_empty_lines: true,
      from_line: 2,
      relax_column_count: true,
      trim: true,
    };

    const records: BusinessUnitRecord[] = await new Promise((resolve, reject) => {
      const results: BusinessUnitRecord[] = [];
      fs.createReadStream(csvFilePath)
        .pipe(parse(parserOptions))
        .on('data', (row: string[]) => {
          if (row.length === 0 || !row[0]?.trim()) return;

          const businessUnit = row[0].trim();
          const costCenters = row
            .slice(1)
            .filter((value) => value?.trim())
            .map((v) => v.trim());

          results.push({ businessUnit, costCenters });
        })
        .on('end', () => resolve(results))
        .on('error', reject);
    });

    for (const { businessUnit, costCenters } of records) {
      const queryRunner = AppDataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        const existing = await queryRunner.query(
          `SELECT id FROM business_units WHERE name = ?`,
          [businessUnit]
        );

        let businessUnitId: number;
        if (existing.length > 0) {
          businessUnitId = existing[0].id;
          console.log('Using existing business unit:', businessUnit);
        } else {
          const result = await queryRunner.query(
            `INSERT INTO business_units (name) VALUES (?)`,
            [businessUnit]
          );
          businessUnitId = result.insertId;
          console.log('Created new business unit:', businessUnit);
        }

        for (const cc of costCenters) {
          const existingCC = await queryRunner.query(
            `SELECT id FROM cost_centers WHERE name = ? AND business_unit_id = ?`,
            [cc, businessUnitId]
          );

          if (existingCC.length > 0) {
            console.log(
              `Cost center "${cc}" already exists for business unit "${businessUnit}"`
            );
            continue;
          }

          await queryRunner.query(
            `INSERT INTO cost_centers (name, business_unit_id) VALUES (?, ?)`,
            [cc, businessUnitId]
          );
          console.log(
            `Inserted cost center "${cc}" for business unit "${businessUnit}"`
          );
        }

        await queryRunner.commitTransaction();
      } catch (err) {
        await queryRunner.rollbackTransaction();
        console.error(`Failed to process ${businessUnit}:`, err);
        throw err;
      } finally {
        await queryRunner.release();
      }
    }

    console.log('Business units and cost centers imported successfully!');
  } catch (err) {
    console.error('Import failed:', err);
    process.exit(1);
  } finally {
    await AppDataSource.destroy();
    process.exit(0);
  }
}

const args = process.argv.slice(2);
if (args.length < 1) {
  console.error('Usage: ts-node import-business-units.ts <filename>');
  process.exit(1);
}

importBusinessUnits(args[0]);
