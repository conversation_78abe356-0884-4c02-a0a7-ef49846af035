// scripts/insertUserRoles.ts
import { AppDataSource } from '../../src/db/typeorm.config';

async function insertUserRoles() {
  await AppDataSource.initialize();

  await AppDataSource.query(`
    INSERT INTO user_roles (user_id, role, cost_center_id, business_unit_id)
    VALUES (26, 'cc_head', 36, 15),
           (26, 'cc_head', 37, 15);
  `);

  console.log('Inserted user_roles successfully');
  await AppDataSource.destroy();
}

insertUserRoles().catch(console.error);
