// scripts/import-item-categories.ts

import fs from 'fs';
import path from 'path';
import { parse } from 'csv-parse';
import { AppDataSource } from '../db/typeorm.config';

interface ItemCategoryRecord {
  name: string;
  description: string | null;
  type: string;
  is_active: number;
  created_by: number;
}

async function importItemCategories(userId: number, filename: string): Promise<void> {
  try {
    await AppDataSource.initialize();

    const csvFilePath = path.join(__dirname, 'csv', filename);
    if (!fs.existsSync(csvFilePath)) {
      console.error(`Error: File ${filename} not found in the csv folder`);
      process.exit(1);
    }

    const records: ItemCategoryRecord[] = await new Promise((resolve, reject) => {
      const results: ItemCategoryRecord[] = [];

      fs.createReadStream(csvFilePath)
        .pipe(
          parse({
            columns: true,
            skip_empty_lines: true,
            trim: true
          })
        )
        .on('data', (row: Record<string, string>) => {
          const record: ItemCategoryRecord = {
            name: row.name?.trim(),
            description: row.description?.trim() || null,
            type: row.type?.trim(),
            is_active: row.is_active?.toLowerCase() === 'true' ? 1 : 0,
            created_by: userId,
          };

          if (!record.name || !record.type) {
            console.warn('Skipping invalid row:', row);
            return;
          }

          results.push(record);
        })
        .on('end', () => resolve(results))
        .on('error', reject);
    });

    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      for (const category of records) {
        const existing = await queryRunner.query(
          `SELECT id FROM item_categories WHERE name = ? AND type = ?`,
          [category.name, category.type]
        );

        if (existing.length > 0) {
          console.log(`Skipping existing category: ${category.name} (${category.type})`);
          continue;
        }

        await queryRunner.query(
          `INSERT INTO item_categories (name, description, type, is_active, created_by)
           VALUES (?, ?, ?, ?, ?)`,
          [
            category.name,
            category.description,
            category.type,
            category.is_active,
            category.created_by,
          ]
        );

        console.log(`Inserted category: ${category.name} (${category.type})`);
      }

      await queryRunner.commitTransaction();
      console.log('Item categories imported successfully!');
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error('Error inserting item categories:', err);
      throw err;
    } finally {
      await queryRunner.release();
    }
  } catch (err) {
    console.error('Import failed:', err);
  } finally {
    await AppDataSource.destroy();
  }
}

const args = process.argv.slice(2);
if (args.length < 2) {
  console.error('Usage: ts-node import-item-categories.ts <userId> <csvFileName>');
  process.exit(1);
}

const userId = parseInt(args[0], 10);
if (isNaN(userId) || userId <= 0) {
  console.error('Invalid user ID. Must be a positive integer.');
  process.exit(1);
}

const filename = args[1];

importItemCategories(userId, filename);
