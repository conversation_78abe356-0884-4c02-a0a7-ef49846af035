import { AppDataSource } from '../db/typeorm.config';
import { GrnItem } from '../entities/GrnItem.entity';

interface UpdateGrnParams {
  grnId: number;
  poId: number;
  quantity: number;
}

async function updateGrnItem({ grnId, poId, quantity }: UpdateGrnParams): Promise<void> {
  if (!grnId || !poId || quantity == null) {
    throw new Error('Missing required parameters: grnId, poId, and quantity are required');
  }

  if (quantity < 0) {
    throw new Error('Quantity must be a non-negative number');
  }

  try {
    await AppDataSource.initialize();
    const grnRepo = AppDataSource.getRepository(GrnItem);

    const grnItem = await grnRepo.findOne({
      where: {
        grnId,
        poItem: {
          id: poId
        }
      },
      relations: ['poItem']
    });

    if (!grnItem) {
      throw new Error(`GRN Item not found for GRN ID: ${grnId} and PO ID: ${poId}`);
    }

    console.log(`Updating GRN Item (ID: ${grnItem.id}):`);
    console.log(`- Old quantity: ${grnItem.grnQuantity}`);
    console.log(`- New quantity: ${quantity}`);

    grnItem.grnQuantity = quantity;
    await grnRepo.save(grnItem);

    console.log('✅ GRN quantity updated successfully');
    console.log('Updated GRN Item:', JSON.stringify(grnItem, null, 2));
  } catch (error: any) {
    console.error('❌ Error updating GRN item:', error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

// Parse command line arguments
function parseArgs(): UpdateGrnParams {
  const args = process.argv.slice(2);
  const params: Partial<UpdateGrnParams> = {};

  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace('--', '');
    const value = args[i + 1];
    
    if (key === 'grnId') params.grnId = parseInt(value, 10);
    if (key === 'poId') params.poId = parseInt(value, 10);
    if (key === 'quantity') params.quantity = parseFloat(value);
  }

  // If not all required params are provided, show usage
  if (!params.grnId || !params.poId || params.quantity == null) {
    console.log('Usage: ts-node update-grn.ts --grnId <number> --poId <number> --quantity <number>');
    console.log('Example: ts-node update-grn.ts --grnId 16 --poId 12 --quantity 5');
    process.exit(1);
  }

  return params as UpdateGrnParams;
}

// Run the script
(async () => {
  try {
    const params = parseArgs();
    await updateGrnItem(params);
    process.exit(0);
  } catch (error: any) {
    console.error('❌ Script failed:', error instanceof Error ? error.message : 'Unknown error');
    process.exit(1);
  }
})();
