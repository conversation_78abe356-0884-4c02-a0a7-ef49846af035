// src/scripts/import-users.ts
import fs from 'fs';
import path from 'path';
import { parse } from 'csv-parse';
import { AppDataSource } from '../../src/db/typeorm.config';

interface CsvUserRecord {
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  password: string;
  is_active: number;
  roles: string[];
}

async function importUsers(filename: string): Promise<void> {
  try {
    await AppDataSource.initialize();

    const csvFilePath = path.join(__dirname, 'csv', filename);
    if (!fs.existsSync(csvFilePath)) {
      console.error(`Error: File ${filename} not found in the csv folder`);
      process.exit(1);
    }

    const records: CsvUserRecord[] = await new Promise((resolve, reject) => {
      const results: CsvUserRecord[] = [];
      fs.createReadStream(csvFilePath)
        .pipe(
          parse({
            columns: true,
            skip_empty_lines: true,
            trim: true,
          })
        )
        .on('data', (row: any) => {
          const record: CsvUserRecord = {
            username: row.username?.trim(),
            email: row.email?.trim(),
            first_name: row.first_name?.trim(),
            last_name: row.last_name?.trim(),
            password: `${row.username}@123`,
            is_active: row.is_active?.toLowerCase() === 'true' ? 1 : 0,
            roles:
              row.roles?.split(',').map((r: string) => r.trim()).filter(Boolean) || [],
          };

          if (!record.username || !record.email) {
            console.warn('Skipping invalid row:', row);
            return;
          }

          results.push(record);
        })
        .on('end', () => resolve(results))
        .on('error', reject);
    });

    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      for (const user of records) {
        const existing = await queryRunner.query(
          `SELECT id FROM users WHERE username = ? OR email = ?`,
          [user.username, user.email]
        );

        if (existing.length > 0) {
          console.log(`Skipping existing user: ${user.username} (${user.email})`);
          continue;
        }

        const result = await queryRunner.query(
          `INSERT INTO users (username, email, first_name, last_name, is_active, password)
           VALUES (?, ?, ?, ?, ?, ?)`,
          [
            user.username,
            user.email,
            user.first_name,
            user.last_name,
            user.is_active,
            user.password,
          ]
        );

        const userIdCreated = result.insertId ?? result[0]?.id;
        console.log(`Created user: ${user.username} (${userIdCreated})`);

        if (user.roles.length > 0) {
          for (const role of user.roles) {
            const existingRole = await queryRunner.query(
              `SELECT * FROM user_roles WHERE user_id = ? AND role = ?`,
              [userIdCreated, role]
            );

            if (existingRole.length > 0) {
              console.log(`Role "${role}" already assigned to user: ${user.username}`);
              continue;
            }

            await queryRunner.query(
              `INSERT INTO user_roles (user_id, role) VALUES (?, ?)`,
              [userIdCreated, role]
            );
            console.log(`Assigned role "${role}" to user: ${user.username}`);
          }
        } else {
          console.log(`No roles assigned for user: ${user.username}`);
        }
      }

      await queryRunner.commitTransaction();
      console.log('Users and roles imported successfully!');
    } catch (err) {
      await queryRunner.rollbackTransaction();
      console.error('Error inserting users or roles:', err);
      throw err;
    } finally {
      await queryRunner.release();
    }
  } catch (err) {
    console.error('Import failed:', err);
  } finally {
    await AppDataSource.destroy();
  }
}

if (process.argv.length < 3) {
  console.error('Usage: ts-node import-users.ts <csvFileName>');
  console.error('Example: ts-node import-users.ts users.csv');
  process.exit(1);
}

const filename = process.argv[2];
importUsers(filename);
