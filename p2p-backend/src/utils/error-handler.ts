import { FastifyInstance } from 'fastify';
import * as Sentry from '@sentry/node';

/**
 * Sets up global error handlers for the application
 * This helps catch unhandled errors that might otherwise cause the server to crash
 * or return incorrect status codes
 */
export function setupGlobalErrorHandlers(server: FastifyInstance): void {
  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    Sentry.captureException(reason);
    
    server.log.error({
      message: 'Unhandled Promise Rejection',
      reason,
      stack: reason instanceof Error ? reason.stack : undefined,
      timestamp: new Date().toISOString()
    });
    
    // In production, we don't want to crash the server
    // In development, we might want to crash to make errors more visible
    if (process.env.NODE_ENV === 'development') {
      console.error('Unhandled Promise Rejection. The server will continue running, but this should be fixed.');
    }
  });

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    Sentry.captureException(error);
    
    server.log.error({
      message: 'Uncaught Exception',
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
    
    // For uncaught exceptions, it's generally safer to crash and restart
    // as the application state might be corrupted
    if (process.env.NODE_ENV === 'production') {
      console.error('Uncaught exception detected. Shutting down gracefully...');
      
      // Attempt to close the server gracefully before exiting
      server.close(() => {
        console.log('Server closed. Exiting process.');
        process.exit(1);
      });
      
      // If server doesn't close within 10 seconds, force exit
      setTimeout(() => {
        console.error('Forced shutdown after timeout');
        process.exit(1);
      }, 10000);
    } else {
      // In development, crash immediately to make errors more visible
      throw error;
    }
  });

  // Add a custom error handler for Fastify
  server.setErrorHandler((error, request, reply) => {

    const isIgnorable = (
      error.statusCode === 401 ||
      error.validation
    );

    if(!isIgnorable) {
      Sentry.withScope(scope => {
        // Add request details to Sentry event
        scope.setTag('method', request.method);
        scope.setTag('url', request.url);
        scope.setTag('request_id', request.id || '');
        
        // Add user information if available
        const user = request.user as { id?: unknown; email?: string; username?: string };
        if (user?.id) {
          scope.setUser({
            id: user.id?.toString(),
            email: user.email,
            username: user.username,
          });
        }
        
        // Add request data as extra context
        scope.setExtra('params', request.params);
        scope.setExtra('query', request.query);
        scope.setExtra('body', request.body);
        
        Sentry.captureException(error);
      });
    }

    // Log the error with detailed information
    server.log.error({
      message: 'Request Error',
      error: error.message,
      stack: error.stack,
      method: request.method,
      url: request.url,
      params: request.params,
      query: request.query,
      headers: request.headers,
      timestamp: new Date().toISOString()
    });

    // Determine the appropriate status code
    let statusCode = error.statusCode || 500;
    
    // Handle specific error types
    if (error.code === 'ETIMEDOUT' || error.code === 'ESOCKETTIMEDOUT' || error.message.includes('timeout')) {
      statusCode = 503; // Service Unavailable for timeouts
    } else if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      statusCode = 503; // Service Unavailable for connection issues
    }

    // Send an appropriate response
    reply.status(statusCode).send({
      error: true,
      code: error.code || 'INTERNAL_SERVER_ERROR',
      message: process.env.NODE_ENV === 'production' 
        ? 'An error occurred while processing your request'
        : error.message,
      requestId: request.id
    });
  });
}
