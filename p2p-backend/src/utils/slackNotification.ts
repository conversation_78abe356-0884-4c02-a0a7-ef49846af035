import axios from 'axios';

const SLACK_WEBHOOK_URL = process.env.SLACK_WEBHOOK_URL;
const ENV = process.env.ENV;

const CHANNELS = {
  development: 'slack-test',
  production: 'p2p-notifications',
};

export interface SlackNotificationOptions {
  type: 'PR' | 'PO' | 'INVOICE';
  data: Record<string, any>;
}

function getChannel() {
  if (ENV === 'production') return CHANNELS.production;
  return CHANNELS.development;
}

function formatMessage(type: string, data: Record<string, any>) {
  switch (type) {
    case 'PR':
      return `:memo: *New PR Created*\nID: ${data.id}` +
        (data.businessUnit ? `\nBusiness Unit: ${data.businessUnit}` : '') +
        (data.costCenter ? `\nCost Center: ${data.costCenter}` : '') +
        (data.totalValue !== undefined ? `\nTotal Value: ₹${data.totalValue}` : '') +
        (data.createdBy ? `\nBy: ${data.createdBy}` : '');
    case 'PO':
      return `:package: *New PO Created*\nID: ${data.id}\nPO Number: ${data.poNumber}` +
        (data.vendorName ? `\nVendor: ${data.vendorName}` : '') +
        (data.businessUnit ? `\nBusiness Unit: ${data.businessUnit}` : '') +
        (data.costCenter ? `\nCost Center: ${data.costCenter}` : '') +
        (data.totalAmount !== undefined ? `\nTotal Amount: ₹${data.totalAmount}` : '') +
        (data.createdBy ? `\nBy: ${data.createdBy}` : '');
    case 'INVOICE':
      return `:receipt: *New Invoice Created*\nInvoice ID: ${data.id}\nInvoice Number: ${data.invoiceNumber}` +
        (data.grnId ? `\nGRN ID: ${data.grnId}` : '') +
        (data.prId ? `\nPR ID: ${data.prId}` : '') +
        (data.poId ? `\nPO ID: ${data.poId}` : '') +
        (data.invoiceValue !== undefined ? `\nInvoice Value: ₹${data.invoiceValue}` : '') +
        (data.createdBy ? `\nBy: ${data.createdBy}` : '');
    default:
      return ':bell: New Event Occurred';
  }
}

export async function sendSlackNotification({ type, data }: SlackNotificationOptions) {
  const channel = getChannel();
  const text = formatMessage(type, data);

  if (!SLACK_WEBHOOK_URL) {
    console.error('[SlackNotification] No Slack webhook URL configured. Skipping notification.');
    return;
  }

  try {
    console.log(`[SlackNotification] Sending ${type} notification to #${channel}`);
    await axios.post(SLACK_WEBHOOK_URL, { text, channel: `#${channel}` }, {
      timeout: 3000, // 3 second timeout to prevent hanging
      headers: {
        'Content-Type': 'application/json'
      }
    });
    console.log(`[SlackNotification] Successfully sent ${type} notification`);
  } catch (error: any) {
    // Log the error but do not throw - this prevents blocking the main thread
    console.error(`[SlackNotification] Failed to send ${type} notification:`, {
      error: error?.message || 'Unknown error',
      status: error?.response?.status,
      statusText: error?.response?.statusText,
      data: error?.response?.data,
      channel: `#${channel}`,
      type
    });

    // Additional specific logging for common Slack errors
    if (error?.response?.status === 404) {
      console.error(`[SlackNotification] Channel #${channel} not found. Please verify the channel exists and the webhook has access.`);
    } else if (error?.response?.status === 403) {
      console.error(`[SlackNotification] Access denied to channel #${channel}. Please check webhook permissions.`);
    } else if (error?.code === 'ECONNREFUSED' || error?.code === 'ENOTFOUND') {
      console.error(`[SlackNotification] Network error: Unable to reach Slack webhook URL.`);
    } else if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
      console.error(`[SlackNotification] Request timeout: Slack webhook took longer than 3 seconds to respond.`);
    }

    // Do NOT throw the error - let the main request continue
  }
}
