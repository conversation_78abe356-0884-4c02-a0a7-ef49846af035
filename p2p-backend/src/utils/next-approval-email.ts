import { AppDataSource } from '../db/typeorm.config';
import { User } from '../entities/User.entity';
import { 
  ApprovalRole, 
  ApprovalType, 
  ApprovalStatus, 
  RequestType, 
  EmailRequestType
} from '../constants/enums';
import { ApprovalNotificationHandler } from '../services/approval-notification.handler';
import { Approval } from '../entities/Approval.entity';


interface NotifyNextApproverParams {
  requestId: number;
  requestType: string;
  approvalType: ApprovalType;
  currentApprovalSequence: number;
  status: ApprovalStatus;
  remarks: string;
  emailRequestType: EmailRequestType;
  costCenterId?: number;
}

export async function notifyNextApprovers({
  requestId,
  requestType,
  approvalType,
  currentApprovalSequence,
  status,
  remarks,
  emailRequestType,
  costCenterId
}: NotifyNextApproverParams): Promise<void> {

  try {
    const queryRunner = AppDataSource.createQueryRunner();
    const approvalRepo = queryRunner.manager.getRepository(Approval);
    const nextApproval = await approvalRepo.findOne({
      where: {
        requestId,
        requestType,
        approvalType,
        approvalSequence: currentApprovalSequence
      }
    });

    if (!nextApproval) {
      console.log(`[NextApproval] No next approval found for request ${requestId}`);
      return;
    }

    const nextRole = nextApproval.approvalRole as ApprovalRole;
    if (!Object.values(ApprovalRole).includes(nextRole)) {
      console.error(`[NextApproval] Invalid next approval role: ${nextRole} for request ${requestId}`);
      return;
    }

    let nextApprovers: User[];
    const userRepo = AppDataSource.getRepository(User);

    if (nextRole === ApprovalRole.CC_HEAD && costCenterId) {
      nextApprovers = await userRepo
        .createQueryBuilder('user')
        .innerJoin('user.roles', 'role')
        .where('role.role = :role AND role.costCenterId = :costCenterId', { role: nextRole, costCenterId })
        .getMany();
    } else {
      nextApprovers = await userRepo
        .createQueryBuilder('user')
        .innerJoin('user.roles', 'role')
        .where('role.role = :role', { role: nextRole })
        .getMany();
    }

    if (nextApprovers.length === 0) {
      console.warn(`[NextApproval] No users found with role ${nextRole} to notify for request ${requestId}`);
      return;
    }

    await Promise.all(
      nextApprovers.map(approver =>
        ApprovalNotificationHandler.handleNextApproverApproval(
          requestId,
          approver,
          status,
          nextRole,
          remarks,
          emailRequestType
        )
      )
    );

    console.log(`[NextApproval] Sent notifications to ${nextApprovers.length} users with role ${nextRole} for request ${requestId}`);
  } catch (error) {
    console.error(`[NextApproval] Error processing next approver notifications for request ${requestId}:`, error);
  }
}
