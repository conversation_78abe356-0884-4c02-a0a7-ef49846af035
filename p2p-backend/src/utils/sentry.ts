import * as Sentry from '@sentry/node';

export function initSentry() {
  try {
    Sentry.init({
      dsn: process.env.SENTRY_DSN_P2P_BE,
      sendDefaultPii: true,
      environment: process.env.ENV || 'development',
      tracesSampleRate: 0.1,
    });

    console.log('Sentry initialized successfully');
  } catch (error) {
    console.error('Sentry initialization failed:', error);
  }

  return Sentry;
}
