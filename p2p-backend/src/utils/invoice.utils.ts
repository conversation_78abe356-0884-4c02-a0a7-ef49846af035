export interface ItemCategory {
    name: string;
    type: string;
}

export function extractUniqueItemCategories(items: any[]): ItemCategory[] {
    const map = new Map<number, ItemCategory>();
    for (const item of items || []) {
        const cat = item.itemCategory;
        if (cat && !map.has(item.itemCategoryId)) {
            map.set(item.itemCategoryId, { name: cat.name, type: cat.type });
        }
    }
    return Array.from(map.values());
}