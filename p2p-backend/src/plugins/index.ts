import { FastifyInstance } from 'fastify';
import cors from '@fastify/cors';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import authPlugin from './auth.plugin';
import { authenticate } from '../middlewares/auth.middleware';
import fastifyMultipart from '@fastify/multipart';
import { requestTimeout } from '../middlewares/request-timeout.middleware';

export const registerPlugins = async (server: FastifyInstance): Promise<void> => {
  // Register request timeout plugin first to ensure all requests are monitored
  await server.register(requestTimeout, {
    timeout: 60000, // 60 seconds timeout
    statusCode: 503, // Service Unavailable
    excludeRoutes: ['/health', '/metrics'] // Exclude health check routes
  });

  // Register CORS
  await server.register(cors, {
    origin: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    credentials: true
  });
  
  // Register Multipart for file uploads
  await server.register(fastifyMultipart, {
    limits: {
      fileSize: 20 * 1024 * 1024, // 20MB limit
    },
    // These options make file uploads accessible in the request body
    addToBody: true, 
    attachFieldsToBody: true
  });
  
  // Add a hook to handle repeated field names as arrays
  server.addHook('preValidation', async (request, reply) => {
    // Only process multipart requests with 'file' field
    if (request.headers['content-type']?.includes('multipart/form-data') && 
        request.body && typeof request.body === 'object') {
      
      // Look for multiple instances of the same field name
      const partIndices = Object.keys(request.body).filter(key => !isNaN(Number(key)));
      if (partIndices.length > 0) {
        // Process parts to find files
        console.log('Processing multipart request with multiple parts');
      }
    }
  });

  // Register Swagger
  await server.register(swagger, {
    swagger: {
      info: {
        title: 'P2P Backend API',
        description: 'API documentation for the P2P Backend application',
        version: '1.0.0'
      },
      externalDocs: {
        url: 'https://swagger.io',
        description: 'Find more info here'
      },
      host: 'localhost:3000',
      schemes: ['http'],
      consumes: ['application/json'],
      produces: ['application/json']
    }
  });

  // Register Swagger UI
  await server.register(swaggerUi, {
    routePrefix: '/documentation',
    uiConfig: {
      docExpansion: 'list',
      deepLinking: false
    }
  });

  // Register Auth Plugin
  await server.register(authPlugin);
  
  // Register global authentication middleware
  server.addHook('onRequest', authenticate);
};
