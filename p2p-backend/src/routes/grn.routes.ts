import { FastifyInstance } from 'fastify';
import { Type } from '@sinclair/typebox';
import { createGrn, deleteGrn, getGrnsByPoId } from '../controllers/grn.controller';
import { DocumentSchema } from './invoice.routes';

// Define schema for create GRN request body
const CreateGrnItemSchema = Type.Object({
  poItemId: Type.Number(),
  grnQuantity: Type.Number()
});

const CreateGrnSchema = Type.Object({
  poId: Type.Number(),
  grnItems: Type.Array(CreateGrnItemSchema),
  fileKey: Type.Optional(Type.String()),
  fileKeys: Type.Optional(Type.Array(Type.String()))
});

// Define schema for get GRNs by PO ID params
const GetGrnsByPoIdParamsSchema = Type.Object({
  poId: Type.Number()
});

// Define response schema for successful GRN creation
const GrnResponseSchema = Type.Object({
  message: Type.String(),
  grn: Type.Object({
    id: Type.Number(),
    poId: Type.Number(),
    createdAt: Type.String({ format: 'date-time' }),
    itemsCount: Type.Number(),
    attachmentsCount: Type.Number()
  })
});

// Define response schema for error responses
const ErrorResponseSchema = Type.Object({
  error: Type.Boolean(),
  message: Type.String()
});

// Define user schema for responses
const UserSchema = Type.Object({
  id: Type.Number(),
  username: Type.String(),
  email: Type.Optional(Type.String()),
  name: Type.Optional(Type.String())
});

// Define invoice item schema
const InvoiceItemSchema = Type.Object({
  id: Type.Number(),
  invoiceQuantity: Type.Number(),
  pricePerUnit: Type.Number(),
  totalPrice: Type.Number(),
  tax: Type.Number(),
  amount: Type.Number(),
  itemCategoryDetails: Type.Optional(Type.Object({
    id: Type.Number(),
    name: Type.String(),
    description: Type.Optional(Type.String())
  })),
  itemName: Type.Optional(Type.String()),
  itemDescription: Type.Optional(Type.String())
});

// Define invoice schema
const InvoiceSchema = Type.Object({
  id: Type.Number(),
  invoiceNumber: Type.String(),
  invoiceDate: Type.String({ format: 'date-time' }),
  invoiceValue: Type.Number(),
  createdBy: UserSchema,
  createdAt: Type.String({ format: 'date-time' }),
  items: Type.Optional(Type.Array(InvoiceItemSchema)),
  businessUnit: Type.String(),
  costCenter: Type.String(),
  vendorName: Type.String(),
  attachments: Type.Optional(Type.Array(DocumentSchema)),
});

// Define response schema for GRN list
const GrnsListResponseSchema = Type.Object({
  message: Type.String(),
  grns: Type.Array(
    Type.Object({
      id: Type.Number(),
      poId: Type.Number(),
      createdBy: Type.Union([
        Type.Number(),
        UserSchema
      ]),
      createdAt: Type.String({ format: 'date-time' }),
      updatedAt: Type.String({ format: 'date-time' }),
      purchaseOrder: Type.Object({
        id: Type.Number(),
        poNumber: Type.String(),
        status: Type.String(),
        vendorId: Type.Number()
      }),
      items: Type.Array(
        Type.Object({
          id: Type.Number(),
          grnQuantity: Type.Number(),
          poItem: Type.Object({
            id: Type.Number(),
            itemName: Type.String(),
            itemDescription: Type.Optional(Type.String()),
            quantity: Type.Union([Type.Number(), Type.String()]),
            uom: Type.String(),
            unitPrice: Type.Union([Type.Number(), Type.String()]),
            totalPrice: Type.Union([Type.Number(), Type.String()]),
            gstPercentage: Type.Optional(Type.Number()),
            gstAmount: Type.Optional(Type.Number()),
            itemCategory: Type.Optional(Type.Object({
              id: Type.Number(),
              name: Type.String(),
              description: Type.Optional(Type.String()),
              type: Type.Optional(Type.String())
            }))
          })
        })
      ),
      attachments: Type.Array(
        Type.Object({
          id: Type.Number(),
          fileKey: Type.String(),
          fileName: Type.String(),
          fileUrl: Type.Optional(Type.String()),
          fileSize: Type.Number(),
          contentType: Type.String(),
          createdAt: Type.String({ format: 'date-time' })
        })
      ),
      invoice: Type.Optional(InvoiceSchema)
    })
  )
});

export const grnRoutes = (server: FastifyInstance): void => {
  // Create a new GRN with items
  server.post('/grns', {
    schema: {
      body: CreateGrnSchema,
      response: {
        201: GrnResponseSchema,
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        403: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    handler: createGrn as any
  });

  // Get all GRNs for a specific purchase order
  server.get<{
    Params: { poId: number };
  }>('/purchase-orders/:poId/grns', {
    schema: {
      params: GetGrnsByPoIdParamsSchema,
      response: {
        200: GrnsListResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    handler: getGrnsByPoId as any
  });

  // Delete a GRN
  server.delete<{ Params: { id: number } }>(
    '/grns/:id',
    {
      schema: {
        params: Type.Object({
          id: Type.Number(),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
          400: ErrorResponseSchema,
          401: ErrorResponseSchema,
          403: ErrorResponseSchema,
          404: ErrorResponseSchema,
          500: ErrorResponseSchema
        }
      },
      handler: deleteGrn as any,
    }
  );
};
