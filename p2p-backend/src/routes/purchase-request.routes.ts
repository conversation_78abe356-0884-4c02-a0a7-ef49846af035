import { FastifyInstance } from 'fastify';
import { as<PERSON>outeHandler } from '../types/route-helper';
import { 
  createPurchaseRequest, 
  getAllPurchaseRequests, 
  getPurchaseRequestById, 
  updatePurchaseRequest,
  rejectPurchaseRequest,
  getPurchaseRequestStatusCounts,
  GetPurchaseRequestsQuery
} from '../controllers/purchase-request.controller';
import { 
  processCCHeadApproval, 
  processBizFinApproval, 
  processProcurementManagerApproval, 
  getPurchaseRequestApprovals 
} from '../controllers/approval.controller';
import { resubmitPurchaseRequest } from '../controllers/resubmit.controller';
import { Type } from '@sinclair/typebox';
import { hasRole } from '../middlewares/auth.middleware';
import { ApprovalRole } from '../constants/enums';
import {
  CreatePurchaseRequestBodySchema,
  GetAllPurchaseRequestsResponseSchema,
  PurchaseRequestWithItemsSchema,
  PurchaseRequestQuerySchema,
  IdParamSchema,
  RequestIdParamSchema,
  ApprovalActionBodySchema,
  ApprovalActionResponseSchema,
  ResubmitBodySchema,
  ResubmitResponseSchema,
  ApprovalWithTimestampsSchema,
  ErrorResponseSchema
} from '../schemas/purchase-request.schema';

export const purchaseRequestRoutes = (server: FastifyInstance): void => {
  // Create a new purchase request
  // Authentication is handled by global middleware
  server.post('/purchase-requests', {
    schema: {
      body: CreatePurchaseRequestBodySchema
    },
    handler: asRouteHandler(createPurchaseRequest)
  });

  // Get all purchase requests with pagination and role-based filtering
  // Accessible to all authenticated users
  server.get('/purchase-requests', {
    schema: {
      querystring: PurchaseRequestQuerySchema,
      response: {
        200: GetAllPurchaseRequestsResponseSchema
      }
    },
    handler: asRouteHandler(getAllPurchaseRequests)
  });

  // Get counts of all purchase request statuses
  // Accessible to all authenticated users
  server.get('/purchase-requests/status-counts', {
    schema: {
      response: {
        200: Type.Object({
          total: Type.Number(),
          counts: Type.Record(Type.String(), Type.Number())
        }),
        401: Type.Object({
          error: Type.Boolean(),
          message: Type.String()
        }),
        500: Type.Object({
          error: Type.Boolean(),
          message: Type.String()
        })
      }
    },
    handler: asRouteHandler(getPurchaseRequestStatusCounts)
  });

  // Get purchase request by ID
  // Accessible to authenticated users with appropriate roles
  server.get('/purchase-requests/:id', {
    schema: {
      params: IdParamSchema,
      response: {
        200: PurchaseRequestWithItemsSchema,
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(getPurchaseRequestById)
  });

  // Get all approvals for a purchase request
  server.get('/purchase-requests/:requestId/approvals', {
    schema: {
      params: RequestIdParamSchema,
      response: {
        200: Type.Array(ApprovalWithTimestampsSchema),
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(getPurchaseRequestApprovals)
  });

  // CC_HEAD approval route
  server.post('/purchase-requests/:requestId/cc-head-approval', {
    preHandler: hasRole([ApprovalRole.CC_HEAD]),
    schema: {
      params: RequestIdParamSchema,
      body: ApprovalActionBodySchema,
      response: {
        200: ApprovalActionResponseSchema,
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(processCCHeadApproval)
  });

  // BIZ_FIN approval route
  server.post('/purchase-requests/:requestId/biz-fin-approval', {
    preHandler: hasRole([ApprovalRole.BIZ_FIN]),
    schema: {
      params: RequestIdParamSchema,
      body: ApprovalActionBodySchema,
      response: {
        200: ApprovalActionResponseSchema,
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(processBizFinApproval)
  });

  // PROCUREMENT_MANAGER approval route
  server.post('/purchase-requests/:requestId/procurement-manager-approval', {
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER]),
    schema: {
      params: RequestIdParamSchema,
      body: ApprovalActionBodySchema,
      response: {
        200: ApprovalActionResponseSchema,
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(processProcurementManagerApproval)
  });

  // Resubmit a rejected purchase request
  server.post('/purchase-requests/:requestId/resubmit', {
    schema: {
      params: RequestIdParamSchema,
      body: ResubmitBodySchema,
      response: {
        200: ResubmitResponseSchema,
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(resubmitPurchaseRequest)
  });

  // Update a purchase request (status)
  server.put('/purchase-requests/:id', {
    schema: {
      params: IdParamSchema,
      body: Type.Object({
        status: Type.Optional(Type.String())
      }),
      response: {
        200: Type.Object({
          message: Type.String(),
          purchaseRequest: PurchaseRequestWithItemsSchema
        }),
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(updatePurchaseRequest)
  });

  // Reject a purchase request
  server.put('/purchase-requests/:id/reject', {
    schema: {
      params: IdParamSchema,
      body: Type.Object({
        status: Type.Optional(Type.String()),
        remarks: Type.Optional(Type.String())
      }),
      response: {
        200: Type.Object({
          message: Type.String(),
          purchaseRequest: PurchaseRequestWithItemsSchema
        }),
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(rejectPurchaseRequest)
  });
};
