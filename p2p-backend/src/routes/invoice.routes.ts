import { FastifyInstance } from 'fastify';
import { Type } from '@sinclair/typebox';
import {
  createInvoice,
  createPaymentRequest,
  checkPaymentRequestStatus,
  approveInvoice,
  rejectInvoice,
  getInvoiceApprovalHistory,
  getAllInvoicesController,
  getInvoiceByIdController,
  updateInvoiceStatus,
  deleteInvoice,
} from '../controllers/invoice.controller';
import {
  processInvoiceBizFinApproval,
  processInvoiceFinanceApproval,
  getInvoiceApprovals
} from '../controllers/invoice-approval.controller';
import { hasRole } from '../middlewares/auth.middleware';
import { ApprovalRole } from '../constants/enums';
import { PaymentRequestStatus } from '../entities/Invoice.entity';

// Invoice item schema for creation
const CreateInvoiceItemSchema = Type.Object({
  grnItemId: Type.Number(),
  amount: Type.Number(),
  tax: Type.Optional(Type.Number())
});

// Create invoice schema
const CreateInvoiceSchema = Type.Object({
  grnId: Type.Number(),
  invoiceNumber: Type.String(),
  invoiceDate: Type.String({ format: 'date' }),
  invoiceValue: Type.Number(),
  remarks: Type.Optional(Type.String()),
  invoiceItems: Type.Array(CreateInvoiceItemSchema),
  fileKey: Type.Optional(Type.String()),
  fileKeys: Type.Optional(Type.Array(Type.String()))
});
const poItemSchema = Type.Object({
  id: Type.Number(),
  itemName: Type.String(),
  itemDescription: Type.Optional(Type.String()),
  quantity: Type.Number(),
  uom: Type.Optional(Type.String()),
  pricePerUnit: Type.Number(),
  gstPercentage: Type.Number(),
  gstAmount: Type.Number(),
  totalValue: Type.Number(),
  itemCategory: Type.Optional(Type.String())
});

export const DocumentSchema = Type.Object({
  id: Type.Number(),
  fileName: Type.String(),
  fileKey: Type.String(),
  fileUrl: Type.Optional(Type.String()),
  fileSize: Type.Optional(Type.Number()),
  fileType: Type.Optional(Type.String()),
  contentType: Type.Optional(Type.String()),
  entityType: Type.String(),
  entityId: Type.Number(),
  createdAt: Type.String(),
  updatedAt: Type.String()
});

// Invoice response schema
const InvoiceResponseSchema = Type.Object({
  message: Type.String(),
  invoice: Type.Object({
    id: Type.Number(),
    grnId: Type.Number(),
    invoiceNumber: Type.String(),
    vendorName: Type.String(),
    vendorId: Type.Number(),
    invoiceDate: Type.String({ format: 'date-time' }),
    invoiceValue: Type.Number(),
    itemsCount: Type.Number(),
    attachmentsCount: Type.Number(),
    paymentRequestStatus: Type.Optional(Type.Enum(PaymentRequestStatus)),
    prId: Type.Optional(Type.Number()),
    showInvoiceVerificationDialog: Type.Optional(Type.Boolean()),
    showInvoiceVerificationToRole: Type.Optional(Type.String()),
    status: Type.Optional(Type.String()),
    attachments: Type.Optional(Type.Array(DocumentSchema)),
    poDetails: Type.Optional(Type.Object({
      id: Type.Number(),
      poNumber: Type.String(),
      status: Type.String(),
      type: Type.String(),
      totalAmount: Type.Number(),
      currency: Type.String(),
      businessUnit: Type.String(),
      costCenter: Type.String(),
      paymentTerms: Type.String(),
      deliveryTerms: Type.String(),
      expectedDeliveryDate: Type.String({ format: 'date-time' }),
      billTo: Type.String(),
      shipToAddress: Type.String(),
      remarks: Type.String(),
      prId: Type.String(),
      items: Type.Array(poItemSchema)
    }))
  })
});

// Payment request response schema
const PaymentRequestResponseSchema = Type.Object({
  message: Type.String(),
  invoice: Type.Object({
    id: Type.Number(),
    invoiceNumber: Type.String(),
    invoiceValue: Type.Number(),
    paymentRequestStatus: Type.Enum(PaymentRequestStatus),
    paymentRequestId: Type.Optional(Type.String()),
    paymentRequestExternalId: Type.Optional(Type.String()),
    paymentRequestCreatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    paymentRequestUpdatedAt: Type.Optional(Type.String({ format: 'date-time' })),
    paymentRequestRemarks: Type.Optional(Type.String())
  })
});

// ID parameter schema
const IdParamSchema = Type.Object({
  id: Type.Number()
});

// Error response schema
const ErrorResponseSchema = Type.Object({
  error: Type.Boolean(),
  message: Type.String()
});

/**
 * Invoice routes implementation
 */
export const invoiceRoutes = (server: FastifyInstance): void => {
  // Health check route
  server.get('/invoices/health', async (request, reply) => {
    return reply.status(200).send({ message: 'Invoice routes ready' });
  });

  // Define ItemCategory schema
  const ItemCategorySchema = Type.Object({
    name: Type.String(),
    type: Type.String()
  });

  // Define Invoice response schema
  const InvoiceResponseListSchema = Type.Object({
    id: Type.Number(),
    invoiceNumber: Type.String(),
    invoiceDate: Type.String(),
    invoiceValue: Type.Number(),
    status: Type.String(),
    vendorName: Type.String(),
    vendorId: Type.String(),
    grn_id: Type.Optional(Type.Number()),
    po_id: Type.Optional(Type.Number()),
    pr_id: Type.Optional(Type.Number()),
    item_categories: Type.Array(ItemCategorySchema)
  });

  // Get all invoices (listing)
  server.get('/invoices', {
    schema: {
      response: {
        200: Type.Object({
          invoices: Type.Array(InvoiceResponseListSchema),
          pagination: Type.Object({
            totalCount: Type.Number(),
            page: Type.Number(),
            limit: Type.Number(),
            totalPages: Type.Number()
          })
        }),
        500: ErrorResponseSchema
      }
    },
    handler: getAllInvoicesController as any
  });

  // Get a single invoice by ID
  server.get('/invoices/:id', {
    schema: {
      params: IdParamSchema,
      response: {
        200: InvoiceResponseSchema,
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    preHandler: hasRole([ApprovalRole.CC_HEAD, ApprovalRole.BIZ_FIN, ApprovalRole.PROCUREMENT_MANAGER, ApprovalRole.FINANCE]),
    handler: getInvoiceByIdController as any
  });

  // Create a new invoice
  server.post('/invoices', {
    schema: {
      body: CreateInvoiceSchema,
      response: {
        201: InvoiceResponseSchema,
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        403: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER, ApprovalRole.BIZ_FIN]),
    handler: createInvoice as any
  });

  // Create a payment request for an invoice
  server.post('/invoices/:id/payment-request', {
    schema: {
      params: IdParamSchema,
      response: {
        200: PaymentRequestResponseSchema,
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        403: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER, ApprovalRole.BIZ_FIN]),
    handler: createPaymentRequest as any
  });

  // Update invoice status
  server.put('/invoices/:id/status', {
    schema: {
      params: IdParamSchema,
      body: Type.Object({
        status: Type.String(),
        remarks: Type.Optional(Type.String())
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
          data: Type.Object({
            id: Type.Number(),
            status: Type.String(),
            remarks: Type.Optional(Type.String()),
            updatedAt: Type.String()
          })
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        403: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER, ApprovalRole.BIZ_FIN, ApprovalRole.FINANCE]),
    handler: updateInvoiceStatus as any
  });

  // Check payment request status for an invoice
  server.get('/invoices/:id/payment-request', {
    schema: {
      params: IdParamSchema,
      response: {
        200: PaymentRequestResponseSchema,
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        403: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER, ApprovalRole.BIZ_FIN]),
    handler: checkPaymentRequestStatus as any
  });

  // Business Finance approval for invoice
  server.post('/invoices/:id/biz-fin-approval', {
    preHandler: hasRole([ApprovalRole.BIZ_FIN]),
    schema: {
      params: IdParamSchema,
      body: Type.Object({
        status: Type.String(),
        remarks: Type.Optional(Type.String())
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
          role: Type.String()
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    handler: processInvoiceBizFinApproval as any
  });

  // Finance approval for invoice
  server.post('/invoices/:id/fin-approval', {
    preHandler: hasRole([ApprovalRole.FINANCE]),
    schema: {
      params: IdParamSchema,
      body: Type.Object({
        status: Type.String(),
        remarks: Type.Optional(Type.String())
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
          role: Type.String()
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    handler: processInvoiceFinanceApproval as any
  });

  // Approve an invoice
  server.post('/invoices/:id/approve', {
    schema: {
      params: IdParamSchema,
      body: Type.Object({ remarks: Type.Optional(Type.String()) }),
      response: {
        200: Type.Object({ message: Type.String() }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema
      }
    },
    preHandler: hasRole([ApprovalRole.CC_HEAD, ApprovalRole.BIZ_FIN, ApprovalRole.PROCUREMENT_MANAGER]),
    handler: approveInvoice as any
  });

  // Reject an invoice
  server.post('/invoices/:id/reject', {
    schema: {
      params: IdParamSchema,
      body: Type.Object({ remarks: Type.Optional(Type.String()) }),
      response: {
        200: Type.Object({ message: Type.String() }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema
      }
    },
    preHandler: hasRole([ApprovalRole.CC_HEAD, ApprovalRole.BIZ_FIN, ApprovalRole.PROCUREMENT_MANAGER, ApprovalRole.FINANCE]),
    handler: rejectInvoice as any
  });

  // Get approvals for an invoice from approval controller
  server.get('/invoices/:id/approvals', {
    schema: {
      params: IdParamSchema,
      response: {
        200: Type.Array(Type.Object({})),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema
      }
    },
    handler: getInvoiceApprovals as any
  });

  // Get approval history for an invoice (legacy endpoint)
  server.get('/invoices/:id/approval-history', {
    schema: {
      params: IdParamSchema,
      response: {
        200: Type.Object({ approvals: Type.Array(Type.Any()) }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema
      }
    },
    preHandler: hasRole([ApprovalRole.CC_HEAD, ApprovalRole.BIZ_FIN, ApprovalRole.PROCUREMENT_MANAGER]),
    handler: getInvoiceApprovalHistory as any
  });

  // Delete an invoice
  server.delete<{ Params: { id: number } }>(
    '/invoices/:id',
    {
      schema: {
        params: Type.Object({
          id: Type.Number(),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
          400: ErrorResponseSchema,
          401: ErrorResponseSchema,
          403: ErrorResponseSchema,
          404: ErrorResponseSchema,
          500: ErrorResponseSchema
        }
      },
      handler: deleteInvoice as any,
    }
  );
};
