import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { asRouteHandler } from '../types/route-helper';
import { Type } from '@sinclair/typebox';
import { 
  getAllPurchaseOrders, 
  getPurchaseOrderById, 
  updatePurchaseOrderStatus,
  getPurchaseOrderStatusCounts,
  GetPurchaseOrdersQuery
} from '../controllers/purchase-order.controller';
// Keep the old update controller for backward compatibility
import { updateRejectedPO } from '../controllers/update-rejected-po.controller';
// Import the new, cleaner rejected PO update controller
import { updateRejectedPurchaseOrder } from '../controllers/rejected-po-update.controller';
import { User } from '../entities/User.entity';
import { UserRole } from '../entities/UserRole.entity';
import { createPurchaseOrder } from '../controllers/create-purchase-order.controller';
import { PurchaseOrderStatus, PurchaseOrderType, RecurrenceFrequency } from '../entities/PurchaseOrder.entity';
import { hasRole } from '../middlewares/auth.middleware';
import { ApprovalRole } from '../constants/enums';
import { 
  CreatePurchaseOrderSchema, 
  PurchaseOrderResponseSchema, 
  DetailedPurchaseOrderResponseSchema,
  ErrorResponseSchema, 
  UpdateStatusBodySchema, 
  IdParamSchema,
  UpdateRejectedPOSchema
} from '../schemas/purchase-order.schema';
import { RejectedPoUpdateSchema, PoIdParamSchema } from '../schemas/rejected-po-update.schema';
import { BusinessUnitSchema, CostCenterSchema } from '../schemas/purchase-request.schema';

// Define schema for query params 
const PurchaseOrderQuerySchema = Type.Object({
  page: Type.Optional(Type.Number({ minimum: 1 })),
  limit: Type.Optional(Type.Number({ minimum: 1, maximum: 100 })),
  status: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  businessUnitId: Type.Optional(Type.Number()),
  costCenterId: Type.Optional(Type.Number()),
  vendorId: Type.Optional(Type.Number()),
  startDate: Type.Optional(Type.String({ format: 'date' })),
  endDate: Type.Optional(Type.String({ format: 'date' }))
});

// Define response schema for GET all purchase orders
const GetAllPurchaseOrdersResponseSchema = Type.Object({
  data: Type.Array(Type.Object({
    id: Type.Number(),
    poNumber: Type.String(),
    prId: Type.Number(),
    type: Type.String(),
    status: Type.String(),
    vendorId: Type.Number(),
    businessUnitId: Type.Number(),
    costCenterId: Type.Number(),
    totalAmount: Type.Number(),
    currency: Type.String(),
    createdAt: Type.String({ format: 'date-time' }),
    recurrenceFrequency: Type.Optional(Type.String()),
    startDate: Type.Optional(Type.String({ format: 'date' })),
    endDate: Type.Optional(Type.String({ format: 'date' })),
    expectedDeliveryDate: Type.Optional(Type.String({ format: 'date' })),
    billTo: Type.Optional(Type.String()),
    shipToAddress: Type.Optional(Type.String()),
    remarks: Type.Optional(Type.String()),
    businessUnit: Type.Optional(BusinessUnitSchema),
    costCenter: Type.Optional(CostCenterSchema),
    
  })),
  pagination: Type.Object({
    totalCount: Type.Number(),
    page: Type.Number(),
    limit: Type.Number(),
    totalPages: Type.Number()
  })
});

// Types for FastifyRequest extensions are defined in src/types/fastify.d.ts

export const purchaseOrderRoutes = (server: FastifyInstance): void => {
  // Get all purchase orders with pagination and role-based filtering
  // Accessible to all authenticated users with appropriate roles
  server.get<{
    Querystring: GetPurchaseOrdersQuery
  }>('/purchase-orders', {
    schema: {
      querystring: PurchaseOrderQuerySchema,
      response: {
        200: GetAllPurchaseOrdersResponseSchema,
        401: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    handler: asRouteHandler(getAllPurchaseOrders)
  });

  // Get counts of all purchase order statuses
  server.get('/purchase-orders/status-counts', {
    schema: {
      response: {
        200: Type.Object({
          total: Type.Number(),
          counts: Type.Record(Type.String(), Type.Number())
        }),
        401: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    handler: asRouteHandler(getPurchaseOrderStatusCounts)
  });

  // Get purchase order by ID
  server.get<{
    Params: { id: number }
  }>('/purchase-orders/:id', {
    schema: {
      params: IdParamSchema,
      response: {
        200: DetailedPurchaseOrderResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    handler: asRouteHandler(getPurchaseOrderById)
  });

  // Create a new purchase order with items
  // Only accessible to procurement managers
  server.post<{
    Body: {
      prId: number,
      quotationId: number,
      type?: PurchaseOrderType,
      vendorId: number,
      businessUnitId: number,
      costCenterId: number,
      paymentTerms?: string,
      deliveryTerms?: string,
      totalAmount: number,
      currency?: string,
      recurrenceFrequency?: RecurrenceFrequency,
      startDate?: string,
      endDate?: string,
      items: any[]
    }
  }>('/purchase-orders', {
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER]),
    schema: {
      body: CreatePurchaseOrderSchema,
      response: {
        201: PurchaseOrderResponseSchema,
        400: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    handler: asRouteHandler(createPurchaseOrder)
  });

  // Update purchase order status
  // Only accessible to BizFin and procurement managers
  server.put<{
    Params: { id: number },
    Body: { status: PurchaseOrderStatus, remarks?: string }
  }>('/purchase-orders/:id/status', {
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER]),
    schema: {
      params: IdParamSchema,
      body: UpdateStatusBodySchema,
      response: {
        200: PurchaseOrderResponseSchema,
        400: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    handler: asRouteHandler(updatePurchaseOrderStatus)
  });

  // Update a rejected purchase order with a new quote
  // Only accessible to procurement managers
  server.put<{
    Params: { id: number },
    Body: {
      quotationId: number;
      type?: PurchaseOrderType;
      vendorId: number;
      businessUnitId?: number;
      costCenterId?: number;
      paymentTerms?: string;
      deliveryTerms?: string;
      remarks?: string;
      totalAmount?: number;
      currency?: string;
      items: any[];
    }
  }>('/purchase-orders/:id/update-with-new-quote', {
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER]),
    schema: {
      params: IdParamSchema,
      body: UpdateRejectedPOSchema,
      response: {
        200: PurchaseOrderResponseSchema,
        400: ErrorResponseSchema,
        403: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    handler: asRouteHandler(updateRejectedPO)
  });

  // New route for updating rejected POs with improved implementation
  server.put<{
    Params: { id: number },
    Body: {
      quotationId: number;
      type?: PurchaseOrderType;
      vendorId: number;
      businessUnitId: number;
      costCenterId: number;
      paymentTerms?: string;
      deliveryTerms?: string;
      remarks?: string;
      totalAmount?: number;
      currency?: string;
      items: any[];
    }
  }>('/purchase-orders/:id/update-rejected', {
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER]),
    schema: {
      params: PoIdParamSchema,
      body: RejectedPoUpdateSchema,
      response: {
        200: PurchaseOrderResponseSchema,
        400: ErrorResponseSchema,
        403: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    handler: asRouteHandler(updateRejectedPurchaseOrder)
  });

};
