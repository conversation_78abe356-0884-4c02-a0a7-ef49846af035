import { FastifyInstance } from 'fastify';
import { asRouteHandler } from '../types/route-helper';
import { getPurchaseRequestTimeline } from '../controllers/pr-timeline.controller';
import { IdParamSchema, TimelineResponseSchema, ErrorResponseSchema } from '../schemas/pr-timeline.schema';
export const prTimelineRoutes = (server: FastifyInstance): void => {
  // Authentication is already handled globally, no need to add it again

  // Get purchase request timeline
  server.get('/purchase-requests/:id/timeline', {
    schema: {
      params: IdParamSchema,
      response: {
        200: TimelineResponseSchema,
        ...ErrorResponseSchema
      }
    },
    handler: as<PERSON>out<PERSON><PERSON><PERSON><PERSON>(getPurchaseRequestTimeline)
  });
};
