import { FastifyInstance } from 'fastify';
import { verifyQuote } from '../controllers/quote-verification.controller';
import { 
  VerificationParamsSchema, 
  QuoteVerificationSchema, 
  VerificationResponseSchema, 
  ErrorResponseSchema 
} from '../schemas/quote-verification.schema';
import { hasRole } from '../middlewares/auth.middleware';
import { ApprovalRole } from '../constants/enums';

export const quoteVerificationRoutes = (server: FastifyInstance): void => {
  // Route for verifying (approving/rejecting) a quote by PO ID
  // This can be used by both BizFin and CC_HEAD roles
  server.put<{
    Params: { id: number }, // PO ID
    Body: {
      action: 'APPROVE' | 'REJECT',
      remarks?: string
    }
  }>('/purchase-orders/:id/verify-quote', {
    preHandler: hasRole([ApprovalRole.BIZ_FIN, ApprovalRole.CC_HEAD]),
    schema: {
      params: VerificationParamsSchema,
      body: QuoteVerificationSchema,
      response: {
        200: VerificationResponseSchema,
        400: ErrorResponseSchema,
        403: ErrorResponseSchema,
        404: ErrorResponseSchema,
        500: ErrorResponseSchema
      }
    },
    handler: verifyQuote
  });
};
