import { FastifyInstance } from 'fastify';
import { asRouteHandler } from '../types/route-helper';
import {
  getAllQuotations,
  getQuotationsByPrId,
  getQuotationById,
  createQuotation,
  updateQuotation,
  deleteQuotation,
  approveQuotation
} from '../controllers/quotation.controller';
import { hasRole } from '../middlewares/auth.middleware';
import { ApprovalRole } from '../constants/enums';
import {
  IdParamSchema,
  PrIdParamSchema,
  CreateQuotationBodySchema,
  UpdateQuotationBodySchema,
  QuotationSchema,
  GetAllQuotationsResponseSchema,
  QuotationResponseSchema
} from '../schemas/quotation.schema';
import { ApprovalActionBodySchema } from '../schemas/purchase-request.schema';
import { ErrorResponseSchema } from '../schemas/purchase-request.schema';
import { Type } from '@sinclair/typebox';

export const quotationRoutes = (server: FastifyInstance): void => {
  // Authentication is already handled globally, no need to add it again

  // Get all quotations
  server.get('/quotations', {
    schema: {
      response: {
        200: GetAllQuotationsResponseSchema,
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(getAllQuotations)
  });

  // Get quotations by purchase request ID
  server.get('/purchase-requests/:prId/quotations', {
    schema: {
      params: PrIdParamSchema,
      response: {
        200: GetAllQuotationsResponseSchema,
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(getQuotationsByPrId)
  });

  // Get quotation by ID
  server.get('/quotations/:id', {
    schema: {
      params: IdParamSchema,
      response: {
        200: QuotationSchema,
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(getQuotationById)
  });

  // Create a new quotation
  server.route({
    method: 'POST',
    url: '/quotations',
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER]),
    schema: {
      body: CreateQuotationBodySchema,
      response: {
        201: QuotationResponseSchema,
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(createQuotation)
  });

  // Update an existing quotation
  server.route({
    method: 'PUT',
    url: '/quotations/:id',
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER]),
    schema: {
      params: IdParamSchema,
      body: UpdateQuotationBodySchema,
      response: {
        200: QuotationResponseSchema,
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(updateQuotation)
  });

  // Delete a quotation
  server.route({
    method: 'DELETE',
    url: '/quotations/:id',
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER]),
    schema: {
      params: IdParamSchema,
      response: {
        200: Type.Object({
          message: Type.String()
        }),
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(deleteQuotation)
  });

  // Approve a quotation
  server.route({
    method: 'POST',
    url: '/quotations/:id/approve',
    preHandler: hasRole([ApprovalRole.PROCUREMENT_MANAGER]),
    schema: {
      params: IdParamSchema,
      body: ApprovalActionBodySchema,
      response: {
        200: Type.Object({
          message: Type.String(),
          approval: Type.Object({})
        }),
        ...ErrorResponseSchema
      }
    },
    handler: asRouteHandler(approveQuotation)
  });
};
