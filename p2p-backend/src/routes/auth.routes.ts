import { FastifyInstance } from 'fastify';
import { Type } from '@sinclair/typebox';
import { getCurrentUser, logout, googleLogin, login } from '../controllers/auth.controller';

export const authRoutes = (server: FastifyInstance): void => {
  // Username/password login route
  server.post('/auth/login', {
    schema: {
      body: Type.Object({
        username: Type.String(),
        password: Type.String()
      }),
      response: {
        200: Type.Object({
          id: Type.Number(),
          username: Type.String(),
          email: Type.String(),
          firstName: Type.Union([Type.String(), Type.Null()]),
          lastName: Type.Union([Type.String(), Type.Null()]),
          roles: Type.Array(
            Type.Object({
              role: Type.String(),
              costCenterId: Type.Union([Type.Number(), Type.Null()]),
              businessUnitId: Type.Union([Type.Number(), Type.Null()])
            })
          ),
          token: Type.String()
        }),
        401: Type.Object({
          message: Type.String(),
          error: Type.String()
        })
      }
    },
    handler: login
  });

  // Google login route
  server.post('/auth/google-login', {
    schema: {
      body: Type.Object({
        token: Type.String(),
        email: Type.String(),
        name: Type.String(),
        firstName: Type.Optional(Type.String()),
        lastName: Type.Optional(Type.String()),
        picture: Type.Optional(Type.String())
      }),
      response: {
        200: Type.Object({
          id: Type.Number(),
          username: Type.String(),
          email: Type.String(),
          firstName: Type.Union([Type.String(), Type.Null()]),
          lastName: Type.Union([Type.String(), Type.Null()]),
          roles: Type.Array(
            Type.Object({
              role: Type.String(),
              costCenterId: Type.Union([Type.Number(), Type.Null()]),
              businessUnitId: Type.Union([Type.Number(), Type.Null()])
            })
          ),
          token: Type.String()
        }),
        401: Type.Object({
          message: Type.String(),
          error: Type.String()
        })
      }
    },
    handler: googleLogin
  });

  // Get current user route (protected)
  server.get('/auth/me', {
    onRequest: server.authenticate ? [server.authenticate] : undefined,
    schema: {
      response: {
        200: Type.Object({
          id: Type.Number(),
          username: Type.String(),
          email: Type.String(),
          firstName: Type.Union([Type.String(), Type.Null()]),
          lastName: Type.Union([Type.String(), Type.Null()]),
          roles: Type.Array(
            Type.Object({
              role: Type.String(),
              costCenterId: Type.Union([Type.Number(), Type.Null()]),
              businessUnitId: Type.Union([Type.Number(), Type.Null()])
            })
          )
        })
      }
    },
    handler: getCurrentUser
  });

  // Logout route
  server.post('/auth/logout', {
    schema: {
      response: {
        200: Type.Object({
          message: Type.String()
        })
      }
    },
    handler: logout
  });
};
