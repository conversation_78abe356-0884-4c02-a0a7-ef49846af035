import { FastifyInstance } from 'fastify';
import documentController from '../controllers/document.controller';
import { EntityType, DocumentResponseSchema, DocumentsResponseSchema, FileUploadResponseSchema, CreateDocumentSchema } from '../schemas/document.schema';
import { Type } from '@sinclair/typebox';

// Define a response schema for multiple file uploads
const MultipleFilesUploadResponseSchema = Type.Object({
  files: Type.Array(Type.Object({
    fileKey: Type.String(),
    fileUrl: Type.String(),
    fileName: Type.String(),
    fileSize: Type.Number(),
    contentType: Type.String()
  })),
  message: Type.String()
});

// Document route definitions
async function documentRoutes(fastify: FastifyInstance) {
  // Configure multipart specifically for document routes
  fastify.register(async (instance) => {
    // Override multipart config for document upload routes with enhanced configuration
    instance.register(require('@fastify/multipart'), {
      limits: {
        fileSize: 50 * 1024 * 1024, // Increased to 50MB limit
        fieldSize: 10 * 1024 * 1024, // 10MB field size limit
        files: 10, // Allow up to 10 files
        fields: 20 // Allow up to 20 fields
      },
      attachFieldsToBody: true, // This helps with access to files
      onFile: async (part: any) => {
        // This will process each file as it arrives
        await part.toBuffer(); // Pre-load the file into memory
      }
    });
    
    // Add debug logging for file uploads
    instance.addHook('preHandler', async (request, reply) => {
      console.log('Document upload route called with content-type:', request.headers['content-type']);
      // Set CORS headers specifically for document upload routes
      reply.header('Access-Control-Allow-Origin', request.headers.origin || '*');
      reply.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
      reply.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');
      reply.header('Access-Control-Allow-Credentials', 'true');
      reply.header('Access-Control-Max-Age', '86400'); // 24 hours
      reply.header('Access-Control-Expose-Headers', 'Content-Length, Content-Type');
    });
  }, { prefix: '/api/documents' });
  
  // We'll handle multipart registration at the app level, not here
  // MultiParser should be registered at the main app level with compatibility for Fastify 4.x

  // Upload a file to S3 (supports both single and multiple files)
  fastify.post(
    '/api/documents/upload',
    {
      schema: {
        response: {
          200: Type.Union([
            FileUploadResponseSchema,
            MultipleFilesUploadResponseSchema
          ])
        },
      },
      // Add specific config for the upload endpoint
      config: {
        // We'll handle timeouts at the application level instead
      }
    },
    documentController.uploadDocument
  );
  
  // Add OPTIONS handler for the upload endpoint to properly handle CORS preflight
  fastify.options(
    '/api/documents/upload',
    (request, reply) => {
      // Set CORS headers for preflight requests
      reply.header('Access-Control-Allow-Origin', request.headers.origin || '*');
      reply.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
      reply.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');
      reply.header('Access-Control-Allow-Credentials', 'true');
      reply.header('Access-Control-Max-Age', '86400'); // 24 hours
      reply.header('Access-Control-Expose-Headers', 'Content-Length, Content-Type');
      reply.code(204).send();
    }
  );

  // Attach a document to an entity
  fastify.post(
    '/api/documents/:entityType/:entityId',
    {
      schema: {
        params: Type.Object({
          entityType: Type.Enum(EntityType),
          entityId: Type.Number(),
        }),
        body: CreateDocumentSchema,
        response: {
          201: DocumentResponseSchema,
        },
      },
    },
    documentController.attachDocument
  );

  // Attach multiple documents to an entity
  fastify.post(
    '/api/documents/:entityType/:entityId/batch',
    {
      schema: {
        params: Type.Object({
          entityType: Type.Enum(EntityType),
          entityId: Type.Number(),
        }),
        body: Type.Object({
          documents: Type.Array(CreateDocumentSchema)
        }),
        response: {
          201: Type.Object({
            documents: Type.Array(Type.Object({
              id: Type.Number(),
              fileName: Type.String(),
              fileKey: Type.String(),
              fileUrl: Type.String(),
              fileSize: Type.Number(),
              contentType: Type.String(),
              entityType: Type.Enum(EntityType),
              entityId: Type.Number(),
              displayName: Type.Optional(Type.String()),
              createdAt: Type.String({ format: 'date-time' }),
              updatedAt: Type.String({ format: 'date-time' })
            })),
            message: Type.String()
          }),
        },
      },
    },
    documentController.attachMultipleDocuments
  );

  // Get all documents for an entity
  fastify.get(
    '/api/documents/:entityType/:entityId',
    {
      schema: {
        params: Type.Object({
          entityType: Type.Enum(EntityType),
          entityId: Type.Number(),
        }),
        response: {
          200: DocumentsResponseSchema,
        },
      },
    },
    documentController.getEntityDocuments
  );

  // Delete a document
  fastify.delete(
    '/api/documents/:id',
    {
      schema: {
        params: Type.Object({
          id: Type.Number(),
        }),
        response: {
          200: Type.Object({
            message: Type.String(),
          }),
        },
      },
    },
    documentController.deleteDocument
  );
}

export default documentRoutes;
