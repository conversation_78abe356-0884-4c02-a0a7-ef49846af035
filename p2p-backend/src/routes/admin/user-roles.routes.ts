import { FastifyInstance } from 'fastify';

import { Type } from '@sinclair/typebox';
import { AppDataSource } from '../../db/typeorm.config';
import { UserRole } from '../../entities/UserRole.entity';
import { User } from '../../entities/User.entity';

export const userRolesRoutes = (server: FastifyInstance): void => {
  // Get all user roles
  server.get('/', async (request, reply) => {
    try {
      const userRoleRepository = AppDataSource.getRepository(UserRole);
      const userRoles = await userRoleRepository.find({
        relations: ['user']
      });
      
      return reply.send(userRoles);
    } catch (error) {
      console.error('Error fetching user roles:', error);
      return reply.status(500).send({ 
        error: true, 
        message: 'Failed to fetch user roles' 
      });
    }
  });

  // Get user role by ID
  server.get('/:id', {
    schema: {
      params: Type.Object({
        id: Type.Number()
      })
    },
    handler: async (request, reply) => {
      try {
        const { id } = request.params as { id: number };
        
        const userRoleRepository = AppDataSource.getRepository(UserRole);
        const userRole = await userRoleRepository.findOne({
          where: { id },
          relations: ['user']
        });
        
        if (!userRole) {
          return reply.status(404).send({ 
            error: true, 
            message: `User role with ID ${id} not found` 
          });
        }
        
        return reply.send(userRole);
      } catch (error) {
        const params = request.params as { id: number };
        console.error(`Error fetching user role ${params.id}:`, error);
        return reply.status(500).send({ 
          error: true, 
          message: 'Failed to fetch user role' 
        });
      }
    }
  });

  // Create a new user role
  server.post('/', {
    schema: {
      body: Type.Object({
        userId: Type.Number(),
        role: Type.String(),
        costCenterId: Type.Optional(Type.Number()),
        businessUnitId: Type.Optional(Type.Number())
      })
    },
    handler: async (request, reply) => {
      try {
        const roleData = request.body as any;
        
        // Check if user exists
        const userRepository = AppDataSource.getRepository(User);
        const user = await userRepository.findOne({
          where: { id: roleData.userId }
        });
        
        if (!user) {
          return reply.status(400).send({ 
            error: true, 
            message: `User with ID ${roleData.userId} not found` 
          });
        }
        
        // Check if user already has this role
        const userRoleRepository = AppDataSource.getRepository(UserRole);
        const existingRole = await userRoleRepository.findOne({
          where: {
            user: { id: roleData.userId },
            role: roleData.role
          }
        });
        
        if (existingRole) {
          return reply.status(400).send({ 
            error: true, 
            message: `User already has the role '${roleData.role}'` 
          });
        }
        
        // Create new user role
        const userRole = userRoleRepository.create({
          user: { id: roleData.userId },
          role: roleData.role,
          costCenterId: roleData.costCenterId,
          businessUnitId: roleData.businessUnitId
        });
        
        const savedUserRole = await userRoleRepository.save(userRole);
        
        // Fetch the saved role with relations
        const userRoleWithRelations = await userRoleRepository.findOne({
          where: { id: savedUserRole.id },
          relations: ['user']
        });
        
        return reply.status(201).send(userRoleWithRelations);
      } catch (error) {
        console.error('Error creating user role:', error);
        return reply.status(500).send({ 
          error: true, 
          message: 'Failed to create user role' 
        });
      }
    }
  });

  // Update an existing user role
  server.put('/:id', {
    schema: {
      params: Type.Object({
        id: Type.Number()
      }),
      body: Type.Object({
        userId: Type.Optional(Type.Number()),
        role: Type.Optional(Type.String()),
        costCenterId: Type.Optional(Type.Number()),
        businessUnitId: Type.Optional(Type.Number())
      })
    },
    handler: async (request, reply) => {
      try {
        const { id } = request.params as { id: number };
        const roleData = request.body as any;
        
        const userRoleRepository = AppDataSource.getRepository(UserRole);
        
        // Check if user role exists
        const userRole = await userRoleRepository.findOne({
          where: { id },
          relations: ['user']
        });
        
        if (!userRole) {
          return reply.status(404).send({ 
            error: true, 
            message: `User role with ID ${id} not found` 
          });
        }
        
        // Check if user exists if userId is provided
        if (roleData.userId) {
          const userRepository = AppDataSource.getRepository(User);
          const user = await userRepository.findOne({
            where: { id: roleData.userId }
          });
          
          if (!user) {
            return reply.status(400).send({ 
              error: true, 
              message: `User with ID ${roleData.userId} not found` 
            });
          }
          
          userRole.user = user;
        }
        
        // Update user role properties
        if (roleData.role) userRole.role = roleData.role;
        if (roleData.costCenterId !== undefined) userRole.costCenterId = roleData.costCenterId;
        if (roleData.businessUnitId !== undefined) userRole.businessUnitId = roleData.businessUnitId;
        
        const updatedUserRole = await userRoleRepository.save(userRole);
        
        // Fetch the updated role with relations
        const userRoleWithRelations = await userRoleRepository.findOne({
          where: { id },
          relations: ['user']
        });
        
        return reply.send(userRoleWithRelations);
      } catch (error) {
        const params = request.params as { id: number };
        console.error(`Error updating user role ${params.id}:`, error);
        return reply.status(500).send({ 
          error: true, 
          message: 'Failed to update user role' 
        });
      }
    }
  });

  // Delete a user role
  server.delete('/:id', {
    schema: {
      params: Type.Object({
        id: Type.Number()
      })
    },
    handler: async (request, reply) => {
      try {
        const { id } = request.params as { id: number };
        
        const userRoleRepository = AppDataSource.getRepository(UserRole);
        
        // Check if user role exists
        const userRole = await userRoleRepository.findOne({
          where: { id }
        });
        
        if (!userRole) {
          return reply.status(404).send({ 
            error: true, 
            message: `User role with ID ${id} not found` 
          });
        }
        
        // Delete user role
        await userRoleRepository.remove(userRole);
        
        return reply.send({ 
          success: true, 
          message: `User role with ID ${id} deleted successfully` 
        });
      } catch (error) {
        const params = request.params as { id: number };
        console.error(`Error deleting user role ${params.id}:`, error);
        return reply.status(500).send({ 
          error: true, 
          message: 'Failed to delete user role' 
        });
      }
    }
  });
};
