import { FastifyInstance } from 'fastify';
import { hasRole } from '../../middlewares/auth.middleware';
import { itemCategoriesRoutes } from './item-categories.routes';
import { usersRoutes } from './users.routes';
import { userRolesRoutes } from './user-roles.routes';
import { businessUnitsRoutes } from './business-units.routes';
import { costCentersRoutes } from './cost-centers.routes';
import { billingAddressAdminRoutes } from './billing-address.routes';

/**
 * Register all admin routes and common routes
 * Admin routes are protected by authentication and role-based access control
 * Only users with 'admin' roles can access most admin routes
 * Some routes like item-categories are accessible to all authenticated users
 */
export const adminRoutes = (server: FastifyInstance): void => {
  // Register item categories route separately outside of admin restriction
  // This makes it accessible to all authenticated users
  server.register(
    async (itemCategoriesServer) => {
      // Authentication is already handled globally, no need to add it again

      itemCategoriesRoutes(itemCategoriesServer);
    },
    { prefix: '/admin/item-categories' }
  );

  // Register admin routes with role-based restrictions
  server.register(
    async (adminServer) => {
      // Authentication is already handled globally, only add role-based access control
      // Apply role-based access control middleware to all admin routes
      adminServer.addHook('preHandler', hasRole(['admin']));

      // Register users routes
      adminServer.register(
        async (usersServer) => {
          usersRoutes(usersServer);
        },
        { prefix: '/users' }
      );

      // Register user roles routes
      adminServer.register(
        async (userRolesServer) => {
          userRolesRoutes(userRolesServer);
        },
        { prefix: '/user-roles' }
      );

      // Register business units routes
      adminServer.register(
        async (businessUnitsServer) => {
          businessUnitsRoutes(businessUnitsServer);
        },
        { prefix: '/business-units' }
      );

      // Register cost centers routes
      adminServer.register(
        async (costCentersServer) => {
          costCentersRoutes(costCentersServer);
        },
        { prefix: '/cost-centers' }
      );

      // Register billing addresses routes
      adminServer.register(
        async (billingAddressServer) => {
          billingAddressAdminRoutes(billingAddressServer);
        },
        { prefix: '/billing-addresses' }
      );

      // Note: Item categories routes are registered separately above
    },
    { prefix: '/admin' }
  );
};
