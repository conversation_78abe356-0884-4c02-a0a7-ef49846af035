import { FastifyInstance } from 'fastify';
import * as billingAddressController from '../../controllers/admin/billing-address.controller';
import {
  CreateBillingAddressSchema,
  UpdateBillingAddressSchema,
  IdParamSchema,
  BillingAddressResponseSchema,
  BillingAddressListResponseSchema,
  BillingAddressQuerySchema
} from '../../schemas/billing-address.schema';
import { hasRole } from '../../middlewares/auth.middleware';
import { asRouteHandler } from '../../types/route-helper';

export const billingAddressAdminRoutes = async (fastify: FastifyInstance): Promise<void> => {
  // Get all billing addresses
  fastify.get(
    '/',
    {
      schema: {
        description: 'Get all billing addresses',
        tags: ['admin', 'billing-addresses'],
        querystring: BillingAddressQuerySchema,
        response: {
          200: BillingAddressListResponseSchema,
          500: {
            type: 'object',
            properties: {
              error: { type: 'boolean' },
              message: { type: 'string' }
            }
          }
        }
      },
      preHandler: hasRole(['admin']) // Authentication is already handled globally
    },
    asRouteHandler(billingAddressController.getAllBillingAddresses)
  );

  // Get billing address by ID
  fastify.get(
    '/:id',
    {
      schema: {
        description: 'Get billing address by ID',
        tags: ['admin', 'billing-addresses'],
        params: IdParamSchema,
        response: {
          200: BillingAddressResponseSchema,
          404: {
            type: 'object',
            properties: {
              error: { type: 'boolean' },
              message: { type: 'string' }
            }
          },
          500: {
            type: 'object',
            properties: {
              error: { type: 'boolean' },
              message: { type: 'string' }
            }
          }
        }
      },
      preHandler: hasRole(['admin']) // Authentication is already handled globally
    },
    asRouteHandler(billingAddressController.getBillingAddressById)
  );

  // Create a new billing address
  fastify.post(
    '/',
    {
      schema: {
        description: 'Create a new billing address',
        tags: ['admin', 'billing-addresses'],
        body: CreateBillingAddressSchema,
        response: {
          201: BillingAddressResponseSchema,
          500: {
            type: 'object',
            properties: {
              error: { type: 'boolean' },
              message: { type: 'string' }
            }
          }
        }
      },
      preHandler: hasRole(['admin']) // Authentication is already handled globally
    },
    asRouteHandler(billingAddressController.createBillingAddress)
  );

  // Update a billing address
  fastify.put(
    '/:id',
    {
      schema: {
        description: 'Update a billing address',
        tags: ['admin', 'billing-addresses'],
        params: IdParamSchema,
        body: UpdateBillingAddressSchema,
        response: {
          200: BillingAddressResponseSchema,
          404: {
            type: 'object',
            properties: {
              error: { type: 'boolean' },
              message: { type: 'string' }
            }
          },
          500: {
            type: 'object',
            properties: {
              error: { type: 'boolean' },
              message: { type: 'string' }
            }
          }
        }
      },
      preHandler: hasRole(['admin']) // Authentication is already handled globally
    },
    asRouteHandler(billingAddressController.updateBillingAddress)
  );

  // Soft delete a billing address
  fastify.delete(
    '/:id',
    {
      schema: {
        description: 'Soft delete a billing address',
        tags: ['admin', 'billing-addresses'],
        params: IdParamSchema,
        response: {
          200: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              message: { type: 'string' }
            }
          },
          404: {
            type: 'object',
            properties: {
              error: { type: 'boolean' },
              message: { type: 'string' }
            }
          },
          500: {
            type: 'object',
            properties: {
              error: { type: 'boolean' },
              message: { type: 'string' }
            }
          }
        }
      },
      preHandler: hasRole(['admin']) // Authentication is already handled globally
    },
    asRouteHandler(billingAddressController.deleteBillingAddress)
  );

  // Permanently delete a billing address
  fastify.delete(
    '/:id/permanent',
    {
      schema: {
        description: 'Permanently delete a billing address',
        tags: ['admin', 'billing-addresses'],
        params: IdParamSchema,
        response: {
          200: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              message: { type: 'string' }
            }
          },
          404: {
            type: 'object',
            properties: {
              error: { type: 'boolean' },
              message: { type: 'string' }
            }
          },
          500: {
            type: 'object',
            properties: {
              error: { type: 'boolean' },
              message: { type: 'string' }
            }
          }
        }
      },
      preHandler: hasRole(['admin']) // Authentication is already handled globally
    },
    asRouteHandler(billingAddressController.permanentlyDeleteBillingAddress)
  );

  // Restore a soft-deleted billing address
  fastify.post(
    '/:id/restore',
    {
      schema: {
        description: 'Restore a soft-deleted billing address',
        tags: ['admin', 'billing-addresses'],
        params: IdParamSchema,
        response: {
          200: {
            type: 'object',
            properties: {
              success: { type: 'boolean' },
              message: { type: 'string' },
              data: BillingAddressResponseSchema
            }
          },
          404: {
            type: 'object',
            properties: {
              error: { type: 'boolean' },
              message: { type: 'string' }
            }
          },
          500: {
            type: 'object',
            properties: {
              error: { type: 'boolean' },
              message: { type: 'string' }
            }
          }
        }
      },
      preHandler: hasRole(['admin']) // Authentication is already handled globally
    },
    asRouteHandler(billingAddressController.restoreBillingAddress)
  );
};
