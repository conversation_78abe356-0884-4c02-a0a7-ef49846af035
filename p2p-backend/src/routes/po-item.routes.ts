import { FastifyInstance } from 'fastify';
import {
  getPOItems,
  getPOItemById,
  createPOItem,
  updatePOItem,
  deletePOItem
} from '../controllers/po-item.controller';
export const poItemRoutes = (server: FastifyInstance): void => {
  // Authentication is already handled globally, no need to add it again

  // Get all items for a specific purchase order
  server.get('/purchase-orders/:poId/items', getPOItems);

  // Get a specific PO item by ID
  server.get('/po-items/:id', getPOItemById);

  // Create a new PO item for a specific purchase order
  server.post('/purchase-orders/:poId/items', createPOItem);

  // Update a PO item
  server.put('/po-items/:id', updatePOItem);

  // Delete a PO item
  server.delete('/po-items/:id', deletePOItem);
};
