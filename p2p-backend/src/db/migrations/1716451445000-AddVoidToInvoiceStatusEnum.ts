import { MigrationInterface, QueryRunner } from "typeorm";

export class AddVoidToInvoiceStatusEnum1716451445000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE invoices
      MODIFY status ENUM('PENDING', 'APPROVED', 'REJECTED', 'PAYMENT_COMPLETED', 'VOID')
      NOT NULL DEFAULT 'PENDING';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // First, update any VOID statuses to REJECTED before rolling back
    await queryRunner.query(`
      UPDATE invoices
      SET status = 'PENDING'
      WHERE status = 'VOID';
    `);

    // Then modify the column to remove the VOID option
    await queryRunner.query(`
      ALTER TABLE invoices
      MODIFY status ENUM('PENDING', 'APPROVED', 'REJECTED', 'PAYMENT_COMPLETED')
      NOT NULL DEFAULT 'PENDING';
    `);
  }
}
