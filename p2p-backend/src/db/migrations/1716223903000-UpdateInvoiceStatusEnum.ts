import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateInvoiceStatusEnum1716223903000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE invoices
      MODIFY status ENUM('PENDING', 'APPROVED', 'REJECTED', 'PAYMENT_COMPLETED')
      NOT NULL DEFAULT 'PENDING';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // First, update any PAYMENT_COMPLETED statuses to APPROVED before rolling back
    await queryRunner.query(`
      UPDATE invoices
      SET status = 'APPROVED'
      WHERE status = 'PAYMENT_COMPLETED';
    `);

    // Then modify the column to remove the PAYMENT_COMPLETED option
    await queryRunner.query(`
      ALTER TABLE invoices
      MODIFY status ENUM('PENDING', 'APPROVED', 'REJECTED')
      NOT NULL DEFAULT 'PENDING';
    `);
  }
}
