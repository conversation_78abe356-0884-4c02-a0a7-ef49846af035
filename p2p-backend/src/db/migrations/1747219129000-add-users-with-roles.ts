import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUsersWithRoles1747219129000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        const users = [
            {
              first_name: "<PERSON><PERSON><PERSON>",
              last_name: "<PERSON>da<PERSON>",
              username: "kaush<PERSON>",
              email: "<EMAIL>",
              password: "kaushal@123",
              cc_head: ["Full Stack - Pomo (Non FX-D)", "Full Stack - Mango (Non FX-D)"]
            },
            {
              first_name: "Viren",
              last_name: "<PERSON><PERSON>",
              username: "viren",
              email: "<EMAIL>",
              password: "viren@123",
              cc_head: ["Full Stack - Apple (Non FX-D)"]
            },
            {
              first_name: "A<PERSON>",
              last_name: "<PERSON><PERSON><PERSON>",
              username: "ajit",
              email: "<EMAIL>",
              password: "ajit@123",
              cc_head: ["Exports - Banana"]
            },
            {
              first_name: "<PERSON><PERSON><PERSON>",
              last_name: "<PERSON>",
              username: "di<PERSON><PERSON>",
              email: "divy<PERSON>.<EMAIL>",
              password: "divy<PERSON>@123",
              cc_head: ["Exports - Others (<PERSON> <PERSON>ana)"]
            },
            {
              first_name: "Neeti",
              last_name: "Jain",
              username: "neeti",
              email: "<EMAIL>",
              password: "neeti@123",
              cc_head: ["Imports"]
            },
            {
              first_name: "Rajiv",
              last_name: "S",
              username: "rajiv",
              email: "<EMAIL>",
              password: "rajiv@123",
              cc_head: ["Jeevana", "Padru", "Bhuj", "Nashik", "Solapur", "Pune", "Others - FX-S - Pomo"]
            },
            {
              first_name: "Ankur",
              last_name: "Badonia",
              username: "ankur",
              email: "<EMAIL>",
              password: "ankur@123",
              cc_head: ["Warrangal", "Bellampalli", "Others - FX-S - Mango", "Others - FX-S - Apple", "Rohru", "Narkanda"]
            },
            {
              first_name: "Vishal",
              last_name: "Verma",
              username: "vishal",
              email: "<EMAIL>",
              password: "vishal@123",
              cc_head: ["FX-D - Bangalore"]
            },
            {
              first_name: "Kiran",
              last_name: "Naik",
              username: "kiran",
              email: "<EMAIL>",
              password: "kiran@123",
              cc_head: ["OSK", "Mango", "Banana Exports"]
            },
            {
              first_name: "Rohin",
              last_name: "Gupta",
              username: "rohin",
              email: "<EMAIL>",
              password: "rohin@123",
              cc_head: ["Finance", "Administation", "Others"]
            },
            {
              first_name: "Neville",
              last_name: "",
              username: "neville",
              email: "<EMAIL>",
              password: "neville@123",
              cc_head: ["Employee Engagment (HR)"]
            },
            {
              first_name: "Pallav",
              last_name: "Shil",
              username: "pallav",
              email: "<EMAIL>",
              password: "pallav@123",
              cc_head: ["Legal"]
            },
            {
              first_name: "Rajeev",
              last_name: "Kapoor",
              username: "rajeev",
              email: "<EMAIL>",
              password: "rajeev@123",
              cc_head: ["Corporate affair / Compliance"]
            },
            {
              first_name: "Mrudhukar",
              last_name: "",
              username: "mrudhukar",
              email: "<EMAIL>",
              password: "mrudhukar@123",
              cc_head: ["Tech"]
            }
          ];

        for (const user of users) {
            let userIdResult = await queryRunner.query(
                `SELECT id FROM users WHERE email = ?`,
                [user.email]
            );
            let userId;
            if (userIdResult.length > 0) {
                userId = userIdResult[0].id;
            } else {
                await queryRunner.query(
                    `INSERT INTO users (first_name, last_name, username, email, password) VALUES (?, ?, ?, ?, ?)` ,
                    [user.first_name, user.last_name, user.username, user.email, user.password]
                );
                userIdResult = await queryRunner.query(
                    `SELECT id FROM users WHERE email = ?`,
                    [user.email]
                );
                userIdResult = await queryRunner.query(
                    `SELECT id FROM users WHERE email = ?`,
                    [user.email]
                );
                userId = userIdResult[0].id;
            }

            // 4. For each cc_head, find cost_center and insert into user_roles
            for (const ccName of user.cc_head) {
                const ccResult = await queryRunner.query(
                    `SELECT id, business_unit_id FROM cost_centers WHERE name = ?`,
                    [ccName]
                );
                if (ccResult.length > 0) {
                    const ccId = ccResult[0].id;
                    const buId = ccResult[0].business_unit_id;
                    // Check if role already exists
                    const roleExists = await queryRunner.query(
                        `SELECT id FROM user_roles WHERE user_id = ? AND cost_center_id = ? AND business_unit_id = ? AND role = ?`,
                        [userId, ccId, buId, 'cc_head']
                    );
                    if (roleExists.length === 0) {
                        await queryRunner.query(
                            `INSERT INTO user_roles (user_id, cost_center_id, business_unit_id, role) VALUES (?, ?, ?, ?)` ,
                            [userId, ccId, buId, 'cc_head']
                        );
                    }
                }
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove only the user_roles and users added by this migration
        const userEmails = ["<EMAIL>"];
        for (const email of userEmails) {
            const userResult = await queryRunner.query(
                `SELECT id FROM users WHERE email = ?`,
                [email]
            );
            if (userResult.length > 0) {
                const userId = userResult[0].id;
                await queryRunner.query(
                    `DELETE FROM user_roles WHERE user_id = ? AND role = ?`,
                    [userId, 'cc_head']
                );
                await queryRunner.query(
                    `DELETE FROM users WHERE id = ?`,
                    [userId]
                );
            }
        }
    }
}
