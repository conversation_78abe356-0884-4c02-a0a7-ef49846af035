import { MigrationInterface, QueryRunner } from "typeorm";

export class AddCompanyNameAndGstinToBillingAddresses1748424361000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE billing_addresses
      ADD COLUMN company_name VARCHAR(255) NULL AFTER address,
      ADD COLUMN gstin VARCHAR(50) NULL AFTER company_name;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE billing_addresses
      DROP COLUMN company_name,
      DROP COLUMN gstin;
    `);
  }
}
