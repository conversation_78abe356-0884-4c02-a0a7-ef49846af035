import { MigrationInterface, QueryRunner } from "typeorm";

export class AddStatusToInvoice1746964745000 implements MigrationInterface {
    name = 'AddStatusToInvoice1746964745000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the status column already exists in the invoices table
        const table = await queryRunner.getTable("invoices");
        const statusColumn = table?.findColumnByName("status");
        
        if (!statusColumn) {
            // Add the status column - MySQL doesn't allow default values for TEXT columns
            await queryRunner.query(`
                ALTER TABLE invoices 
                ADD COLUMN status VARCHAR(255) NOT NULL
            `);
            
            // Set default value after creating the column
            await queryRunner.query(`
                UPDATE invoices
                SET status = 'PENDING'
                WHERE status IS NULL
            `);
            
            console.log('Status column added to invoices table');
        } else {
            console.log('Status column already exists in invoices table');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Check if the status column exists before trying to remove it
        const table = await queryRunner.getTable("invoices");
        const statusColumn = table?.findColumnByName("status");
        
        if (statusColumn) {
            await queryRunner.query(`
                ALTER TABLE invoices 
                DROP COLUMN status
            `);
            
            console.log('Status column removed from invoices table');
        }
    }
}
