import {MigrationInterface, QueryRunner} from "typeorm";

export class addBusinessUnitsAndCostCenters1746648508000 implements MigrationInterface {
    name = 'addBusinessUnitsAndCostCenters1746648508000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        const businessUnits = {
            "FX-S - Pomo": ["<PERSON><PERSON><PERSON>", "Padru", "Bhuj", "Nashik", "Solapur", "Pune", "Others - FX-S - Pomo"],
            "FX-S - Mango": ["Warangal", "Hyderabad", "Others - FX-S - Mango"],
            "FX-S - Apple": ["Narkhanda", "Others - FX-S - Apple"],
            "Exports - Banana": ["Exports - Banana"],
            "Exports - Others (Non Banana)": ["Exports - Others (Non Banana)"],
            "FX-D": ["FX-D - Bangalore"],
            "Full Stack - Pomo (Non FX-D)": ["Full Stack - Pomo (Non FX-D)"],
            "Full Stack - Imported (Non FX-D)": ["Full Stack - Imported (Non FX-D)"],
            "Full Stack - Mango (Non FX-D)": ["Full Stack - Mango (Non FX-D)"],
            "Full Stack - Apple (Non FX-D)": ["Full Stack - Apple (Non FX-D)"],
            "Velens": ["Velens"],
            "Captive": ["OSK", "Mango", "Banana Exports"],
            "Corporate": ["Employee Engagment (HR)", "Administation", "Others", "Legal", "Finance", "Corporate affair / Compliance", "Tech"],
            "Revenue Assurance": ["Collections", "Credit"],
            "Imports": ["Imports"]
        };

        // Insert business units
        for (const [businessUnitName, costCenters] of Object.entries(businessUnits)) {
            // Insert business unit
            await queryRunner.query(
                `INSERT INTO business_units (name) VALUES (?)`,
                [businessUnitName]
            );

            // Get the ID of the newly created business unit
            const businessUnitId = await queryRunner.query(
                `SELECT id FROM business_units WHERE name = ?`,
                [businessUnitName]
            );

            // Insert cost centers for this business unit
            for (const costCenter of costCenters) {
                await queryRunner.query(
                    `INSERT INTO cost_centers (name, business_unit_id) VALUES (?, ?)`,
                    [costCenter, businessUnitId[0].id]
                );
            }
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Delete all cost centers and business units
        await queryRunner.query(`DELETE FROM cost_centers`);
        await queryRunner.query(`DELETE FROM business_units`);
    }
}
