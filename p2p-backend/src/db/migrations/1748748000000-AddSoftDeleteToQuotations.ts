import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSoftDeleteToQuotations1748748000000 implements MigrationInterface {
    name = 'AddSoftDeleteToQuotations1748748000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add deletedAt column to track when the record was soft deleted
        await queryRunner.query(
            `ALTER TABLE quotations ADD COLUMN deleted_at DATETIME NULL`
        );
        
        // Add index on deleted_at for query performance
        await queryRunner.query(
            `CREATE INDEX idx_quotations_deleted_at ON quotations (deleted_at)`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the index first
        await queryRunner.query(
            `DROP INDEX idx_quotations_deleted_at ON quotations`
        );
        
        // Drop the column
        await queryRunner.query(
            `ALTER TABLE quotations DROP COLUMN deleted_at`
        );
    }
}
