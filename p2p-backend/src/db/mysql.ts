import mysql from 'mysql2/promise';
import { config } from '../config';

// Create a connection pool
const pool = mysql.createPool({
  host: config.MYSQL.HOST,
  port: config.MYSQL.PORT,
  user: config.MYSQL.USER,
  password: config.MYSQL.PASSWORD,
  database: config.MYSQL.DATABASE,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// --- Connection Pool Monitoring ---
import { setInterval } from 'timers';
const heldConnections = new Map();
const origGetConnection = pool.getConnection.bind(pool);
pool.getConnection = async function() {
  const conn = await origGetConnection();
  const acquiredAt = Date.now();
  heldConnections.set(conn, acquiredAt);
  console.log(`[mysql2 Pool] Connection acquired at ${new Date(acquiredAt).toISOString()} (ID: ${conn.threadId})`);
  // Patch release
  const origRelease = conn.release.bind(conn);
  conn.release = function(...rArgs) {
    const releasedAt = Date.now();
    const heldTime = releasedAt - acquiredAt;
    if (heldTime > 5000) {
      console.warn(`[mysql2 Pool] Connection held for ${heldTime} ms (ID: ${conn.threadId})`);
    }
    heldConnections.delete(conn);
    console.log(`[mysql2 Pool] Connection released at ${new Date(releasedAt).toISOString()} (ID: ${conn.threadId})`);
    return origRelease(...rArgs);
  };
  return conn;
};
// Periodically log pool stats
setInterval(() => {
  if ((pool as any)._allConnections && (pool as any)._freeConnections) {
    console.log(`[mysql2 Pool] Total: ${(pool as any)._allConnections.length}, Free: ${(pool as any)._freeConnections.length}, Waiting: ${(pool as any)._connectionQueue.length}`);
  }
  // Log all currently held connections
  for (const [conn, acquiredAt] of heldConnections.entries()) {
    const heldTime = Date.now() - acquiredAt;
    if (heldTime > 5000) {
      console.warn(`[mysql2 Pool] STILL held for ${heldTime} ms (ID: ${conn.threadId})`);
    }
  }
}, 60000);


export const connectToDatabase = async (): Promise<void> => {
  try {
    // Test the connection
    const connection = await pool.getConnection();
    console.log('Successfully connected to MySQL database');
    connection.release();
  } catch (error) {
    console.error('Failed to connect to MySQL database:', error);
    throw error;
  }
};

// Export the pool to be used in other modules
export const getPool = (): mysql.Pool => pool;

// Helper function to execute queries
export const query = async <T>(sql: string, params?: any[]): Promise<T> => {
  try {
    const [results] = await pool.execute(sql, params);
    return results as T;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
};
