import { DataSource } from 'typeorm';
import { config } from '../config';
import path from 'path';
import * as bcrypt from 'bcrypt';

// Import entities
import { PurchaseRequest } from '../entities/PurchaseRequest.entity';
import { PrItem } from '../entities/PrItem.entity';
import { Quotation } from '../entities/Quotation.entity';
import { Approval } from '../entities/Approval.entity';
import { User } from '../entities/User.entity';
import { UserRole } from '../entities/UserRole.entity';
import { BusinessUnit } from '../entities/BusinessUnit.entity';
import { CostCenter } from '../entities/CostCenter.entity';
import { PurchaseOrder } from '../entities/PurchaseOrder.entity';
import { PoItem } from '../entities/PoItem.entity';
import { ItemCategory } from '../entities/ItemCategory.entity';
import { Invoice } from '../entities/Invoice.entity';
import { Grn } from '../entities/Grn.entity';
import { GrnItem } from '../entities/GrnItem.entity';
import { InvoiceItem } from '../entities/InvoiceItem.entity';
import { Document } from '../entities/document.entity';
import { BillingAddress } from '../entities/BillingAddress.entity';

export const AppDataSource = new DataSource({
  type: 'mysql',
  host: config.MYSQL.HOST,
  port: config.MYSQL.PORT,
  username: config.MYSQL.USER,
  password: config.MYSQL.PASSWORD,
  database: config.MYSQL.DATABASE,
  synchronize: false, // Only synchronize in development
  // synchronize: config.NODE_ENV === 'development', // Only synchronize in development
  logging: config.NODE_ENV === 'development',
  extra: {
<<<<<<< Updated upstream
    connectionLimit: 50,
    connectTimeout: 10000, // 20 seconds
    acquireTimeout: 20000, // 20 seconds
    timeout: 30000, // 30 seconds
    queueLimit: 100, // Maximum connection requests to queue
    waitForConnections: true // Wait for connections rather than error immediately
=======
    connectionLimit: 20, // Increased from 10 to handle more concurrent requests
    acquireTimeout: 60000, // 60 seconds timeout for acquiring connections
    timeout: 60000, // 60 seconds query timeout
    reconnect: true,
    idleTimeout: 300000, // 5 minutes idle timeout
    queueLimit: 0 // No limit on queue
>>>>>>> Stashed changes
  },
  // Set a maximum execution time for queries
  maxQueryExecutionTime: 10000, // Log queries taking longer than 10 seconds
  entities: [
    PurchaseRequest,
    PrItem,
    Quotation,
    Approval,
    User,
    UserRole,
    BusinessUnit,
    CostCenter,
    PurchaseOrder,
    PoItem,
    ItemCategory,
    Invoice,
    Grn,
    GrnItem,
    InvoiceItem,
    Document,
    BillingAddress,
  ],
  migrations: [path.join(__dirname, 'migrations/**/*.{ts,js}')],
  subscribers: [path.join(__dirname, '../subscribers/**/*.{ts,js}')]
});

// --- Connection Pool Monitoring ---
import { setInterval } from 'timers';

if (AppDataSource.driver && (AppDataSource.driver as any).pool) {
  const pool = (AppDataSource.driver as any).pool;
  const heldConnections = new Map();

  const origGetConnection = pool.getConnection.bind(pool);
  pool.getConnection = async function(...args: any[]) {
    const conn = await origGetConnection(...args);
    const acquiredAt = Date.now();
    heldConnections.set(conn, acquiredAt);
    console.log(`[TypeORM Pool] Connection acquired at ${new Date(acquiredAt).toISOString()} (ID: ${conn.threadId})`);
    const origRelease = conn.release.bind(conn);
    conn.release = function(...rArgs: any[]) {
      const releasedAt = Date.now();
      const heldTime = releasedAt - acquiredAt;
      if (heldTime > 5000) {
        console.warn(`[TypeORM Pool] Connection held for ${heldTime} ms (ID: ${conn.threadId})`);
      }
      heldConnections.delete(conn);
      console.log(`[TypeORM Pool] Connection released at ${new Date(releasedAt).toISOString()} (ID: ${conn.threadId})`);
      return origRelease(...rArgs);
    };
    
    const connectionTimeout = setTimeout(() => {
      if (heldConnections.has(conn)) {
        const heldTime = Date.now() - acquiredAt;
        console.error(`[TypeORM Pool] Connection timeout after ${heldTime}ms - forcing release (ID: ${conn.threadId})`);
        try {
          conn.release();
        } catch (err) {
          console.error(`[TypeORM Pool] Error releasing timed-out connection:`, err);
        }
      }
    }, 120000); // 2 minute timeout
    
    const origDestroy = conn.destroy?.bind(conn);
    if (origDestroy) {
      conn.destroy = function(...dArgs: any[]) {
        clearTimeout(connectionTimeout);
        heldConnections.delete(conn);
        return origDestroy(...dArgs);
      };
    }
    
    return conn;
  };

  setInterval(() => {
    if (pool._allConnections && pool._freeConnections) {
      console.log(`[TypeORM Pool] Total: ${pool._allConnections.length}, Free: ${pool._freeConnections.length}, Waiting: ${pool._connectionQueue?.length || 0}`);
    }
    
    const now = Date.now();
    for (const [conn, acquiredAt] of heldConnections.entries()) {
      const heldTime = now - acquiredAt;
      if (heldTime > 300000) { // 5 minutes
        console.error(`[TypeORM Pool] Connection held for ${heldTime}ms - FORCE RELEASING (ID: ${conn.threadId})`);
        try {
          conn.release();
        } catch (err) {
          console.error(`[TypeORM Pool] Error force-releasing connection:`, err);
          try {
            conn.destroy();
          } catch (destroyErr) {
            console.error(`[TypeORM Pool] Error destroying stuck connection:`, destroyErr);
          }
        }
      } else if (heldTime > 10000) { // 10 seconds
        console.warn(`[TypeORM Pool] Connection held for ${heldTime}ms (ID: ${conn.threadId})`);
      }
    }
  }, 30000); // Check every 30 seconds
}

// --- Memory & Handle Monitoring ---
setInterval(() => {
  const used = process.memoryUsage();
  console.log('[Process] Memory RSS:', (used.rss / 1024 / 1024).toFixed(2), 'MB');
  console.log('[Process] Heap Used:', (used.heapUsed / 1024 / 1024).toFixed(2), 'MB');
  if (typeof (process as any)._getActiveHandles === 'function') {
    console.log('[Process] Active Handles:', (process as any)._getActiveHandles().length);
  }
  if (typeof (process as any)._getActiveRequests === 'function') {
    console.log('[Process] Active Requests:', (process as any)._getActiveRequests().length);
  }
}, 60000);

export const initializeDatabase = async (): Promise<void> => {
  try {
    await AppDataSource.initialize();
    console.log('Data Source has been initialized!');
    
    // Create default admin user if it doesn't exist
    await createDefaultAdminUser();
    if (!AppDataSource.options.synchronize){
      await AppDataSource.runMigrations();
    }
  } catch (error) {
    console.error('Error during Data Source initialization:', error);
    throw error;
  }
};

async function createDefaultAdminUser() {
  try {
    const userRepository = AppDataSource.getRepository(User);
    const userRoleRepository = AppDataSource.getRepository(UserRole);
    
    // Check if admin user already exists
    let adminUser = await userRepository.findOne({ 
      where: { email: '<EMAIL>' } 
    });
    
    if (!adminUser) {
      // Create new admin user
      console.log('Creating default admin user...');
      
      // Hash the password
      const hashedPassword = await bcrypt.hash('Admin@123', 10);
      
      adminUser = userRepository.create({
        username: 'Sai Charan',
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Sai',
        lastName: 'Charan',
        isActive: true
      });
      
      await userRepository.save(adminUser);
      console.log('Default admin user created successfully!');
    } else {
      console.log('Admin user already exists, skipping creation.');
    }
    
    // Check if admin role already exists for this user
    const existingRole = await userRoleRepository.findOne({
      where: {
        userId: adminUser.id,
        role: 'admin'
      }
    });
    
    if (!existingRole) {
      // Create admin role for the user
      const adminRole = userRoleRepository.create({
        userId: adminUser.id,
        role: 'admin'
      });
      
      await userRoleRepository.save(adminRole);
      console.log('Admin role assigned to default user successfully!');
    } else {
      console.log('Admin role already assigned to user, skipping.');
    }
  } catch (error) {
    console.error('Error creating default admin user:', error);
  }
}
