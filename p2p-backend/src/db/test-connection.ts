import { connectToDatabase, getPool } from './mysql';

const testConnection = async () => {
  try {
    // Connect to the database
    await connectToDatabase();
    
    // Test a simple query
    const pool = getPool();
    const [rows] = await pool.query('SELECT 1 + 1 as result');
    
    console.log('Database connection test successful!');
    console.log('Query result:', rows);
    
    console.log('\nDatabase configuration is working correctly.');
    process.exit(0);
  } catch (error) {
    console.error('Database connection test failed:', error);
    process.exit(1);
  }
};

testConnection();
