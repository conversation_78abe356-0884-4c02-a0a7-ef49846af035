import { Entity, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, JoinColumn } from 'typeorm';
import { PurchaseRequest } from './PurchaseRequest.entity';
import { ItemCategory } from './ItemCategory.entity';

@Entity('pr_items')
export class PrItem {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'pr_id' })
  prId: number;

  @Column({ name: 'item_category_id', nullable: true })
  itemCategoryId: number | null;

  @Column({ name: 'item_name', length: 255})
  itemName: string;

  @Column({ name: 'item_description', type: 'text', nullable: true })
  itemDescription: string;

  @Column({ type: 'decimal', precision: 10, scale: 2})
  quantity: number;

  @Column({ length: 50})
  uom: string;

  @Column({ name: 'estimated_price_per_quanity', type: 'decimal', precision: 12, scale: 2})
  estimatedPricePerQuantity: number;

  @ManyToOne(() => PurchaseRequest, (purchaseRequest) => purchaseRequest.items)
  @JoinColumn({ name: 'pr_id' })
  purchaseRequest: PurchaseRequest;

  @ManyToOne(() => ItemCategory)
  @JoinColumn({ name: 'item_category_id' })
  itemCategory: ItemCategory;
}
