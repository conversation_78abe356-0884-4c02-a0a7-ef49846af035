import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { PurchaseOrder } from './PurchaseOrder.entity';
import { User } from './User.entity';
import { GrnItem } from './GrnItem.entity';
import { Invoice } from './Invoice.entity';

@Entity('grns')
export class Grn {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'po_id' })
  poId: number;

  @ManyToOne(() => PurchaseOrder)
  @JoinColumn({ name: 'po_id' })
  purchaseOrder: PurchaseOrder;

  @Column({ name: 'created_by' })
  createdBy: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @OneToMany(() => GrnItem, grnItem => grnItem.grn)
  grnItems: GrnItem[];

  @OneToMany(() => Invoice, invoice => invoice.grn)
  invoices: Invoice[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
