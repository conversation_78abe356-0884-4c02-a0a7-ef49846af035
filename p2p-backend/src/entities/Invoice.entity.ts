import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, Join<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Grn } from './Grn.entity';
import { User } from './User.entity';
import { InvoiceItem } from './InvoiceItem.entity';

// Define invoice types as enum for type safety
export enum InvoiceType {
  ADVANCE = 'Advance',
  BILL = 'Bill'
}

// Define payment request status enum
export enum PaymentRequestStatus {
  NOT_REQUESTED = 'NOT_REQUESTED',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PAID = 'PAID',
  FAILED = 'FAILED'
}

// Define invoice status enum
export enum InvoiceStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  PAYMENT_COMPLETED = 'PAYMENT_COMPLETED',
  VOID = 'VOID'
}

@Entity('invoices')
export class Invoice {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'grn_id' })
  grnId: number;

  @ManyToOne(() => Grn)
  @JoinColumn({ name: 'grn_id' })
  grn: Grn;

  @Column({
    type: 'enum',
    enum: InvoiceType,
    default: InvoiceType.BILL
  })
  type: InvoiceType;

  @Column({ name: 'invoice_number' })
  invoiceNumber: string;

  @Column({ name: 'invoice_date', type: 'datetime' })
  invoiceDate: Date;

  @Column({ name: 'invoice_value', type: 'decimal', precision: 10, scale: 2 })
  invoiceValue: number;

  @Column({ name: 'remarks', type: 'text', nullable: true })
  remarks: string;

  @Column({type: 'enum', enum: InvoiceStatus, default: InvoiceStatus.PENDING})
  status: InvoiceStatus;

  @Column({ name: 'created_by' })
  createdBy: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  // One-to-many relationship with InvoiceItems
  @OneToMany(() => InvoiceItem, invoiceItem => invoiceItem.invoice)
  invoiceItems: InvoiceItem[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Payment request fields
  @Column({
    name: 'payment_request_status',
    type: 'enum',
    enum: PaymentRequestStatus,
    default: PaymentRequestStatus.NOT_REQUESTED
  })
  paymentRequestStatus: PaymentRequestStatus;

  @Column({ name: 'payment_request_id', nullable: true })
  paymentRequestId: string;

  @Column({ name: 'payment_request_external_id', nullable: true })
  paymentRequestExternalId: string;

  @Column({ name: 'payment_request_created_at', type: 'datetime', nullable: true })
  paymentRequestCreatedAt: Date;

  @Column({ name: 'payment_request_updated_at', type: 'datetime', nullable: true })
  paymentRequestUpdatedAt: Date;

  @Column({ name: 'payment_request_remarks', type: 'text', nullable: true })
  paymentRequestRemarks: string;
}
