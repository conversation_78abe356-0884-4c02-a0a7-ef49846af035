import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from 'typeorm';

@Entity('quotations')
export class Quotation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'pr_id' })
  prId: number;

  @Column({ name: 'payment_terms', type: 'text', nullable: true })
  paymentTerms: string;

  @Column({ name: 'delivery_terms', type: 'text', nullable: true })
  deliveryTerms: string;

  @Column({ name: 'remarks', type: 'text', nullable: true })
  remarks: string;

  @Column({ name: 'vendor_id', type: 'int', nullable: true })
  vendorId: number | null;

  @Column({ name: 'vendor_name', length: 255 })
  vendorName: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'deleted_at', type: 'datetime', nullable: true })
  @Index()
  deletedAt: Date | null;

  @ManyToOne('PurchaseRequest', 'quotations')
  @JoinColumn({ name: 'pr_id' })
  purchaseRequest: any;
}
