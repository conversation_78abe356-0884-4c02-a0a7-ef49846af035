import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON>in<PERSON><PERSON><PERSON>n, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Grn } from './Grn.entity';
import { PoItem } from './PoItem.entity';
import { User } from './User.entity';

@Entity('grn_items')
export class GrnItem {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'grn_id' })
  grnId: number;

  @ManyToOne(() => Grn, grn => grn.grnItems)
  @JoinColumn({ name: 'grn_id' })
  grn: Grn;

  @Column({ name: 'po_item_id' })
  poItemId: number;

  @ManyToOne(() => PoItem)
  @JoinColumn({ name: 'po_item_id' })
  poItem: PoItem;

  @Column({ name: 'grn_quantity', type: 'decimal', precision: 10, scale: 2 })
  grnQuantity: number;

  @Column({ name: 'created_by' })
  createdBy: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
