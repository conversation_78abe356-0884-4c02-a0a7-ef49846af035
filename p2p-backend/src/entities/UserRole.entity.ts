import { Entity, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, Join<PERSON>olumn } from 'typeorm';
import { User } from './User.entity';
import { ApprovalRole } from '../constants/enums';

@Entity('user_roles')
export class UserRole {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ type: 'varchar', length: 50 })
  role: string;

  @Column({ name: 'cost_center_id', type: 'int', nullable: true })
  costCenterId: number | null;

  @Column({ name: 'business_unit_id', type: 'int', nullable: true })
  businessUnitId: number | null;

  @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;

  @ManyToOne(() => User, user => user.roles)
  @JoinColumn({ name: 'user_id' })
  user: User;
}