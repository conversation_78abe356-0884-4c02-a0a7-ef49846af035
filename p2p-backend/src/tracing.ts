import { NodeTracerProvider } from '@opentelemetry/sdk-trace-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { BatchSpanProcessor } from '@opentelemetry/sdk-trace-base';
import { resourceFromAttributes } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { registerInstrumentations } from '@opentelemetry/instrumentation';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { HttpInstrumentation } from '@opentelemetry/instrumentation-http';

export function initializeTracing(): boolean {
  try {
    const TRACING_CONFIG = {
      serviceName: process.env.SERVICE_NAME || 'p2p-backend',
      environment: process.env.ENV || 'test',
      signozUrl: "https://ingest.in.signoz.cloud:443/v1/traces",
      signozAccessToken: process.env.SIGNOZ_ACCESS_TOKEN || '',
    };

    const resource = resourceFromAttributes({
      [SemanticResourceAttributes.SERVICE_NAME]: TRACING_CONFIG.serviceName,
      [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]:
        TRACING_CONFIG.environment,
    }); 

    const exporter = new OTLPTraceExporter({
      url: TRACING_CONFIG.signozUrl,
      headers: {
        "signoz-access-token": TRACING_CONFIG.signozAccessToken,
      },
    });

    const spanProcessor = new BatchSpanProcessor(exporter, {
        maxQueueSize: 100,
        scheduledDelayMillis: 5000,
        exportTimeoutMillis: 30000,
      });
  
      const provider = new NodeTracerProvider({
        resource,
        spanProcessors: [spanProcessor],
      });
  
    provider.register();
    registerInstrumentations({
      tracerProvider: provider,
      instrumentations: [
        getNodeAutoInstrumentations(),
        new HttpInstrumentation({
          requestHook: (span, request) => {
            if (span && request) {
              if ('url' in request) { 
                const url = request.url;
                const host = request.headers?.host;
                const method = (request as any).method;
                const fullUrl = `http://${host}${url}`;
                span.updateName(`HTTP ${method} ${fullUrl}`);
              }
            }
          },
        }),
      ],
    });

    const tracer = provider.getTracer('p2p-backend-tracer');
    const testSpan = tracer.startSpan('test-initialization-span');
    testSpan.end();

    return true;
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error("Comprehensive Tracing Initialization Error:", {
        message: error.message,
        name: error.name,
        stack: error.stack,
      });
    } else {
      console.error("Unknown error during tracing initialization", error);
    }
    return false;
  }
}

// Global error tracking
process.on('uncaughtException', (error: Error) => {
  console.error('Global Error Tracking:', {
    message: error.message,
    stack: error.stack,
    name: error.name
  });
});

process.on('unhandledRejection', (reason: unknown, promise: Promise<unknown>) => {
  console.error("Unhandled Promise Rejection:", {
    reason,
    promise
  });
});
