{"name": "p2p-backend", "version": "1.0.0", "description": "Node.js TypeScript application with Fastify and MySQL", "main": "dist/index.js", "scripts": {"build": "tsc -p tsconfig.json", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "typeorm": "ts-node ./node_modules/typeorm/cli.js", "migration:run": "npm run typeorm -- migration:run -d src/db/typeorm.config.ts", "migration:revert": "npm run typeorm -- migration:revert -d src/db/typeorm.config.ts", "migration:generate": "npm run typeorm -- migration:generate -d src/db/typeorm.config.ts"}, "keywords": ["fastify", "mysql", "typescript", "node"], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.797.0", "@aws-sdk/lib-storage": "^3.797.0", "@aws-sdk/s3-request-presigner": "^3.798.0", "@fastify/cookie": "^8.3.0", "@fastify/cors": "^8.3.0", "@fastify/env": "^4.2.0", "@fastify/jwt": "^6.7.1", "@fastify/multipart": "^6.0.0", "@fastify/swagger": "^8.8.0", "@fastify/swagger-ui": "^1.9.3", "@fastify/type-provider-typebox": "^5.1.0", "@opentelemetry/auto-instrumentations-node": "^0.58.1", "@opentelemetry/auto-instrumentations-web": "^0.46.0", "@opentelemetry/context-zone": "^2.0.0", "@opentelemetry/exporter-trace-otlp-http": "^0.200.0", "@opentelemetry/instrumentation": "^0.200.0", "@opentelemetry/instrumentation-http": "^0.201.0", "@opentelemetry/instrumentation-xml-http-request": "^0.200.0", "@opentelemetry/resources": "^2.0.0", "@opentelemetry/sdk-trace-base": "^2.0.0", "@opentelemetry/sdk-trace-node": "^2.0.1", "@opentelemetry/sdk-trace-web": "^2.0.0", "@opentelemetry/semantic-conventions": "^1.30.0", "@sentry/node": "^9.22.0", "@sinclair/typebox": "^0.34.33", "@types/nodemailer": "^6.4.17", "axios": "^1.9.0", "bcrypt": "^5.1.1", "dotenv": "^16.3.1", "fastify": "^4.21.0", "fastify-plugin": "^5.0.1", "jsonwebtoken": "^9.0.2", "lru-cache": "^11.1.0", "mysql2": "^3.14.0", "nodemailer": "^7.0.3", "reflect-metadata": "^0.2.2", "sequelize": "^6.37.7", "typeorm": "^0.3.22", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/csv-parse": "^1.2.5", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.5.0", "fp-ts": "^2.16.10", "jest": "^29.6.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.1.6"}}