FROM node:18-alpine

WORKDIR /app

# Copy package files and install dependencies
COPY package*.json ./
# Install bcryptjs instead of bcrypt to avoid architecture issues
RUN npm uninstall bcrypt --no-save || true && \
    npm install bcryptjs --save && \
    npm install

# Copy the rest of the application code
COPY . .

# Patch any code that uses bcrypt to use bcryptjs instead
RUN find ./src -type f -name "*.ts" -exec sed -i "s/from .\/bcrypt./from 'bcryptjs'/g" {} \; || true && \
    find ./src -type f -name "*.ts" -exec sed -i "s/require(.\/bcrypt.)/require('bcryptjs')/g" {} \; || true && \
    find ./src -type f -name "*.ts" -exec sed -i "s/from 'bcrypt'/from 'bcryptjs'/g" {} \; || true && \
    find ./src -type f -name "*.ts" -exec sed -i "s/require('bcrypt')/require('bcryptjs')/g" {} \; || true

# # Run migrations
# RUN echo "Running database migrations..."
# RUN npm run migration:run
# Run migrations
# RUN echo "Migrations successfully completed!!"

# Set environment variables
ENV NODE_ENV=development
ENV PORT=3000
ENV HOST=0.0.0.0

# Expose the API port
EXPOSE 3001

# Command to run the application in development mode with hot reloading
CMD ["npm", "run", "dev"] 