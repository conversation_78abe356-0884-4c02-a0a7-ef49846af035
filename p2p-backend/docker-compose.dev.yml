services:
  p2p-backend:
    build:
      context: .
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "${PORT:-5100}:${PORT:-5100}"
    env_file:
      - .env
    environment:
      - ENV=development
      - NODE_ENV=development
      - PORT=${PORT:-5100}
      - HOST=${HOST:-0.0.0.0}
      - MYSQL_HOST=host.docker.internal
      - MYSQL_PORT=${MYSQL_PORT:-3306}
      - MYSQL_USER=${MYSQL_USER:-root}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DATABASE=${MYSQL_DATABASE}
      - JWT_SECRET=${JWT_SECRET}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_REGION=${AWS_REGION}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_BUCKET_NAME=${AWS_BUCKET_NAME}
      - RUN_MIGRATIONS=${RUN_MIGRATIONS:-true}
      - VELYNK_TECH_USER_USERNAME=${VELYNK_TECH_USER_USERNAME}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: ["sh", "/app/docker-entrypoint.sh"]
