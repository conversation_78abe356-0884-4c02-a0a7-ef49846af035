#!/bin/bash

# Add the import to route files
for file in src/routes/admin/business-units.routes.ts src/routes/admin/cost-centers.routes.ts \
            src/routes/admin/user-roles.routes.ts src/routes/admin/users.routes.ts \
            src/routes/pr-timeline.routes.ts src/routes/purchase-order.routes.ts \
            src/routes/purchase-request.routes.ts src/routes/quotation.routes.ts; do
  if [ -f "$file" ]; then
    # Add import statement for asRouteHandler if it doesn't exist
    if ! grep -q "import { asRouteHandler } from '../../types/route-helper';" "$file"; then
      if grep -q "import.*from.*'fastify';" "$file"; then
        sed -i '' -e "/import.*from.*'fastify';/a\\
import { asRouteHandler } from '../../types/route-helper';" "$file"
      fi
    fi

    # Fix handler calls by wrapping them with asRouteHandler
    sed -i '' -e 's/\(handler: \)\([a-zA-Z0-9.]*\)/\1asRouteHandler(\2)/g' "$file"
  fi
done

echo "Route files updated with asRouteHandler."
