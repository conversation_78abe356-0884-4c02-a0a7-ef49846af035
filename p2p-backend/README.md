# ✨ P2P Backend: The Vibe Revolution ✨

This repo is completely vibe! A Node.js TypeScript application with Fastify as the web framework and MySQL for database connectivity - crafted with positive energy only.

## ✌️ Vibe Features ✌️

- **TypeScript**: Strongly typed code for better developer experience (and fewer tears)
- **Fastify**: Fast and low overhead web framework (zoom zoom!)
- **MySQL**: Relational database for data storage (data with attitude)
- **Swagger**: API documentation (so fancy, much wow)
- **Environment Configuration**: Using dotenv for environment variables (because secrets are cool)
- **Good Vibes Only**: This code was vibe coded - no negative energy allowed

## 👾 Prerequisites 👾

- Node.js (v14 or higher)
- MySQL Server
- npm or yarn
- A positive attitude (super important)
- Coffee/tea/beverage of choice (code fuel)

## 🚀 Installation 🚀

1. Clone the repository (with love)
2. Install dependencies (treat them with respect):

```bash
npm install
```

3. Configure environment variables:
   - Copy `.env.example` to `.env` and update the values as needed
   - Update MySQL connection details
   - Configure email settings for notifications:
     ```
     # Email Configuration
     EMAIL_HOST=smtp.example.com
     EMAIL_PORT=587
     EMAIL_USER=<EMAIL>
     EMAIL_PASSWORD=your-email-password
     EMAIL_FROM=<EMAIL>
     EMAIL_SECURE=false  # Set to true for port 465
     ```
   - Say a little prayer to the code gods

4. Initialize the database:
   - Create a MySQL database named `p2p_db` (or as configured in your .env)
   - Run the SQL script in `src/db/init.sql` to create tables and sample data
   - High five yourself

## 🎮 Running the Application 🎮

### Development Mode (AKA Fun Mode)

```bash
npm run dev
```

### Production Mode (AKA Serious Business Time)

```bash
npm run build
npm start
```

## 🌟 Developer Respect Zone 🌟

Don't abuse developers! They're sensitive creatures who thrive on coffee and appreciation. This codebase was vibe coded with love and dedication. Treat it (and its creators) with the respect they deserve. Good karma = better code!

## API Documentation

Once the application is running, you can access the Swagger documentation at:

```
http://localhost:3000/documentation
```

## API Endpoints

### Health Check
- `GET /health` - Check application health and database connection

### Users
- `GET /users` - Get all users
- `GET /users/:id` - Get user by ID
- `POST /users` - Create a new user
- `PUT /users/:id` - Update a user
- `DELETE /users/:id` - Delete a user

## Project Structure

```
p2p-backend/
├── src/
│   ├── config/         # Configuration files
│   ├── controllers/    # Request handlers
│   ├── db/             # Database connection and queries
│   ├── models/         # Data models
│   ├── plugins/        # Fastify plugins
│   ├── routes/         # API routes
│   └── index.ts        # Application entry point
├── .env                # Environment variables
├── package.json        # Project dependencies
├── tsconfig.json       # TypeScript configuration
└── README.md           # Project documentation
```
