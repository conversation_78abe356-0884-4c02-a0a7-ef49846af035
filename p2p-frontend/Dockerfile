# Build docker image
FROM node:22.15.0 as build-frontend
# Make the 'app' folder the current working directory
WORKDIR /app
# copy project files and folders to the current working directory (i.e. 'app' folder)
COPY package.json .
# install project dependencies
RUN yarn
# copy project files and folders   
COPY . .
# setting up environment variables
ENV REACT_APP_ENVIRONMENT=production

# build app for production with minification
RUN yarn build
# Copy the react app build above in nginx
FROM nginx:alpine
# copy dist for build-frontend to nginx
COPY --from=build-frontend /app/build /usr/share/nginx/html
# export port
EXPOSE 3000
# copy nginx conf file
COPY nginx/default.conf /etc/nginx/conf.d/default.conf
# Containers run nginx with global directives and daemon off
CMD ["nginx", "-g", "daemon off;"]