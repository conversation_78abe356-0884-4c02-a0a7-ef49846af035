import axios from 'axios';
import apiConfig from '../config/api.config';
import { getUserData } from '../utils/localStorage';




const axiosInstance = axios.create({
  baseURL: apiConfig.baseUrl,
  timeout: 10000
  // Don't set a default Content-Type here
  // Let axios set the appropriate Content-Type based on the request data
});

// Add request interceptor for authentication
axiosInstance.interceptors.request.use(
  (config) => {
    config.headers['ngrok-skip-browser-warning'] = 'true';
    // Add the ngrok warning header
    const userData = getUserData();

    if (userData?.token) {
      config.headers.Authorization = `Bearer ${userData.token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default axiosInstance;
