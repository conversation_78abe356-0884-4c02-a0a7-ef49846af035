import axiosInstance from './axiosInstance';
import apiConfig from '../config/api.config';
import velynkServiceInstance from './velynkServiceInstance';
/**
 * API Service for the P2P Expense Management System
 */
const apiService = {
  // Approve an invoice
  async approveInvoice(invoiceId, remarks) {
    const url = apiConfig.endpoints.invoices.detail(invoiceId) + '/approve';
    const response = await axiosInstance.post(url, { remarks, status: 'APPROVED' });
    return response.data;
  },
  // Reject an invoice
  async rejectInvoice(invoiceId, remarks) {
    const url = apiConfig.endpoints.invoices.detail(invoiceId) + '/reject';
    const response = await axiosInstance.post(url, { remarks, status: 'REJECTED' });
    return response.data;
  },

  async updateInvoiceStatus(invoiceId, status, remarks) {
    const url = apiConfig.endpoints.invoices.detail(invoiceId) + '/status';
    const response = await axiosInstance.put(url, { remarks, status });
    return response.data;
  },

  async deleteInvoice(invoiceId) {
    const url = apiConfig.endpoints.invoices.delete(invoiceId);
    const response = await axiosInstance.delete(url);
    return response.data;
  },

  async deleteGRN(grnId) {
    const url = apiConfig.endpoints.grns.delete(grnId);
    const response = await axiosInstance.delete(url);
    return response.data;
  },

  // Business Finance approval for an invoice
  async bizFinApproveInvoice(invoiceId, remarks) {
    try {
      // Use the function directly with the invoiceId parameter
      const url = apiConfig.endpoints.invoices.bizFinApproval(invoiceId);
      console.log('Business Finance approval URL:', url);
      
      const response = await axiosInstance.post(url, { 
        remarks,
        status: 'APPROVED'
      });
      return response.data;
    } catch (error) {
      console.error('Error in Business Finance approval for invoice:', error);
      throw error;
    }
  },

  // Finance approval for an invoice
  async finApproveInvoice(invoiceId, remarks) {
    try {
      // Use the function directly with the invoiceId parameter
      const url = apiConfig.endpoints.invoices.finApproval(invoiceId);
      console.log('Finance approval URL:', url);
      
      const response = await axiosInstance.post(url, { 
        remarks,
        status: 'APPROVED'
      });
      return response.data;
    } catch (error) {
      console.error('Error in Finance approval for invoice:', error);
      throw error;
    }
  },
  // Fetch invoice details by ID
  getInvoiceById(id) {
    return axiosInstance.get(apiConfig.endpoints.invoices.detail(id));
  },
  // Fetch approval history for an invoice
  getInvoiceApprovalHistory(id) {
    return axiosInstance.get(`${apiConfig.endpoints.invoices.detail(id)}/history`);
  },
  // Fetch all invoices for listing with pagination support
  // @param {Object} params - Pagination parameters (page, limit)
  // @returns {Promise} Promise with paginated invoices data
  getInvoices(params = {}) {
    return axiosInstance.get(apiConfig.endpoints.invoices.list, { params });
  },
  // Create a new purchase request
  // @param {Object} purchaseRequest - Purchase request data
  // @returns {Promise} Promise with created purchase request
  createPurchaseRequest(param) {
    return axiosInstance.post(apiConfig.endpoints.purchaseRequests.list, param);
  },

  getPurchaseRequests(param) {
    return axiosInstance.get(apiConfig.endpoints.purchaseRequests.list);
  },

  // Get purchase request status counts
  // @returns {Promise} Promise with status counts
  getPurchaseRequestStatusCounts() {
    return axiosInstance.get(apiConfig.endpoints.purchaseRequests.statusCounts);
  },

  // Get all vendors/partners using the new endpoint
  // @param {Object} params - Optional filter parameters
  // @returns {Promise} Promise with all vendor/partner data
  getVendors(params = {}) {
    // The new endpoint uses POST instead of GET and requires a specific format
    return axiosInstance.post(apiConfig.endpoints.vendors.list, params);
  },
  
  // Get detailed partner/vendor information for a specific partner
  // @param {Object} params - Must include partnerId
  // @returns {Promise} Promise with partner details including verification status and remarks
  getPartnerDetails(params) {
    if (!params.partnerId) {
      console.warn('getPartnerDetails called without partnerId');
    }
    return velynkServiceInstance.get(apiConfig.velynkUrl + apiConfig.endpoints.partners.getPartnerDetails, { params });
  },

  // Approve a purchase request (standard approval)
  // @param {string} prId - Purchase request ID
  // @param {string} remarks - Approval remarks
  // @returns {Promise} Promise with approval result
  async approvePurchaseRequest(prId, remarks) {
    try {
      const url = apiConfig.endpoints.purchaseRequests.approve(prId);
      console.log('Standard approval URL:', url);
      
      const response = await axiosInstance.post(url, { 
        remarks,
        status: 'APPROVED'
      });
      return response.data;
    } catch (error) {
      console.error('Error approving purchase request:', error);
      throw error;
    }
  },
  
  // CC Head approval for a purchase request
  // @param {string} prId - Purchase request ID
  // @param {string} remarks - Approval remarks
  // @returns {Promise} Promise with approval result
  async ccHeadApprovePurchaseRequest(prId, remarks) {
    try {
      const url = apiConfig.endpoints.purchaseRequests.ccHeadApproval(prId);
      console.log('CC Head approval URL:', url);
      
      const response = await axiosInstance.post(url, { 
        remarks,
        status: 'APPROVED'
      });
      return response.data;
    } catch (error) {
      console.error('Error in CC Head approval for purchase request:', error);
      throw error;
    }
  },
  
  // Business Finance approval for a purchase request
  // @param {string} prId - Purchase request ID
  // @param {string} remarks - Approval remarks
  // @returns {Promise} Promise with approval result
  async bizFinApprovePurchaseRequest(prId, remarks) {
    try {
      const url = apiConfig.endpoints.purchaseRequests.bizFinApproval(prId);
      console.log('Business Finance approval URL:', url);
      
      const response = await axiosInstance.post(url, { 
        remarks,
        status: 'APPROVED'
      });
      return response.data;
    } catch (error) {
      console.error('Error in Business Finance approval for purchase request:', error);
      throw error;
    }
  },
  
  // Procurement Manager approval for a purchase request
  // @param {string} prId - Purchase request ID
  // @param {string} remarks - Approval remarks
  // @returns {Promise} Promise with approval result
  async procurementManagerApprovePurchaseRequest(prId, remarks) {
    try {
      const url = apiConfig.endpoints.purchaseRequests.procurementManagerApproval(prId);
      console.log('Procurement Manager approval URL:', url);
      
      const response = await axiosInstance.post(url, { 
        remarks,
        status: 'APPROVED'
      });
      return response.data;
    } catch (error) {
      console.error('Error in Procurement Manager approval for purchase request:', error);
      throw error;
    }
  },

  // Reject a purchase request
  // @param {string} prId - Purchase request ID
  // @param {string} remarks - Rejection remarks
  // @returns {Promise} Promise with rejection result
  async rejectPurchaseRequest(prId, remarks) {
    try {
      const url = apiConfig.endpoints.purchaseRequests.reject(prId);
      console.log('Rejection URL:', url);
      
      const response = await axiosInstance.put(url, { 
        remarks,
        status: 'REJECTED'
      });
      return response.data;
    } catch (error) {
      console.error('Error rejecting purchase request:', error);
      throw error;
    }
  },

  // Get purchase request details by ID
  // @param {string} prId - Purchase request ID
  // @returns {Promise} Promise with purchase request details
  async getPurchaseRequestDetails(prId) {
    try {
      const response = await axiosInstance.get(apiConfig.endpoints.purchaseRequests.detail(prId));
      return response.data;
    } catch (error) {
      console.error('Error fetching PR details:', error);
      throw error;
    }
  },

  // Get purchase request timeline
  // @param {string} prId - Purchase request ID
  // @returns {Promise} Promise with purchase request timeline
  async getPurchaseRequestTimeline(prId) {
    try {
      const response = await axiosInstance.get(apiConfig.endpoints.purchaseRequests.timeline(prId));
      return response.data;
    } catch (error) {
      console.error('Error fetching PR timeline:', error);
      throw error;
    }
  },

  // Get dashboard statistics
  // @returns {Promise} Promise with dashboard statistics
  async getDashboardStats() {
    try {
      // TODO: Replace with actual API call when endpoint is available
      // return axiosInstance.get(apiConfig.endpoints.dashboard.stats);
      
      // Mock data for dashboard statistics
      return {
        myPRs: 12,
        myPOs: 5,
        pendingApprovals: 8,
        totalRequests: 25
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return {
        myPRs: 0,
        myPOs: 0,
        pendingApprovals: 0,
        totalRequests: 0
      };
    }
  },
  
  // Get recent activities for dashboard
  // @returns {Promise} Promise with recent activities data
  async getRecentActivities() {
    try {
      // TODO: Replace with actual API call when endpoint is available
      // return axiosInstance.get(apiConfig.endpoints.dashboard.activities);
      
      // Mock data for recent activities
      return [
        {
          id: 1001,
          type: 'PR',
          title: 'Office Supplies',
          description: 'Purchase request for office supplies has been submitted',
          status: 'SUBMITTED',
          timestamp: '10 minutes ago',
          businessUnit: 'Engineering'
        },
        {
          id: 1002,
          type: 'PR',
          title: 'Laptop Peripherals',
          description: 'Purchase request for laptop peripherals is awaiting your approval',
          status: 'PENDING',
          timestamp: '2 hours ago',
          businessUnit: 'Engineering'
        },
        {
          id: 1003,
          type: 'PR',
          title: 'Conference Room Equipment',
          description: 'Your purchase request has been approved',
          status: 'APPROVED',
          timestamp: '1 day ago',
          businessUnit: 'Operations'
        },
        {
          id: 1004,
          type: 'PO',
          title: 'Software Licenses',
          description: 'Purchase order for software licenses has been created',
          status: 'CREATED',
          timestamp: '2 days ago',
          businessUnit: 'IT'
        },
        {
          id: 1005,
          type: 'PO',
          title: 'Office Furniture',
          description: 'Purchase order for office furniture has been approved',
          status: 'APPROVED',
          timestamp: '3 days ago',
          businessUnit: 'Corporate'
        }
      ];
    } catch (error) {
      console.error('Error fetching recent activities:', error);
      return [];
    }
  },

  // Quotations
  quotationService: {
    // Get all quotations
    // @returns {Promise} Promise with all quotations
    getAllQuotations: async () => {
      try {
        const response = await axiosInstance.get(apiConfig.endpoints.quotations.list);
        return response.data;
      } catch (error) {
        console.error('Error fetching all quotations:', error);
        throw error;
      }
    },

    // Get quotations for a specific purchase request
    // @param {number} prId - Purchase request ID
    // @returns {Promise} Promise with quotations for the PR
    getQuotationsForPR: async (prId) => {
      try {
        const response = await axiosInstance.get(apiConfig.endpoints.purchaseRequests.quotations(prId));
        return response.data;
      } catch (error) {
        console.error(`Error fetching quotations for PR ${prId}:`, error);
        throw error;
      }
    },

    // Get quotation details
    // @param {number} id - Quotation ID
    // @returns {Promise} Promise with quotation details
    getQuotationDetails: async (id) => {
      try {
        const response = await axiosInstance.get(apiConfig.endpoints.quotations.detail(id));
        return response.data;
      } catch (error) {
        console.error(`Error fetching quotation ${id}:`, error);
        throw error;
      }
    },

    // Create a new quotation
    // @param {Object} quotationData - Quotation data
    // @returns {Promise} Promise with created quotation
    createQuotation: async (quotationData) => {
      try {
        const response = await axiosInstance.post(apiConfig.endpoints.quotations.create, quotationData);
        return response.data;
      } catch (error) {
        console.error('Error creating quotation:', error);
        throw error;
      }
    },

    // Update an existing quotation
    // @param {number} id - Quotation ID
    // @param {Object} quotationData - Updated quotation data
    // @returns {Promise} Promise with updated quotation
    updateQuotation: async (id, quotationData) => {
      try {
        const response = await axiosInstance.put(apiConfig.endpoints.quotations.update(id), quotationData);
        return response.data;
      } catch (error) {
        console.error(`Error updating quotation ${id}:`, error);
        throw error;
      }
    },

    // Delete a quotation
    // @param {number} id - Quotation ID
    // @returns {Promise} Promise with deletion result
    deleteQuotation: async (id) => {
      try {
        const response = await axiosInstance.delete(apiConfig.endpoints.quotations.delete(id));
        return response.data;
      } catch (error) {
        console.error(`Error deleting quotation ${id}:`, error);
        throw error;
      }
    },

    // Approve a quotation
    // @param {number} id - Quotation ID
    // @param {Object} approvalData - Approval data (status, remarks)
    // @returns {Promise} Promise with approval result
    approveQuotation: async (id, approvalData) => {
      try {
        const response = await axiosInstance.post(apiConfig.endpoints.quotations.approve(id), approvalData);
        return response.data;
      } catch (error) {
        console.error(`Error approving quotation ${id}:`, error);
        throw error;
      }
    }
  },

  // Item Categories
  itemCategoryService: {
    // Get all item categories
    // @returns {Promise} Promise with all item categories
    getAllItemCategories: async (params = {}) => {
      try {
        const response = await axiosInstance.get(apiConfig.endpoints.itemCategories.list, { params });
        return response.data;
      } catch (error) {
        console.error('Error fetching item categories:', error);
        throw error;
      }
    },

    // Get active item categories
    // @returns {Promise} Promise with active item categories
    getActiveItemCategories: async () => {
      try {
        const response = await axiosInstance.get(apiConfig.endpoints.itemCategories.list, { 
          params: { isActive: true } 
        });
        return response.data;
      } catch (error) {
        console.error('Error fetching active item categories:', error);
        throw error;
      }
    },

    // Get item category by ID
    // @param {number} id - Item category ID
    // @returns {Promise} Promise with item category details
    getItemCategoryById: async (id) => {
      try {
        const response = await axiosInstance.get(apiConfig.endpoints.itemCategories.detail(id));
        return response.data;
      } catch (error) {
        console.error(`Error fetching item category ${id}:`, error);
        throw error;
      }
    },

    // Create a new item category
    // @param {Object} itemCategoryData - Item category data
    // @returns {Promise} Promise with created item category
    createItemCategory: async (itemCategoryData) => {
      try {
        const response = await axiosInstance.post(apiConfig.endpoints.itemCategories.create, itemCategoryData);
        return response.data;
      } catch (error) {
        console.error('Error creating item category:', error);
        throw error;
      }
    },

    // Update an existing item category
    // @param {number} id - Item category ID
    // @param {Object} itemCategoryData - Updated item category data
    // @returns {Promise} Promise with updated item category
    updateItemCategory: async (id, itemCategoryData) => {
      try {
        const response = await axiosInstance.put(apiConfig.endpoints.itemCategories.update(id), itemCategoryData);
        return response.data;
      } catch (error) {
        console.error(`Error updating item category ${id}:`, error);
        throw error;
      }
    },

    // Delete an item category
    // @param {number} id - Item category ID
    // @returns {Promise} Promise with deletion result
    deleteItemCategory: async (id) => {
      try {
        const response = await axiosInstance.delete(apiConfig.endpoints.itemCategories.delete(id));
        return response.data;
      } catch (error) {
        console.error(`Error deleting item category ${id}:`, error);
        throw error;
      }
    }
  },

  // Purchase Orders
  purchaseOrderService: {
    // Get purchase order status counts
    // @returns {Promise} Promise with status counts
    getPurchaseOrderStatusCounts() {
      return axiosInstance.get(apiConfig.endpoints.purchaseOrders.statusCounts);
    },
    // Get single GRN details by ID
    getGRNDetails(grnId) {
      return axiosInstance.get(`/grns/${grnId}`);
    },
    getPurchaseOrders: async (filters) => {
      try {
        const response = await axiosInstance.get(apiConfig.endpoints.purchaseOrders.list, { params: filters });
        return response.data;
      } catch (error) {
        console.error('Error fetching purchase orders:', error);
        throw error;
      }
    },
    
    getPurchaseOrderDetails: async (id) => {
      try {
        const response = await axiosInstance.get(apiConfig.endpoints.purchaseOrders.detail(id));
        
        // Check if the response has data
        if (!response || !response.data) {
          throw new Error('Purchase order not found');
        }
        
        return response;
      } catch (error) {
        console.error(`Error fetching purchase order ${id}:`, error);
        
        // If it's a 404 error specifically for "not found"
        if (error.response && error.response.status === 404) {
          throw new Error('Purchase order not found');
        }
        
        throw error;
      }
    },
    
    createPurchaseOrder: async (poData) => {
      try {
        const response = await axiosInstance.post(apiConfig.endpoints.purchaseOrders.create, poData);
        return response.data;
      } catch (error) {
        console.error('Error creating purchase order:', error);
        throw error;
      }
    },
    
    updatePurchaseOrder: async (id, poData) => {
      try {
        const response = await axiosInstance.put(apiConfig.endpoints.purchaseOrders.update(id), poData);
        return response.data;
      } catch (error) {
        console.error(`Error updating purchase order ${id}:`, error);
        throw error;
      }
    },
    
    submitGoodsReceipt: async (grnData) => {
      try {
        const response = await axiosInstance.post(apiConfig.endpoints.purchaseOrders.submitGRN, grnData);
        return response.data;
      } catch (error) {
        console.error('Error submitting goods receipt:', error);
        throw error;
      }
    },
    
    // Get all GRNs for a purchase order
    // @param {string} poId - Purchase order ID
    // @returns {Promise} Promise with GRNs for the purchase order
    getGRNsForPO: async (poId) => {
      try {
        const response = await axiosInstance.get(`/purchase-orders/${poId}/grns`);
        return response.data;
      } catch (error) {
        console.error(`Error fetching GRNs for PO ${poId}:`, error);
        throw error;
      }
    },
    
    // Create a new GRN without invoice
    // @param {Object} grnData - GRN data with poId and grnItems
    // @returns {Promise} Promise with creation result
    createGRN: async (grnData) => {
      try {
        const response = await axiosInstance.post('/grns', grnData);
        return response.data;
      } catch (error) {
        console.error('Error creating GRN:', error);
        throw error;
      }
    },
    
    // Verify a quote on the purchase order
    // @param {string} poId - Purchase order ID
    // @param {Object} verificationData - Data with action and remarks
    // @returns {Promise} Promise with verification result
    verifyQuote: async (poId, verificationData) => {
      try {
        const url = apiConfig.endpoints.purchaseOrders.verifyQuote(poId);
        console.log('PO Quote verification URL:', url);
        console.log('PO Quote verification payload:', verificationData);
        
        const response = await axiosInstance.put(url, verificationData);
        return response.data;
      } catch (error) {
        console.error(`Error verifying quote for PO ${poId}:`, error);
        throw error;
      }
    },
    
    // Get purchase order timeline
    // @param {string} poId - Purchase order ID
    // @returns {Promise} Promise with purchase order timeline
    getPurchaseOrderTimeline: async (poId) => {
      try {
        const response = await axiosInstance.get(apiConfig.endpoints.purchaseOrders.timeline(poId));
        return response.data;
      } catch (error) {
        console.error(`Error fetching timeline for PO ${poId}:`, error);
        throw error;
      }
    },
    
    // Add invoice to GRN
    // @param {Object} data - Invoice data including fileKeys for uploaded files
    // Format example:
    // {
    //   "grnId": 2,
    //   "invoiceNumber": "INV-2025-001",
    //   "invoiceDate": "2025-04-29",
    //   "invoiceValue": 5000,
    //   "remarks": "Standard delivery invoice",
    //   "invoiceItems": [
    //     { "grnItemId": 7, "amount": 3000, "tax": 300, "quantity": 90 },
    //     { "grnItemId": 8, "amount": 2000, "tax": 200, "quantity": 90 }
    //   ],
    //   "fileKeys": ["3-17459169d576-afsbc6796391335c.jpeg", "5-174591f6918826-sac2b7fa3781c2b05.jpeg"]
    // }
    // @returns {Promise} Promise with creation result
    addInvoiceToGRN: async (invoiceData) => {
      try {
        // Validate required fields
        if (!invoiceData.grnId) {
          throw new Error('GRN ID is required');
        }
        
        if (!invoiceData.invoiceNumber) {
          throw new Error('Invoice number is required');
        }
        
        if (!invoiceData.invoiceDate) {
          throw new Error('Invoice date is required');
        }
        
        if (!invoiceData.invoiceValue || isNaN(invoiceData.invoiceValue)) {
          throw new Error('Valid invoice value is required');
        }
        
        if (!Array.isArray(invoiceData.invoiceItems) || invoiceData.invoiceItems.length === 0) {
          throw new Error('At least one invoice item is required');
        }
        
        // Validate each item has the required fields
        for (const item of invoiceData.invoiceItems) {
          if (!item.grnItemId) {
            throw new Error('GRN Item ID is required for each invoice item');
          }
          if (isNaN(item.amount)) {
            throw new Error('Valid amount is required for each invoice item');
          }
          if (isNaN(item.tax)) {
            throw new Error('Valid tax amount is required for each invoice item');
          }
          if (isNaN(item.quantity)) {
            throw new Error('Valid quantity is required for each invoice item');
          }
        }
        
        // Make the API call with the validated data
        console.log('Sending invoice data:', invoiceData);
        const response = await axiosInstance.post(apiConfig.endpoints.invoices.create, invoiceData);
        
        return {
          success: true,
          data: response.data
        };
      } catch (error) {
        console.error('Error adding invoice to GRN:', error);
        return {
          success: false,
          error: error.message || error.response?.data?.message || 'Failed to add invoice to GRN'
        };
      }
    }

  },
  createPurchaseOrder: (data) => apiService.purchaseOrderService.createPurchaseOrder(data),

  // Create GRN without invoice
  // @param {Object} payload - GRN data with poId and grnItems
  // @returns {Promise} Promise with creation result
  createGRN: async (payload) => {
    return apiService.purchaseOrderService.createGRN(payload);
  },

  // Create GRN with Invoice
  // @param {Object} payload - GRN and invoice data
  // @param {File} invoiceFile - Optional invoice file to upload
  // @returns {Promise} Promise with creation result
  createGRNWithInvoice: async (payload, invoiceFile) => {
    try {
      // First create the GRN and invoice record
      const response = await axiosInstance.post(apiConfig.endpoints.invoices.createWithGRN, payload);
      
      // If there's a file to upload and the initial request was successful
      if (invoiceFile && response.data && response.data.invoiceId) {
        const invoiceId = response.data.invoiceId;
        
        // Create form data for file upload
        const formData = new FormData();
        formData.append('file', invoiceFile);
        
        // Upload the invoice file
        await axiosInstance.post(
          apiConfig.endpoints.invoices.uploadFile(invoiceId), 
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }
        );
      }
      
      return response.data;
    } catch (error) {
      console.error('Error creating GRN with invoice:', error);
      throw error;
    }
  },
  
  // Update purchase order status
  // @param {number} poId - Purchase order ID
  // @param {string} status - New status (e.g., 'Closed', 'Open')
  // @param {string} remarks - Optional remarks for status change
  // @returns {Promise} Promise with update result
  updatePOStatus: async (poId, status, remarks = '') => {
    try {
      const response = await axiosInstance.put(
        apiConfig.endpoints.purchaseOrders.updateStatus(poId),
        { status, remarks }
      );
      return response.data;
    } catch (error) {
      console.error(`Error updating PO status for PO ${poId}:`, error);
      throw error;
    }
  },
  
  // Fetch available locations for billing/shipping
  // @returns {Promise} Promise with locations data
  fetchLocations: async () => {
    try {
      // This is a mock implementation. Replace with actual API endpoint when available
      // For now, we'll return static data for demonstration purposes
      return {
        data: [
          { id: 'HQ', name: 'Headquarters' },
          { id: 'WAREHOUSE1', name: 'Warehouse 1' },
          { id: 'WAREHOUSE2', name: 'Warehouse 2' },
          { id: 'OFFICE1', name: 'Office 1' },
          { id: 'OFFICE2', name: 'Office 2' }
        ]
      };
      
      // Uncomment when actual API endpoint is available
      // const response = await axiosInstance.get(apiConfig.endpoints.locations.list);
      // return response;
    } catch (error) {
      console.error('Error fetching locations:', error);
      throw error;
    }
  },
};

export default apiService;
