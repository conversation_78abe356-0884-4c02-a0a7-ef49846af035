import apiConfig from "../config/api.config";
import {
    TECH_USER_USERNAME,
    TECH_USER_PASSWORD,
    DEFAULT_EXPIRY_DURATION_IN_MS,
  } from "../constants/constants";
  
  // In-memory cache
  let cachedToken = null;
  let tokenExpiry = null;
  
export async function getAuthToken() {
    const now = Date.now();

    if (cachedToken && tokenExpiry && now < tokenExpiry) {
        console.log("Using cached token");
        return cachedToken;
    }

    console.log("Fetching new token...");
    try {
        const response = await fetch(apiConfig.velynkUrl + apiConfig.endpoints.velynkAuth.login, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
        },
        body: JSON.stringify({
            user: {
            username: TECH_USER_USERNAME,
            password: TECH_USER_PASSWORD,
            },
        }),
        });

        if (!response.ok) {
        throw new Error("Login failed");
        }

        const authToken = response.headers.get("Authorization");
        if (!authToken) {
        throw new Error("No auth token received");
        }

        // Store token and set expiry
        cachedToken = authToken;
        tokenExpiry = now + DEFAULT_EXPIRY_DURATION_IN_MS;

        return cachedToken;
    } catch (error) {
        console.error("Error fetching token:", error);
        throw error;
    }
}
