import apiConfig from '../config/api.config';
import velynkServiceInstance from './velynkServiceInstance';

/**
 * Service for vendor-related API calls
 */
const vendorService = {
  /**
   * Get list of vendors/partners using the new partners/get_partner_details endpoint
   * @param {Object} params - Optional query parameters including limit, offset
   * @returns {Promise} - Promise resolving to partner data with GST, PAN, and bank details
   */
  getVendors: async (params = {}) => {
    try {
      // Add default params for initial load
      const defaultParams = {
        limit: 10,
        offset: 0,
        ...params
      };
      
      console.log('Fetching partners with params:', defaultParams);
      
      // Use the new partners endpoint as a GET request with query parameters
      const response = await velynkServiceInstance.get(apiConfig.endpoints.partners.getPartners, { 
        params: defaultParams
      });
      
      console.log('Partner response received:', response);
      
      // Format the response to maintain backward compatibility
      if (response && response.items) {
        // Based on the new response structure which has items at the top level
        const partners = response.items || [];
        
        console.log('Processing partners:', partners);
        
        // Map to standard format expected by components
        const formattedPartners = partners.map(partner => ({
          id: partner.id,
          name: partner.name || 'Unknown Partner',
          vendorName: partner.name || 'Unknown Partner',
          partnerName: partner.name || 'Unknown Partner',
          // Phone details
          phone: partner.phone_number,
          phoneNumber: partner.phone_number,
          whatsappNumber: partner.whatsapp_number,
          // Location details
          location: partner.location,
          area: partner.area,
          address: partner.location?.full_address,
          // Bank details from bank_detail_attributes
          bankDetails: partner.bank_detail_attributes || {},
          accountNumber: partner.bank_detail_attributes?.account_number,
          ifscCode: partner.bank_detail_attributes?.ifsc,
          bankName: partner.bank_detail_attributes?.ext_bank_verification?.data?.bankName,
          accountHolderName: partner.bank_detail_attributes?.beneficiary_name,
          // Verification status
          bankVerified: partner.bank_detail_attributes?.status === 'Verified',
          is_bank_verified: partner.bank_detail_attributes?.status === 'Verified',
          // KYC documents
          kycDocs: partner.kyc_docs_attributes || [],
          // Additional fields
          roleNames: partner.role_names,
          is_active: true,
          // Keep original data for reference
          originalData: partner
        }));
        
        console.log('Formatted partners:', formattedPartners);
        
        return { data: { items: formattedPartners } };
      }
      
      // Default empty response
      return { data: { items: [] } };
    } catch (error) {
      console.error('Error fetching partners:', error);
      // Return a standardized empty response object
      return { data: { items: [] } };
    }
  },
  
  /**
   * Get vendor details by ID
   * @param {string} id - Vendor/Partner ID
   * @returns {Promise} - Promise resolving to vendor details with GST, PAN, and bank details
   */
  getVendorById: async (id) => {
    try {
      // Query for specific vendor by ID using the partners endpoint with GET request
      const response = await velynkServiceInstance.get(apiConfig.endpoints.partners.getPartnerDetails, {
        params: {
          id: id,
          limit: 1,
          offset: 0
        }
      });
      
      console.log(`Partner details response for ID ${id}:`, response);
      
      // Process the response based on the new structure
      if (response && response.data && response.data.items && response.data.items.length > 0) {
        const partner = response.data.items[0];
        
        if (partner) {
          return {
            id: partner.id,
            name: partner.name || 'Unknown Partner',
            vendorName: partner.name || 'Unknown Partner',
            partnerName: partner.name || 'Unknown Partner',
            // Phone details
            phone: partner.phone_number,
            phoneNumber: partner.phone_number,
            whatsappNumber: partner.whatsapp_number,
            // Location details
            location: partner.location,
            area: partner.area,
            address: partner.location?.full_address,
            // Bank details from bank_detail_attributes
            bankDetails: partner.bank_detail_attributes || {},
            accountNumber: partner.bank_detail_attributes?.account_number,
            ifscCode: partner.bank_detail_attributes?.ifsc,
            bankName: partner.bank_detail_attributes?.ext_bank_verification?.data?.bankName,
            accountHolderName: partner.bank_detail_attributes?.beneficiary_name,
            // Verification status
            bankVerified: partner.bank_detail_attributes?.status === 'Verified',
            is_bank_verified: partner.bank_detail_attributes?.status === 'Verified',
            // KYC documents
            kycDocs: partner.kyc_docs_attributes || [],
            // Additional fields
            roleNames: partner.role_names,
            is_active: true,
            // Keep original data for reference
            originalData: partner
          };
        }
      }
      
      // Fallback: try to fetch using getVendors and filter
      const { items = [] } = await vendorService.getVendors();
      return items.find(vendor => vendor.id === Number(id)) || null;
    } catch (error) {
      console.error(`Error fetching partner with ID ${id}:`, error);
      return null;
    }
  }
};

export default vendorService;
