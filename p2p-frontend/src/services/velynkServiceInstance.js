/* eslint-disable import/no-cycle */
import axios from 'axios';
import apiConfig from '../config/api.config';
import { getAuthToken } from './velynkAuthService';
// Service configuration
const serviceConfig = {
  timeout: 45000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
  crossDomain: true,
};

// Create velynk service instance
const velynkServiceInstance = axios.create({
  ...serviceConfig,
  baseURL: apiConfig.velynkUrl,
});

velynkServiceInstance.CancelToken = axios.CancelToken;
velynkServiceInstance.isCancel = axios.isCancel;

// Add request interceptor for authentication
velynkServiceInstance.interceptors.request.use(
  async (config) => {
    // Add the fixed bearer token for Velynk service
    const modifiedConfig = { ...config };
    modifiedConfig.headers.Authorization = await getAuthToken();
    
    // Add ngrok skip header if needed
    modifiedConfig.headers['ngrok-skip-browser-warning'] = 'true';
    
    return modifiedConfig;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor
velynkServiceInstance.interceptors.response.use(
  (response) => {
    // Return just the data part of the response
    return response.data;
  },
  (error) => {
    if (velynkServiceInstance.isCancel(error)) {
      return;
    }
    
    // Handle timeout errors
    if (error.code === 'ECONNABORTED') {
      const timeoutError = {
        status: 504, // Gateway Timeout status code
        message: 'Request Timed Out',
      };
      return Promise.reject(timeoutError);
    }
    
    // Log the error for debugging
    console.error('Velynk API Error:', error);
    
    // Rethrow the error for handling in the service
    throw error;
  }
);

export default velynkServiceInstance;
