/**
 * Role Constants
 * Centralized place for all role definitions used throughout the application
 */

// User role types
export const USER_ROLES = {
  // Cost Center Head role
  COST_CENTER_HEAD: 'cc_head',
  
  // Business Finance role
  BUSINESS_FINANCE: 'Biz_Fin',
  
  // Finance role
  FINANCE: 'finance',
  
  // Procurement Manager role
  PROCUREMENT_MANAGER: 'procurement_manager',
  
  // Admin role - may be implemented differently (as a flag rather than a role)
  ADMIN: 'admin'
};

// Helper function to check if a user has a specific role
export const hasRole = (user, roleName) => {
  if (!user || !user.roles) return false;
  
  // If roles is an array of objects with a 'role' property (primary format)
  if (Array.isArray(user.roles)) {
    return user.roles.some(roleObj => 
      roleObj && typeof roleObj === 'object' && roleObj.role === roleName
    );
  }
  
  // Alternative checks
  // If roles is a string
  if (typeof user.roles === 'string') {
    return user.roles === roleName;
  }
  
  // If singular role property exists
  if (user.role && typeof user.role === 'string') {
    return user.role === roleName;
  }
  
  return false;
};

// Helper function to check if a user is an admin
export const isAdmin = (user) => {
  if (!user) return false;
  
  // Check various ways admin status might be stored
  return (
    user.isAdmin === true ||
    hasRole(user, USER_ROLES.ADMIN) ||
    (user.permissions && user.permissions.includes('admin'))
  );
};


/**
 * Check if user can view GRNs for a PO
 * @param {Object} user - Current user object
 * @param {Object} poDetails - PO details object
 * @returns {boolean} - Whether user can view GRNs
 */
export const canViewGRNs = (user, poDetails) => {
  if (!user || !poDetails) return false;
  
  // Always allow if user created the PR
  if (user.id === poDetails.createdBy) {
    return true;
  }
  
  // Check for specific roles
  const allowedRoles = [USER_ROLES.BUSINESS_FINANCE, USER_ROLES.FINANCE, USER_ROLES.ADMIN, USER_ROLES.PROCUREMENT_MANAGER];
  return hasAnyRole(user, allowedRoles);
};

/**
 * Check if user can view Invoices for a PO
 * @param {Object} user - Current user object
 * @returns {boolean} - Whether user can view Invoices
 */
export const canViewInvoices = (user) => {
  if (!user) return false;
  
  // Only allow specific roles to view invoices
  const allowedRoles = [USER_ROLES.BUSINESS_FINANCE, USER_ROLES.FINANCE, USER_ROLES.ADMIN, USER_ROLES.PROCUREMENT_MANAGER];
  return hasAnyRole(user, allowedRoles);
};

export const cancreateGRNs = (user, poDetails) => {
  if (!user || !poDetails) return false;
  
  // Always allow if user created the PR
  if (user.id === poDetails.createdBy) {
    return true;
  }
  
  // Only allow specific roles to create GRNs
  const allowedRoles = [USER_ROLES.PROCUREMENT_MANAGER];
  return hasAnyRole(user, allowedRoles);
};

/**
 * Check if user has any of the specified roles
 * @param {Object} user - User object
 * @param {string[]} roles - Array of role names to check
 * @returns {boolean} - Whether user has any of the specified roles
 */
export const hasAnyRole = (user, roles) => {
  console.log("user",user);
  console.log("roles",roles);
  if (!user) return false;
  
  let is_role = roles.some(role => hasRole(user, role));
  console.log("is_role",is_role);
  // Check each role individually using the hasRole function
  return is_role
};