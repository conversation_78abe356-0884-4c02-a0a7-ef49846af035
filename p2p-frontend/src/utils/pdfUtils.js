// Vegrow Invoice-style PO PDF Generator (fixed encoding, real values)
import { jsPDF } from 'jspdf';
import vegrowLogo from '../vegrow.png';

export const generatePOPDF = (poDetails) => {
  const doc = new jsPDF({ orientation: 'portrait', unit: 'mm', format: 'a4' });
  const pageWidth = doc.internal.pageSize.getWidth();
  const margin = 15;

  const addLogo = () => {
    const img = new Image();
    img.src = vegrowLogo;
    doc.addImage(img, 'PNG', margin, 10, 30, 12);
  };
  addLogo();

  doc.setFont('helvetica', 'bold');
  doc.setFontSize(14);
  doc.text('PURCHASE ORDER', pageWidth - margin, 20, { align: 'right' });
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);
  doc.text(`PO Number : ${poDetails.poNumber || 'N/A'}`, pageWidth - margin, 25, { align: 'right' });
  doc.text(`Date : ${new Date(poDetails.createdAt).toLocaleDateString('en-IN')}`, pageWidth - margin, 30, { align: 'right' });

  doc.setFontSize(10);
  doc.setFont('helvetica', 'bold');
  doc.text('Billing To:', margin, 40);
  doc.text('Shipping To:', pageWidth / 2 + 10, 40);

  doc.setFont('helvetica', 'normal');

  const vendor = poDetails.vendor || {};

  const leftBlock = [
    `Vendor: ${vendor.name || poDetails.quotation.vendorName || 'N/A'}`,
    `GSTIN: ${vendor.gstin || 'N/A'}`,
    `Address: ${vendor.address || 'N/A'}`
  ];
  const rightBlock = [
    `Business Unit : ${poDetails.businessUnit.name || 'N/A'}`,
    `Cost Center : ${poDetails.costCenter.name || 'N/A'}`,
    `Address : ${poDetails.shipToAddress || 'N/A'}`,
    `GSTIN: ${poDetails.gstin || 'N/A'}`
  ];
  leftBlock.forEach((text, i) => doc.text(text, margin, 45 + i * 5));
  rightBlock.forEach((text, i) => doc.text(text, pageWidth / 2 + 10, 45 + i * 5));

  // Table Header
  const tableHeaders = ['S No', 'Item and Description', 'Qty (UOM)', 'Price/Unit', 'GST','Total (with GST)'];
  const colWidths = [12, 66, 20, 22, 30, 30];
  const startY = 90;

  const drawRow = (row, y, isHeader = false) => {
    let x = margin;
    row.forEach((cell, i) => {
      const w = colWidths[i];
      doc.setFont('helvetica', isHeader ? 'bold' : 'normal');
      doc.setFontSize(9);
      doc.setTextColor(0);
      doc.text(String(cell), x + 2, y + 5);
      doc.rect(x, y, w, 8);
      x += w;
    });
  };

  drawRow(tableHeaders, startY, true);

  let y = startY + 8;
  const items = poDetails.items || [];
  const unitTotal = items.reduce((sum, i) => sum + parseInt(i.quantity || 0), 0);
  // const totalPricePerUnit = items.reduce((sum, i) => sum + parseInt(i.pricePerUnit || 0), 0);
  // const totalGst = items.reduce((sum, i) => sum + parseInt(i.gstAmount || 0), 0);
  const totalAmount = items.reduce((sum, i) => sum + parseInt(i.totalValue || 0), 0);

  items.forEach((item, i) => {
    const row = [
      i + 1,
      `${item.itemName || 'N/A'}${item.itemDescription ? ' - ' + item.itemDescription : ''}`,
      parseFloat(item.quantity || 0).toFixed(2)+" "+item.uom,
      parseInt(item.pricePerUnit || 0),
      parseInt(item.gstAmount || 0) + " (" + parseInt(item.gstPercentage) + "%)",
      parseInt(item.totalValue)
    ];
    drawRow(row, y);
    y += 8;
  });

  // drawRow(["", "Total", unitTotal, totalPricePerUnit, totalGst, totalAmount], y, true);
  doc.setFont('helvetica', 'bold');
  doc.text(`Total Units: ${unitTotal}`, margin, y+8);
  doc.text(`Total Amount: ${totalAmount}`, pageWidth /2 + 85, y+8, { align: 'right' });

  // Footer
  y += 15;
  doc.setDrawColor(0);
  doc.line(margin, y, pageWidth - margin, y);
  y += 10;
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text('Customer Sign:', margin, y + 15);
  doc.text('__________________________', margin, y + 20);
  doc.text('Authorized Sign:', pageWidth - 70, y + 15);
  doc.text('__________________________', pageWidth - 70, y + 20);

  return doc;
};

export const downloadPDF = (doc, filename) => {
  doc.save(`${filename}.pdf`);
};

export const printPDF = (doc) => {
  const blob = doc.output('blob');
  const url = URL.createObjectURL(blob);
  const win = window.open(url, '_blank');
  if (win) win.onload = () => win.print();
};
