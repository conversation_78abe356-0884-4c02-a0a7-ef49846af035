/**
 * Status Constants
 * Centralized place for all status definitions used throughout the application
 */

// Purchase Request status types (backend values)
export const PR_STATUS = {
  // Pending statuses
  PENDING_APPROVAL: 'PENDING_APPROVAL',
  PENDING_CC_HEAD_APPROVAL: 'PENDING_CC_HEAD_APPROVAL',
  PENDING_BUSINESS_HEAD_APPROVAL: 'PENDING_BUSINESS_HEAD_APPROVAL',
  PENDING_PROCUREMENT_APPROVAL: 'PENDING_PROCUREMENT_APPROVAL',
  
  // Completed statuses
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  DRAFT: 'DRAFT',
  
  // Other statuses
  CANCELLED: 'CANCELLED',
  IN_PROCESS: 'IN_PROCESS',
  COMPLETED: 'COMPLETED',
  VOID: 'VOID'
};

// Display-friendly status labels (for UI)
export const PR_STATUS_DISPLAY = {
  [PR_STATUS.PENDING_APPROVAL]: 'PENDING APPROVAL',
  [PR_STATUS.PENDING_CC_HEAD_APPROVAL]: 'PENDING CC HEAD APPROVAL',
  [PR_STATUS.PENDING_BUSINESS_HEAD_APPROVAL]: 'PENDING BUSINESS HEAD APPROVAL',
  [PR_STATUS.PENDING_PROCUREMENT_APPROVAL]: 'PENDING PROCUREMENT APPROVAL',
  [PR_STATUS.APPROVED]: 'APPROVED',
  [PR_STATUS.REJECTED]: 'REJECTED',
  [PR_STATUS.DRAFT]: 'DRAFT',
  [PR_STATUS.CANCELLED]: 'CANCELLED',
  [PR_STATUS.IN_PROCESS]: 'IN PROCESS',
  [PR_STATUS.COMPLETED]: 'COMPLETED',
  [PR_STATUS.VOID]: 'VOID'
};

// Helper function to get the display-friendly status label
export const getStatusDisplay = (statusCode) => {
  return PR_STATUS_DISPLAY[statusCode] || statusCode;
};

// Status color mappings for UI components
export const getStatusColor = (status) => {
  switch (status) {
    case PR_STATUS.APPROVED:
      return 'success';
    case PR_STATUS.REJECTED:
    case PR_STATUS.CANCELLED:
      return 'error';
    case PR_STATUS.PENDING_APPROVAL:
    case PR_STATUS.PENDING_CC_HEAD_APPROVAL:
    case PR_STATUS.PENDING_BUSINESS_HEAD_APPROVAL:
    case PR_STATUS.PENDING_PROCUREMENT_APPROVAL:
      return 'warning';
    case PR_STATUS.DRAFT:
      return 'default';
    case PR_STATUS.IN_PROCESS:
      return 'info';
    case PR_STATUS.COMPLETED:
      return 'success';
    case PR_STATUS.VOID:
      return 'error';
    default:
      return 'default';
  }
};
