import React from 'react';
import { <PERSON>, But<PERSON>, Alert } from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';

const ErrorState = ({ error, onBack }) => {
  return (
    <Box sx={{ p: 3 }}>
      <Button
        startIcon={<ArrowBackIcon />}
        onClick={onBack}
        variant="outlined"
        sx={{ mb: 2 }}
      >
        Back to My Approvals
      </Button>
      <Alert severity="error">{error || 'Purchase request not found'}</Alert>
    </Box>
  );
};

export default ErrorState;
