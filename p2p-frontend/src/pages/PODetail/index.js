import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
// import { toast } from 'react-toastify';
import {
  Box,
  Paper,
  Typography,
  Button,
  CircularProgress,
  Divider,
  Alert,
} from "@mui/material";
import {
  Receipt as ReceiptIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import apiService from '../../services/apiService';
import { canViewGRNs, canViewInvoices, cancreateGRNs } from '../../utils/roleConstants';

// Import our modular components
import PODetailHeader from '../../components/PODetail/PODetailHeader';
import POItemsTable from '../../components/PODetail/POItemsTable';
import POApprovalHistory from '../../components/PODetail/POApprovalHistory';
import PODetailInfo from '../../components/PODetail/PODetailInfo';
import ApprovalActions from '../../components/ApprovalDetail/ApprovalActions';
import GRNDialog from '../../components/GRN/GRNDialog';
import GRNTable from '../../components/GRN/GRNTable';
import QuotesDisplay from '../../components/PODetail/QuotesDisplay';
import POStatusActions from '../../components/PODetail/POStatusActions';

// Note: We're using helper functions from our modular components instead of defining them here
// This helps keep our code DRY (Don't Repeat Yourself) and more maintainable

const PODetail = () => {
  const { poId } = useParams();
  const navigate = useNavigate();
  // Get auth context for the user object and roles
  const { user } = useAuth();
  const [poDetails, setPODetails] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showPOVerificationDialog, setShowPOVerificationDialog] =
    useState(false);
  const [showPOVerificationToRole, setShowPOVerificationToRole] =
    useState(null);
  // State for PO approval
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [actionSuccess, setActionSuccess] = useState(null);
  // GRN state is now fully managed in the GRNTable component for better modularity
  const [openGRNDialog, setOpenGRNDialog] = useState(false);
  
  // State for all quotations related to this PO
  const [allQuotations, setAllQuotations] = useState([]);
  const [quotationsLoading, setQuotationsLoading] = useState(false);
  const [selectedQuotationId, setSelectedQuotationId] = useState(null);

  // Function to fetch PO details - defined outside useEffect for reuse
  const fetchPODetails = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Make API call to get PO details
      const response = await apiService.purchaseOrderService.getPurchaseOrderDetails(poId);
      
      // Check if response is valid
      if (response && (response.data || response)) {
        // Handle both cases where data might be in response.data or response directly
        const poData = response.data || response;
        
        if (!poData || Object.keys(poData).length === 0) {
          setError('Purchase order details not found');
          return;
        }
        
        // Handle both direct response and nested data structure
        const purchaseOrderData = poData.purchaseOrder || poData;
        
        // Find and set the selected quotation ID from various possible fields
        if (purchaseOrderData.quotationId) {
          console.log('Found quotationId in PO details:', purchaseOrderData.quotationId);
          setSelectedQuotationId(purchaseOrderData.quotationId);
        } else if (purchaseOrderData.quotation && purchaseOrderData.quotation.id) {
          console.log('Found quotation.id in PO details:', purchaseOrderData.quotation.id);
          setSelectedQuotationId(purchaseOrderData.quotation.id);
        } else if (purchaseOrderData.vendorQuote && purchaseOrderData.vendorQuote.id) {
          console.log('Found vendorQuote.id in PO details:', purchaseOrderData.vendorQuote.id);
          setSelectedQuotationId(purchaseOrderData.vendorQuote.id);
        }
        
        setPODetails(purchaseOrderData);
        
        // Fetch all quotations related to this PO
        if (purchaseOrderData.prId) {
          fetchQuotations(purchaseOrderData.prId);
        }
        
        // Check for PO verification dialog settings
        if (purchaseOrderData.showPOVerificationDialog !== undefined) {
          setShowPOVerificationDialog(purchaseOrderData.showPOVerificationDialog);
        }
        
        if (purchaseOrderData.showPOVerificationToRole) {
          setShowPOVerificationToRole(purchaseOrderData.showPOVerificationToRole);
        }
        
        // GRN items initialization is now handled by the GRNTable component
      }
    } catch (error) {
      console.error("Error fetching PO details:", error);
      setError(error.message || "Failed to fetch purchase order details");
    } finally {
      setLoading(false);
    }
  };
  
  // Function to fetch quotations related to the PO's PR - using same approach as PR details page
  const fetchQuotations = async (prId) => {
    if (!prId) return;
    
    try {
      setQuotationsLoading(true);
      
      // Use the same quotation service API as in the PR details page
      const quotations = await apiService.quotationService.getQuotationsForPR(prId);
      
      if (quotations && quotations.length > 0) {
        console.log('Fetched quotations:', quotations);
        
        // Make sure payment and delivery terms are consistently available
        const processedQuotations = quotations.map(quote => {
          return {
            ...quote,
            // Ensure consistent field names for payment and delivery terms
            paymentTerms: quote.paymentTerms || quote.payment_terms || '',
            deliveryTerms: quote.deliveryTerms || quote.delivery_terms || ''
          };
        });
        
        // Store all quotations in the state
        setAllQuotations(processedQuotations);
        
        // Also update the PO details with the quotations for components that expect it there
        setPODetails(prev => ({
          ...prev,
          vendorQuotes: processedQuotations
        }));
        
        // Verify if the selectedQuotationId exists in the fetched quotations
        const quotationExists = processedQuotations.some(quote => 
          quote.id && selectedQuotationId && 
          (quote.id.toString() === selectedQuotationId.toString() || 
           quote.id === parseInt(selectedQuotationId) || 
           parseInt(quote.id) === selectedQuotationId)
        );
        
        // If selected quotation ID is not found in the quotations, try to set it again from PO details
        if (!quotationExists) {
          if (poDetails && poDetails.quotationId) {
            console.log('Setting selected quotation ID from PO details:', poDetails.quotationId);
            setSelectedQuotationId(poDetails.quotationId);
          } else if (poDetails && poDetails.quotation && poDetails.quotation.id) {
            console.log('Setting selected quotation ID from PO quotation object:', poDetails.quotation.id);
            setSelectedQuotationId(poDetails.quotation.id);
          }
        }
      } else {
        setAllQuotations([]);
      }
    } catch (error) {
      console.error('Error fetching quotations:', error);
      setAllQuotations([]);
    } finally {
      setQuotationsLoading(false);
    }
  };

  useEffect(() => {
    // Fetch PO details when component mounts
    fetchPODetails();
    
    // Clean up any subscriptions
    return () => {
      // clean up code if needed
    };
  }, [poId]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleBackClick = () => {
    // Check if we have a referrer in the history
    const referrer = document.referrer;

    // If coming from a PR detail page, go back to that PR
    if (referrer && referrer.includes("/purchase-requests/")) {
      window.history.back();
    } else {
      // Otherwise go to the purchase orders list
      navigate("/purchase-orders");
    }
  };

  // Function to handle opening GRN dialog - simplified for better modularity
  const handleOpenGRNDialog = () => {
    setOpenGRNDialog(true);
  };

  const handleViewPR = (prId) => {
    // Navigate to the approval detail page for the PR
    navigate(`/approval-detail/${prId}`);
  };

  // Handle approve quotation
  const handleApproveQuotation = async (remarks = "") => {
    try {
      setIsActionLoading(true);

      // Call the API to approve the quotation
      await apiService.purchaseOrderService.verifyQuote(poId, {
        action: "APPROVE",
        remarks: remarks,
      });

      // On success
      setActionSuccess({
        type: "success",
        message: "Quotation approved successfully",
      });

      // Refresh PO details
      fetchPODetails();
    } catch (error) {
      console.error("Error approving quotation:", error);
      setActionSuccess({
        type: "error",
        message:
          error.response?.data?.message ||
          "Error approving quotation. Please try again.",
      });
    } finally {
      setIsActionLoading(false);
    }
  };

  const handleRejectQuotation = async (remarks = "") => {
    if (!remarks.trim()) {
      setActionSuccess({
        type: "error",
        message: "Rejection reason is required",
      });
      return;
    }

    try {
      setIsActionLoading(true);

      // Call the API to reject the quotation
      await apiService.purchaseOrderService.verifyQuote(poId, {
        action: "REJECT",
        remarks: remarks,
      });

      // On success
      setActionSuccess({
        type: "success",
        message: "Quotation rejected successfully",
      });

      // Refresh PO details
      fetchPODetails();
    } catch (error) {
      console.error("Error rejecting quotation:", error);
      setActionSuccess({
        type: "error",
        message:
          error.response?.data?.message ||
          "Error rejecting quotation. Please try again.",
      });
    } finally {
      setIsActionLoading(false);
    }
  };

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "80vh",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={handleBackClick}
          sx={{ mt: 2 }}
        >
          Back to Purchase Orders
        </Button>
      </Box>
    );
  }

  // Make the PO details available for components that might need it
  window.poDetails = poDetails;

  return (
    <Box sx={{ flexGrow: 1, p: { xs: 2, md: 3 } }}>
      {/* PO Header Component - now with better styling */}
      <PODetailHeader
        poDetails={poDetails}
        onBack={handleBackClick}
        onViewPR={handleViewPR}
      />

      {/* Main content - improve spacing to prevent overlap */}
      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", md: "row" },
          gap: 3,
          mb: 6 // Add bottom margin to ensure space for GRN table
        }}
      >
        {/* Left side (main content) */}
        <Box sx={{ 
          flex: { md: '1 1 65%' },
          display: 'flex',
          flexDirection: 'column',
          gap: 3
        }}>
          {/* Order Information - moved to top */}
          <Paper sx={{ p: 3 }}>
            <PODetailInfo poDetails={poDetails} onViewPR={handleViewPR} />
          </Paper>
          
          {/* PO Items - moved before quotations */}
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Purchase Order Items
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {!poDetails.items || poDetails.items.length === 0 ? (
              <Alert severity="info" sx={{ mt: 2 }}>
                No items found for this purchase order.
              </Alert>
            ) : (
              <POItemsTable
                items={poDetails.items}
                currency={poDetails.currency}
              />
            )}
          </Paper>

          {/* Quotation Information */}
          <Paper sx={{ p: 3, mb: 6 }}> {/* Increase bottom margin */}
            <Typography variant="h6" gutterBottom>
              Quotation Details
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            {quotationsLoading ? (
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 3 }}>
                <CircularProgress size={24} sx={{ mr: 1 }} />
                <Typography>Loading quotations...</Typography>
              </Box>
            ) : allQuotations.length === 0 && !poDetails.quotation ? (
              <Alert severity="info">
                No quotations available for this purchase request.
              </Alert>
            ) : (
              <QuotesDisplay 
                quotations={allQuotations} 
                selectedQuoteId={selectedQuotationId || poDetails.quotationId}
                showComparison={true}
                enableViewMode={true}
              />
            )}
          </Paper>
        </Box>

        {/* Right side */}
        <Box
          sx={{
            flex: { md: "1 1 35%" },
            display: "flex",
            flexDirection: "column",
            gap: 3,
          }}
        >
          {/* GRN Button - only show if PO status is appropriate */}
          {poDetails.status === "Open" && (
            <Paper sx={{ p: 3, mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Goods Receipt
              </Typography>
              <Box sx={{ mt: 2, display: "flex", justifyContent: "center" }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<ReceiptIcon />}
                  onClick={handleOpenGRNDialog}
                  fullWidth
                  disabled={!cancreateGRNs(user, poDetails)}
                >
                  Create GRN
                </Button>
              </Box>
              {/* PO Status Actions - Using the dedicated component */}
              <POStatusActions
                poDetails={poDetails}
                onStatusChange={fetchPODetails}
                onActionSuccess={setActionSuccess}
              />
            </Paper>
          )}
          
          {/* PO Status Actions for Closed POs - Allow reopening */}
          {poDetails.status === "Closed" && (
            <Paper sx={{ p: 3, mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                PO Status Actions
              </Typography>
              <POStatusActions
                poDetails={poDetails}
                onStatusChange={fetchPODetails}
                onActionSuccess={setActionSuccess}
              />
            </Paper>
          )}
          {/* Approval Timeline - now using PR timeline API with PR ID */}
          <Paper sx={{ p: 0, overflow: "hidden" }}>
            <POApprovalHistory prId={poDetails.prId} />
          </Paper>

          {/* Approval Actions - don't show if PO is rejected */}
          {poDetails.status === "REJECTED" ? (
            <Paper sx={{ p: 3, mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Purchase Order Rejected
              </Typography>
              <Box sx={{ p: 2 }}>
                <Alert severity="error">
                  This purchase order has been rejected and cannot be processed
                  further.
                </Alert>
              </Box>
            </Paper>
          ) : (
            showPOVerificationDialog && (
              <Paper sx={{ p: 3, mt: 3 }}>
                <Typography variant="h6" gutterBottom>
                  PO Approval Actions
                </Typography>
                <Box sx={{ p: 2 }}>
                  <ApprovalActions
                    showPRVerificationDialog={showPOVerificationDialog}
                    showPRVerificationToRole={showPOVerificationToRole}
                    userRoles={user.roles || []}
                    onApproveAction={handleApproveQuotation}
                    onRejectAction={handleRejectQuotation}
                    isActionLoading={isActionLoading}
                    isPO={true}
                  />
                </Box>
              </Paper>
            )
          )}
        </Box>
      </Box>
      
      {/* GRN Listing Section - Clear fix for overlap */}
      {user && poDetails && canViewGRNs(user, poDetails) && (
        <Box 
          sx={{ 
            mt: 8, // Increased top margin
            pt: 2, // Add padding top
            position: 'relative',
            clear: 'both',
            width: '100%'
          }}
        >
          <Divider sx={{ mb: 4 }} /> {/* Add divider before GRN table */}
          <GRNTable 
            poDetails={poDetails}
            poId={poId}
            openGRNDialog={openGRNDialog}
            setOpenGRNDialog={setOpenGRNDialog}
            onOpenGRNDialog={handleOpenGRNDialog}
            onRefresh={fetchPODetails}
            canViewInvoice={canViewInvoices(user)}
          />
        </Box>
      )}
      
      {/* Action success/error message */}
      {actionSuccess && (
        <Alert
          severity={actionSuccess.type}
          sx={{ mt: 2 }}
          onClose={() => setActionSuccess(null)}
        >
          {actionSuccess.message}
        </Alert>
      )}
      
      <GRNDialog
        open={openGRNDialog}
        onClose={() => {
          setOpenGRNDialog(false);
          // Refresh data after closing the dialog
          fetchPODetails();
          // GRN data is now fetched directly by the GRNTable component
        }}
        poDetails={poDetails}
        onSubmit={async (payload, invoiceFile) => {
          try {
            let result;
            
            // Use createGRNWithInvoice if there's an invoice file, otherwise use createGRN
            if (invoiceFile) {
              result = await apiService.createGRNWithInvoice(payload, invoiceFile);
              setActionSuccess({
                type: 'success',
                message: 'GRN and Invoice created successfully'
              });
            } else {
              result = await apiService.createGRN(payload);
              setActionSuccess({
                type: 'success',
                message: 'GRN created successfully'
              });
            }
            
            // Refresh PO details - GRN data is handled by the GRNTable component
            await fetchPODetails();
            return result;
          } catch (error) {
            console.error('Error creating GRN/Invoice:', error);
            setActionSuccess({
              type: 'error',
              message: error.response?.data?.message || 'Failed to create GRN/Invoice'
            });
            throw error;
          }
        }}
      />
    </Box>
  );
};

export default PODetail;
