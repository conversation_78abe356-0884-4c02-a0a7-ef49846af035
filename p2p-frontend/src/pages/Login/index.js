import React, { useState, useEffect } from 'react';
import { GoogleLogin } from '@react-oauth/google';
import {
  Box,
  Typography,
  CircularProgress,
  Container,
  Paper,
  Alert,
  AlertTitle
} from '@mui/material';
import { useAuth } from '../../context/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';

/**
 * Login page component with Google Sign-In only
 */
const Login = () => {
  const { handleGoogleLogin, isAuthenticated, error: authError, clearError } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [accessDenied, setAccessDenied] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Extract returnTo from URL query parameters
  const searchParams = new URLSearchParams(location.search);
  const returnTo = searchParams.get('returnTo') || '/dashboard';

  // Redirect to returnTo path if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate(returnTo, { replace: true });
    }
  }, [isAuthenticated, navigate, returnTo]);

  // Set local error state when auth context error changes
  useEffect(() => {
    if (authError) {
      setError(authError);
      // Check if the error is about access being denied
      setAccessDenied(authError.includes('Access denied') || authError.includes('not authorized'));
    }
  }, [authError]);

  // Clear errors when component unmounts
  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  /**
   * Handle Google login response
   * @param {Object} response - Google OAuth response
   */
  const responseGoogle = async (response) => {
    try {
      setLoading(true);
      setError('');
      setAccessDenied(false);
      await handleGoogleLogin(response, returnTo);
    } catch (error) {
      console.error('Google login error:', error);
      setError(error.message || 'Google login failed. Please try again.');
      // Check if the error is about access being denied
      setAccessDenied(error.message && (
        error.message.includes('Access denied') ||
        error.message.includes('not authorized')
      ));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm" sx={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh'
    }}>
      <Paper elevation={3} sx={{
        width: '100%',
        padding: 4,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }}>
        <Box sx={{ mb: 4, textAlign: 'center' }}>
          <Typography variant="h4" sx={{ mb: 1, fontWeight: 'bold', color: 'primary.main' }}>
            Vegrow - Procure to Pay
          </Typography>
          <Typography variant="body1" sx={{ color: 'text.secondary' }}>
            Sign in to continue
          </Typography>
        </Box>

        {error && (
          <Alert
            severity={accessDenied ? "warning" : "error"}
            sx={{
              width: '100%',
              mb: 3,
              '& .MuiAlert-message': {
                width: '100%'
              }
            }}
          >
            {accessDenied && <AlertTitle>Access Denied</AlertTitle>}
            {error}
            {accessDenied && (
              <Typography variant="body2" sx={{ mt: 1 }}>
                If you believe this is a mistake, please contact your administrator for assistance.
              </Typography>
            )}
          </Alert>
        )}

        {/* Google Login */}
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: '100%', mt: 3 }}>
          <Box position="relative" sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
            {loading ? (
              <CircularProgress size={40} />
            ) : (
              <GoogleLogin
                onSuccess={responseGoogle}
                onError={(error) => {
                  console.error('Google Sign-In Error:', error);
                  setError('Failed to connect to Google. Please try again.');
                  setAccessDenied(false);
                }}
                useOneTap
                theme="filled_blue"
                shape="rectangular"
                text="signin_with"
                size="large"
                disabled={loading}
              />
            )}
          </Box>

          <Typography variant="body2" sx={{ mt: 2, textAlign: 'center', color: 'text.secondary' }}>
            Please use your Google account to sign in
          </Typography>
          <Typography variant="body2" sx={{ mt: 1, textAlign: 'center', color: 'text.secondary' }}>
            Note: Only authorized users can access this application
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default Login;
