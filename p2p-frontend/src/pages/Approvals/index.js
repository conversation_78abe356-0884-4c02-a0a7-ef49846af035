import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  Typography, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  Button,
  Chip,
  Toolbar,
  IconButton,
  InputBase,
  Divider
} from '@mui/material';
import { 
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';
import apiService from '../../services/apiService';
import { useAuth } from '../../context/AuthContext';
import ApprovalTableRow from '../../components/Approvals/ApprovalTableRow';
import ApprovalHeader from '../../components/Approvals/ApprovalHeader';

const Approvals = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [pendingApprovals, setPendingApprovals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const fetchPendingApprovals = async () => {
      try {
        const approvals = await apiService.getPendingApprovals(user?.id);
        setPendingApprovals(approvals);
      } catch (error) {
        console.error('Error fetching pending approvals:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPendingApprovals();
  }, [user]);

  const handleViewPR = (prId) => {
    navigate(`/approvals/${prId}`);
  };

  const filteredApprovals = pendingApprovals.filter(approval => 
    approval.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    approval.businessUnit.toLowerCase().includes(searchTerm.toLowerCase()) ||
    approval.costCenter.toLowerCase().includes(searchTerm.toLowerCase()) ||
    approval.raisedBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
    approval.itemType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <ApprovalHeader 
        title="Pending Approvals" 
        subtitle="Review and approve purchase requests"
        count={pendingApprovals.length}
      />
      
      <Paper sx={{ width: '100%', mb: 2 }}>
        <Toolbar sx={{ pl: { sm: 2 }, pr: { xs: 1, sm: 1 } }}>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Box sx={{ position: 'relative', mr: 2, flex: 1 }}>
              <Box sx={{ position: 'absolute', height: '100%', display: 'flex', alignItems: 'center', pl: 1 }}>
                <SearchIcon color="action" />
              </Box>
              <InputBase
                sx={{ pl: 5, width: '100%', '& input': { py: 1 } }}
                placeholder="Search by PR ID, Business Unit, Cost Center, or Requester"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </Box>
            <IconButton>
              <FilterListIcon />
            </IconButton>
          </Box>
        </Toolbar>
        
        <Divider />
        
        <TableContainer>
          <Table sx={{ minWidth: 750 }}>
            <TableHead>
              <TableRow>
                <TableCell>PR ID</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Business Unit</TableCell>
                <TableCell>Cost Center</TableCell>
                <TableCell>Raised By</TableCell>
                <TableCell>Raised On</TableCell>
                <TableCell align="right">Amount (₹)</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">Loading...</TableCell>
                </TableRow>
              ) : filteredApprovals.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} align="center">No pending approvals found</TableCell>
                </TableRow>
              ) : (
                filteredApprovals.map((approval) => (
                  <ApprovalTableRow 
                    key={approval.id} 
                    approval={approval} 
                    onView={handleViewPR}
                  />
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
};

export default Approvals;
