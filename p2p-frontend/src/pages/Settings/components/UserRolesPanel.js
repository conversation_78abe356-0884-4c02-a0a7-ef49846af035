import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Alert,
  Snackbar,
  CircularProgress,
  DialogContentText,
  FormHelperText
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import axios from 'axios';
import { getApiUrl } from '../../../config/api.config';
import { useAuth } from '../../../context/AuthContext';

// Available roles for selection
const AVAILABLE_ROLES = [
  { id: 1, name: 'admin', label: 'Admin' },
  { id: 2, name: 'procurement_manager', label: 'Procurement Manager' },
  { id: 3, name: 'cc_head', label: 'Cost Center Head' },
  { id: 4, name: 'biz_fin', label: 'Business Finance' },
  { id: 5, name: 'finance', label: 'Finance' },
  { id: 6, name: 'standard_user', label: 'Standard User' },
];

const UserRolesPanel = () => {
  // State for user roles data
  const [userRoles, setUserRoles] = useState([]);
  const [users, setUsers] = useState([]);
  const [costCenters, setCostCenters] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // State for role form
  const [openForm, setOpenForm] = useState(false);
  const [currentRole, setCurrentRole] = useState(null);
  const [formData, setFormData] = useState({
    userId: '',
    role: '',
    costCenterId: null
  });

  // State for delete confirmation
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [roleToDelete, setRoleToDelete] = useState(null);

  // State for notifications
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  const { user: currentAuthUser } = useAuth();

  // Load data on component mount
  useEffect(() => {
    fetchUserRoles();
    fetchUsers();
    fetchCostCenters();
  }, [currentAuthUser]);

  // Fetch user roles from API
  const fetchUserRoles = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(getApiUrl('/admin/user-roles'), {
        headers: {
          'Authorization': `Bearer ${currentAuthUser.token}`
        }
      });

      // Log the response for debugging
      console.log('API response for user roles:', response.data);

      // Ensure we always set an array, even if the API returns something else
      if (!Array.isArray(response.data)) {
        console.warn('API did not return an array for user roles. Got:', typeof response.data, response.data);

        // If response.data is an object with a userRoles property that is an array, use that
        if (response.data && typeof response.data === 'object' && Array.isArray(response.data.userRoles)) {
          setUserRoles(response.data.userRoles);
        } else {
          // Otherwise, set an empty array
          setUserRoles([]);
        }
      } else {
        setUserRoles(response.data);
      }
    } catch (err) {
      console.error('Error fetching user roles:', err);
      setError('Failed to load user roles. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch users for dropdown
  const fetchUsers = async () => {
    try {
      console.log('Fetching users with token:', currentAuthUser?.token ? 'Token exists' : 'No token');
      
      const response = await axios.get(getApiUrl('/admin/users'), {
        headers: {
          'Authorization': `Bearer ${currentAuthUser.token}`
        }
      });

      // Log the response for debugging
      console.log('API response for users dropdown:', response.data);
      
      // Add more detailed logging
      if (Array.isArray(response.data)) {
        console.log(`Received ${response.data.length} users in array format`);
        
        // Log the first few users
        if (response.data.length > 0) {
          console.log('Sample user data:', response.data.slice(0, 3));
        }
        
        setUsers(response.data);
      } else if (response.data && typeof response.data === 'object') {
        console.log('Received users in object format with keys:', Object.keys(response.data));
        
        if (Array.isArray(response.data.users)) {
          console.log(`Received ${response.data.users.length} users in response.data.users`);
          setUsers(response.data.users);
        } else {
          console.warn('No users array found in response');
          setUsers([]);
        }
      } else {
        console.warn('Unexpected response format for users:', typeof response.data);
        setUsers([]);
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      if (err.response) {
        console.error('Error response:', err.response.status, err.response.data);
      }
      setError('Failed to load users for the dropdown. Please try again.');
      setUsers([]); // Ensure users is at least an empty array on error
    }
  };

  // Fetch cost centers for dropdown
  const fetchCostCenters = async () => {
    try {
      const response = await axios.get(getApiUrl('/admin/cost-centers'), {
        headers: {
          'Authorization': `Bearer ${currentAuthUser.token}`
        }
      });

      // Log the response for debugging
      console.log('API response for cost centers dropdown:', response.data);

      // Ensure we always set an array, even if the API returns something else
      if (!Array.isArray(response.data)) {
        console.warn('API did not return an array for cost centers dropdown. Got:', typeof response.data, response.data);

        // If response.data is an object with a costCenters property that is an array, use that
        if (response.data && typeof response.data === 'object' && Array.isArray(response.data.costCenters)) {
          setCostCenters(response.data.costCenters);
        } else {
          // Otherwise, set an empty array
          setCostCenters([]);
        }
      } else {
        setCostCenters(response.data);
      }
    } catch (err) {
      console.error('Error fetching cost centers:', err);
      setError('Failed to load cost centers for the dropdown. Please try again.');
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // If changing role to something other than cc_head, reset costCenterId
    if (name === 'role' && value !== 'cc_head') {
      setFormData({
        ...formData,
        [name]: value,
        costCenterId: null
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Open form for editing an existing user role
  const handleEditRole = (role) => {
    setCurrentRole(role);
    setFormData({
      userId: role.userId,
      role: role.role,
      costCenterId: role.costCenterId
    });
    setOpenForm(true);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (role) => {
    setRoleToDelete(role);
    setOpenDeleteDialog(true);
  };

  // Submit user role form (create or update)
  const handleSubmit = async () => {
    // Validate form
    if (!formData.userId || !formData.role) {
      setSnackbar({
        open: true,
        message: 'Please fill in all required fields',
        severity: 'error'
      });
      return;
    }

    // Validate cost center for cc_head role
    if (formData.role === 'cc_head' && !formData.costCenterId) {
      setSnackbar({
        open: true,
        message: 'Cost center is required for Cost Center Head role',
        severity: 'error'
      });
      return;
    }

    setLoading(true);

    try {
      if (currentRole) {
        // Update existing user role
        const response = await axios.put(
          getApiUrl(`/admin/user-roles/${currentRole.id}`),
          formData,
          {
            headers: {
              'Authorization': `Bearer ${currentAuthUser.token}`
            }
          }
        );

        // Update user roles list with the updated item
        const updatedItems = userRoles.map(item =>
          item.id === currentRole.id ? response.data : item
        );
        setUserRoles(updatedItems);

        setSnackbar({
          open: true,
          message: 'User role updated successfully',
          severity: 'success'
        });
      } else {
        // Create new user role
        const response = await axios.post(
          getApiUrl('/admin/user-roles'),
          formData,
          {
            headers: {
              'Authorization': `Bearer ${currentAuthUser.token}`
            }
          }
        );

        // Add new user role to the list
        setUserRoles([...userRoles, response.data]);

        setSnackbar({
          open: true,
          message: 'User role created successfully',
          severity: 'success'
        });
      }

      // Close the form dialog
      setOpenForm(false);
    } catch (err) {
      console.error('Error saving user role:', err);

      let errorMessage = 'Failed to save user role.';
      if (err.response && err.response.data && err.response.data.message) {
        errorMessage = err.response.data.message;
      }

      setSnackbar({
        open: true,
        message: errorMessage,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Delete user role
  const handleDelete = async () => {
    if (!roleToDelete) return;

    setLoading(true);

    try {
      await axios.delete(
        getApiUrl(`/admin/user-roles/${roleToDelete.id}`),
        {
          headers: {
            'Authorization': `Bearer ${currentAuthUser.token}`
          }
        }
      );

      // Remove deleted item from the list
      setUserRoles(userRoles.filter(item => item.id !== roleToDelete.id));

      setSnackbar({
        open: true,
        message: 'User role deleted successfully',
        severity: 'success'
      });

      // Close the delete dialog
      setOpenDeleteDialog(false);
    } catch (err) {
      console.error('Error deleting user role:', err);

      let errorMessage = 'Failed to delete user role.';
      if (err.response && err.response.data && err.response.data.message) {
        errorMessage = err.response.data.message;
      }

      setSnackbar({
        open: true,
        message: errorMessage,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Get role label for display
  const getRoleLabel = (roleName) => {
    const role = AVAILABLE_ROLES.find(r => r.name === roleName);
    return role ? role.label : roleName;
  };

  // Get user name for display
  const getUserName = (userId) => {
    console.log('Getting user name for userId:', userId);
    console.log('Available users:', users);
    
    if (!userId) return 'Unknown User';
    
    // Try with both string and number formats
    const user = users.find(u => 
      u.id === userId || 
      u.id === Number(userId) || 
      u.id === String(userId)
    );
    
    console.log('Found user:', user);
    
    if (!user) return `User #${userId}`;

    if (user.firstName || user.lastName) {
      return `${user.firstName || ''} ${user.lastName || ''}`.trim();
    } else if (user.username) {
      return user.username;
    } else if (user.email) {
      return user.email;
    } else {
      return `User #${userId}`;
    }
  };

  // Get cost center name for display
  const getCostCenterName = (costCenterId) => {
    if (!costCenterId) return '-';
    const costCenter = costCenters.find(cc => cc.id === costCenterId);
    return costCenter ? costCenter.name : `Cost Center #${costCenterId}`;
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6">User Roles</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => {
              fetchUserRoles();
              fetchUsers();
              fetchCostCenters();
            }}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading && !userRoles.length ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>User</TableCell>
                <TableCell>Role</TableCell>
                <TableCell>Cost Center</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.isArray(userRoles) && userRoles.map(item => (
                <TableRow key={item.id}>
                  <TableCell>
                    {item.user ? 
                      (item.user.firstName || item.user.lastName
                        ? `${item.user.firstName || ''} ${item.user.lastName || ''}`.trim()
                        : item.user.username || item.user.email || item.user.fullName || `User #${item.userId}`) 
                      : getUserName(item.userId)}
                  </TableCell>
                  <TableCell>{getRoleLabel(item.role)}</TableCell>
                  <TableCell>
                    {item.role === 'cc_head'
                      ? (item.costCenter ? item.costCenter.name : getCostCenterName(item.costCenterId))
                      : '-'}
                  </TableCell>
                  <TableCell align="right">
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleEditRole(item)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteClick(item)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              {(!Array.isArray(userRoles) || userRoles.length === 0) && (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    No user roles found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* User Role Form Dialog */}
      <Dialog
        open={openForm}
        onClose={() => setOpenForm(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Edit User Role
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <FormControl fullWidth margin="normal" required>
              <InputLabel id="user-label">User</InputLabel>
              <Select
                labelId="user-label"
                id="userId"
                name="userId"
                value={formData.userId}
                onChange={handleInputChange}
                label="User"
              >
                {Array.isArray(users) && users.map((user) => (
                  <MenuItem key={user.id} value={user.id}>
                    {user.firstName || user.lastName
                      ? `${user.firstName || ''} ${user.lastName || ''}`.trim()
                      : user.username}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth margin="normal" required>
              <InputLabel id="role-label">Role</InputLabel>
              <Select
                labelId="role-label"
                id="role"
                name="role"
                value={formData.role}
                onChange={handleInputChange}
                label="Role"
              >
                {AVAILABLE_ROLES.map((role) => (
                  <MenuItem key={role.id} value={role.name}>
                    {role.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {formData.role === 'cc_head' && (
              <FormControl fullWidth margin="normal" required>
                <InputLabel id="cost-center-label">Cost Center</InputLabel>
                <Select
                  labelId="cost-center-label"
                  id="costCenterId"
                  name="costCenterId"
                  value={formData.costCenterId || ''}
                  onChange={handleInputChange}
                  label="Cost Center"
                >
                  {Array.isArray(costCenters) && costCenters.map((cc) => (
                    <MenuItem key={cc.id} value={cc.id}>
                      {cc.name}
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText>Required for Cost Center Head role</FormHelperText>
              </FormControl>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenForm(false)}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || !formData.userId || !formData.role || (formData.role === 'cc_head' && !formData.costCenterId)}
          >
            {loading ? <CircularProgress size={24} /> : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this user role? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Cancel</Button>
          <Button
            onClick={handleDelete}
            color="error"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default UserRolesPanel;
