import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Typography,
  Alert,
  Snackbar,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import axios from 'axios';
import { getApiUrl } from '../../../config/api.config';
import { useAuth } from '../../../context/AuthContext';

const AVAILABLE_ROLES = [
  { id: 1, name: 'admin', label: 'Admin' },
  { id: 2, name: 'procurement_manager', label: 'Procurement Manager' },
  { id: 3, name: 'cc_head', label: 'Cost Center Head' },
  { id: 4, name: 'biz_fin', label: 'Business Finance' },
  { id: 5, name: 'finance', label: 'Finance' },
  { id: 6, name: 'standard_user', label: 'Standard User' },
];

const UsersPanel = () => {
  // State for users data
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formErrors, setFormErrors] = useState({});

  // State for user form
  const [openUserForm, setOpenUserForm] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    isActive: true,
    roles: []
  });
  const [selectedCostCenters, setSelectedCostCenters] = useState([]);

  // Initialize selected cost centers when editing a user
  useEffect(() => {
    if (currentUser) {
      const existingCostCenters = formData.roles
        .filter(role => role.role === 'cc_head')
        .map(role => role.costCenterId);
      setSelectedCostCenters(existingCostCenters);
    }
  }, [currentUser, formData.roles]);

  // State for cost centers
  const [costCenters, setCostCenters] = useState([]);

  // State for delete confirmation
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);

  // State for notifications
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  const { user: currentAuthUser } = useAuth();

  // Load users on component mount
  useEffect(() => {
    fetchUsers();
    fetchCostCenters();
  }, []);

  // Fetch users from API
  const fetchUsers = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(getApiUrl('/admin/users'), {
        headers: {
          'Authorization': `Bearer ${currentAuthUser.token}`
        }
      });

      // Log the response for debugging
      console.log('API response for users:', response.data);

      // Ensure we always set an array, even if the API returns something else
      if (!Array.isArray(response.data)) {
        console.warn('API did not return an array for users. Got:', typeof response.data, response.data);

        // If response.data is an object with a users property that is an array, use that
        if (response.data && typeof response.data === 'object' && Array.isArray(response.data.users)) {
          setUsers(response.data.users);
        } else {
          // Otherwise, set an empty array
          setUsers([]);
        }
      } else {
        setUsers(response.data);
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load users. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch cost centers for dropdown
  const fetchCostCenters = async () => {
    try {
      const response = await axios.get(getApiUrl('/admin/cost-centers'), {
        headers: {
          'Authorization': `Bearer ${currentAuthUser.token}`
        }
      });
      setCostCenters(response.data);
    } catch (err) {
      console.error('Error fetching cost centers:', err);
      // Don't set error state here to avoid blocking the main UI
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle role selection
  const handleRoleChange = (e) => {
    const selectedRoles = e.target.value;

    // Map selected roles to role objects
    const roleObjects = selectedRoles.map(roleName => {
      // If this role was already selected, keep its existing properties
      const existingRole = formData.roles.find(r => r.role === roleName);

      // For cc_head role, initialize with null costCenterId if it's a new selection
      if (roleName === 'cc_head') {
        return existingRole || { role: roleName, costCenterId: null };
      }

      // For other roles, just return the role name
      return existingRole || { role: roleName };
    });

    setFormData({
      ...formData,
      roles: roleObjects
    });
  };

  // Handle cost center selection for a specific role
  const handleCostCenterChange = (e, roleIndex) => {
    const selectedIds = e.target.value;
    
    // Update the selected cost centers state
    setSelectedCostCenters(selectedIds);

    // Create new roles array by:
    // 1. Filtering out existing cc_head roles
    // 2. Adding new cc_head roles for each selected cost center
    const updatedRoles = formData.roles.filter(role => role.role !== 'cc_head');
    
    // Add new cc_head roles for each selected cost center
    selectedIds.forEach(id => {
      // Only add if it's not already present
      if (!updatedRoles.some(r => r.role === 'cc_head' && r.costCenterId === id)) {
        updatedRoles.push({
          role: 'cc_head',
          costCenterId: id
        });
      }
    });

    setFormData(prev => ({
      ...prev,
      roles: updatedRoles
    }));
  };

  // Open user form for creating a new user
  const handleAddUser = () => {
    setCurrentUser(null);
    setFormData({
      username: '',
      email: '',
      password: '',
      firstName: '',
      lastName: '',
      isActive: true,
      roles: []
    });
    setOpenUserForm(true);
  };

  // Open user form for editing an existing user
  const handleEditUser = (user) => {
    setCurrentUser(user);
    setFormData({
      username: user.username,
      email: user.email,
      password: '',
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      isActive: user.isActive,
      roles: user.roles || []
    });
    setOpenUserForm(true);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (user) => {
    setUserToDelete(user);
    setOpenDeleteDialog(true);
  };

  // Submit user form (create or update)
  const validateForm = () => {
    const errors = {};
    if (!formData.username) errors.username = 'Username is required';
    if (!formData.email) errors.email = 'Email is required';
    if (!formData.firstName) errors.firstName = 'First name is required';
    if (!formData.lastName) errors.lastName = 'Last name is required';
    if (!formData.roles || formData.roles.length === 0) errors.roles = 'At least one role is required';
    if (!currentUser && !formData.password) errors.password = 'Password is required';
    return errors;
  };

  const handleSubmitUser = async () => {
    const errors = validateForm();
    setFormErrors(errors);
    if (Object.keys(errors).length > 0) return;

    setLoading(true);

    try {
      if (currentUser) {
        // Update existing user - remove password if empty
        const dataToSend = { ...formData };
        if (!dataToSend.password) {
          delete dataToSend.password;
        }

        const response = await axios.put(
          getApiUrl(`/admin/users/${currentUser.id}`),
          dataToSend,
          {
            headers: {
              'Authorization': `Bearer ${currentAuthUser.token}`
            }
          }
        );

        // Update users list with the updated user
        if (Array.isArray(users)) {
          const updatedUsers = users.map(u =>
            u.id === currentUser.id ? response.data : u
          );
          setUsers(updatedUsers);
        } else {
          console.warn('users is not an array when updating user');
          setUsers([response.data]);
        }

        setSnackbar({
          open: true,
          message: 'User updated successfully',
          severity: 'success'
        });
      } else {
        // Create new user
        const response = await axios.post(
          getApiUrl('/admin/users'),
          formData,
          {
            headers: {
              'Authorization': `Bearer ${currentAuthUser.token}`
            }
          }
        );

        // Add new user to the list
        if (Array.isArray(users)) {
          setUsers([...users, response.data]);
        } else {
          console.warn('users is not an array when adding new user');
          setUsers([response.data]);
        }

        setSnackbar({
          open: true,
          message: 'User created successfully',
          severity: 'success'
        });
      }

      setOpenUserForm(false);
    } catch (err) {
      console.error('Error saving user:', err);
      setSnackbar({
        open: true,
        message: err.response?.data?.message || `Failed to ${currentUser ? 'update' : 'create'} user. Please try again.`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Delete user
  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    setLoading(true);

    try {
      await axios.delete(
        getApiUrl(`/admin/users/${userToDelete.id}`),
        {
          headers: {
            'Authorization': `Bearer ${currentAuthUser.token}`
          }
        }
      );

      // Remove deleted user from the list
      if (Array.isArray(users)) {
        const filteredUsers = users.filter(u => u.id !== userToDelete.id);
        setUsers(filteredUsers);
      } else {
        console.warn('users is not an array when deleting user');
        setUsers([]);
      }

      setSnackbar({
        open: true,
        message: 'User deleted successfully',
        severity: 'success'
      });
    } catch (err) {
      console.error('Error deleting user:', err);
      setSnackbar({
        open: true,
        message: err.response?.data?.message || 'Failed to delete user. Please try again.',
        severity: 'error'
      });
    } finally {
      setLoading(false);
      setOpenDeleteDialog(false);
      setUserToDelete(null);
    }
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  // Get role labels for display
  const getRoleLabel = (roleName) => {
    const role = AVAILABLE_ROLES.find(r => r.name === roleName);
    return role ? role.label : roleName;
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" component="h2">
          Manage Users
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddUser}
        >
          Add User
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading && !openUserForm && !openDeleteDialog ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Username</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Roles</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.isArray(users) && users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{`${user.firstName || ''} ${user.lastName || ''}`}</TableCell>
                  <TableCell>
                    {user.roles && (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {Array.from(new Set(user.roles.map(role => role.role)))
                          .map((uniqueRole, index) => (
                            <Chip
                              key={index}
                              label={getRoleLabel(uniqueRole)}
                              size="small"
                            />
                            ))}
                      </Box>
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={user.isActive ? 'Active' : 'Inactive'}
                      color={user.isActive ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="right">
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleEditUser(user)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteClick(user)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              {(!Array.isArray(users) || users.length === 0) && (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    No users found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* User Form Dialog */}
      <Dialog
        open={openUserForm}
        onClose={() => setOpenUserForm(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {currentUser ? 'Edit User' : 'Add New User'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="Username"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              disabled={currentUser !== null}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="Email Address"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
            />
            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                margin="normal"
                required
                fullWidth
                id="firstName"
                label="First Name"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                id="lastName"
                label="Last Name"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
              />
            </Box>
            <FormControl fullWidth margin="normal">
              <InputLabel id="roles-label">Roles *</InputLabel>
              <Select
                labelId="roles-label"
                id="roles"
                multiple
                value={Array.from(new Set(formData.roles.map(r => r.role)))}
                onChange={handleRoleChange}
                renderValue={(selected) => {
                  // Remove duplicates from selected values
                  const uniqueSelected = Array.from(new Set(selected));
                  return (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {uniqueSelected.map((value) => (
                        <Chip
                          key={value}
                          label={getRoleLabel(value)}
                          size="small"
                        />
                      ))}
                    </Box>
                  );
                }}
              >
                {AVAILABLE_ROLES.map((role) => (
                  <MenuItem key={role.id} value={role.name}>
                    {role.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Cost Center selection for Cost Center Head roles */}
            {formData.roles.some(role => role.role === 'cc_head') && (
              <FormControl fullWidth margin="normal" key="cc-select">
                <InputLabel id="cost-center-label">Cost Centers</InputLabel>
                <Select
                  labelId="cost-center-label"
                  id="costCenter"
                  value={selectedCostCenters}
                  onChange={(e) => handleCostCenterChange(e, 0)}
                  label="Cost Centers"
                  required
                  multiple
                  renderValue={(selected) => {
                    if (selected.length === 0) return 'Select Cost Centers';
                    
                    // Find the cost center names for the selected IDs
                    const selectedCenters = costCenters.filter(cc => selected.includes(cc.id));
                    
                    return (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selectedCenters.map((cc) => (
                          <Chip key={cc.id} label={cc.name} size="small" />
                        ))}
                      </Box>
                    );
                  }}
                >
                  {costCenters.map((cc) => (
                    <MenuItem
                      key={cc.id}
                      value={cc.id}
                      selected={selectedCostCenters.includes(cc.id.toString())}
                    >
                      {cc.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}

            <FormControl fullWidth margin="normal">
              <InputLabel id="status-label">Status</InputLabel>
              <Select
                labelId="status-label"
                id="isActive"
                name="isActive"
                value={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.value })}
              >
                <MenuItem value={true}>Active</MenuItem>
                <MenuItem value={false}>Inactive</MenuItem>
              </Select>
            </FormControl>
            <TextField
              margin="normal"
              required={!currentUser}
              fullWidth
              id="password"
              label="Password"
              name="password"
              type="password"
              value={formData.password}
              onChange={handleInputChange}
              helperText={currentUser ? "Leave blank to keep current password" : "Required for new users"}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenUserForm(false)}>Cancel</Button>
          <Button
            onClick={handleSubmitUser}
            variant="contained"
            disabled={loading || Object.keys(validateForm()).length > 0}
          >
            {loading ? <CircularProgress size={24} /> : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete user "{userToDelete?.username}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteUser}
            color="error"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default UsersPanel;
