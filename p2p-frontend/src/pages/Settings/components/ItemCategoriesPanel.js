import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Typography,
  Alert,
  Snackbar,
  CircularProgress,
  DialogContentText,
  Switch,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import apiService from '../../../services/apiService';
import {getCategoryTypeLabel, AVAILABLE_ITEM_CATEGORIES_TYPE} from '../../../constants/constants';


const ItemCategoriesPanel = () => {
  // State for item categories data
  const [itemCategories, setItemCategories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // State for item category form
  const [openForm, setOpenForm] = useState(false);
  const [currentItem, setCurrentItem] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: '',
    isActive: true
  });
  
  // State for delete confirmation
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  
  // State for notifications
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });
  
  // Load item categories on component mount
  useEffect(() => {
    fetchItemCategories();
  }, []);
  
  // Fetch item categories from API
  const fetchItemCategories = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await apiService.itemCategoryService.getAllItemCategories();
      
      // Ensure itemCategories is always an array
      const itemCategoriesData = Array.isArray(data) ? data : 
                           (data && data.itemCategories ? data.itemCategories : []);
      
      console.log('Item categories data:', itemCategoriesData);
      setItemCategories(itemCategoriesData);
    } catch (err) {
      console.error('Error fetching item categories:', err);
      setError('Failed to load item categories. Please try again.');
      // Initialize with empty array on error
      setItemCategories([]);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, checked } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'isActive' ? checked : value
    });
  };

  const handleItemCategoryTypeChange = (e) => {
    setFormData({
      ...formData,
      type: e.target.value
    });
  };
  
  // Open form for creating a new item category
  const handleAddItem = () => {
    setCurrentItem(null);
    setFormData({
      name: '',
      description: '',
      type: '',
      isActive: true
    });
    setOpenForm(true);
  };
  
  // Open form for editing an existing item category
  const handleEditItem = (item) => {
    setCurrentItem(item);
    setFormData({
      name: item.name,
      description: item.description || '',
      type: item.type || '',
      isActive: item.isActive
    });
    setOpenForm(true);
  };
  
  // Open delete confirmation dialog
  const handleDeleteClick = (item) => {
    setItemToDelete(item);
    setOpenDeleteDialog(true);
  };
  
  // Submit item category form (create or update)
  const handleSubmit = async () => {
    setLoading(true);
    
    try {
      if (currentItem) {
        // Update existing item category
        const response = await apiService.itemCategoryService.updateItemCategory(
          currentItem.id, 
          formData
        );
        
        // Update item categories list with the updated item
        const updatedItems = itemCategories.map(item => 
          item.id === currentItem.id ? response : item
        );
        setItemCategories(updatedItems);
        
        setSnackbar({
          open: true,
          message: 'Item category updated successfully',
          severity: 'success'
        });
      } else {
        // Create new item category
        const response = await apiService.itemCategoryService.createItemCategory(formData);
        
        // Add new item category to the list
        setItemCategories([...itemCategories, response]);
        
        setSnackbar({
          open: true,
          message: 'Item category created successfully',
          severity: 'success'
        });
      }
      
      // Close the form dialog
      setOpenForm(false);
    } catch (err) {
      console.error('Error saving item category:', err);
      setSnackbar({
        open: true,
        message: `Failed to save item category: ${err.response?.data?.message || err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Delete item category
  const handleDelete = async () => {
    if (!itemToDelete) return;
    
    setLoading(true);
    
    try {
      await apiService.itemCategoryService.deleteItemCategory(itemToDelete.id);
      
      // Remove deleted item from the list
      setItemCategories(itemCategories.filter(item => item.id !== itemToDelete.id));
      
      setSnackbar({
        open: true,
        message: 'Item category deleted successfully',
        severity: 'success'
      });
      
      // Close the delete dialog
      setOpenDeleteDialog(false);
    } catch (err) {
      console.error('Error deleting item category:', err);
      setSnackbar({
        open: true,
        message: `Failed to delete item category: ${err.response?.data?.message || err.message}`,
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };
  
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Manage Item Categories</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddItem}
          disabled={loading}
        >
          Add Category
        </Button>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      {loading && !itemCategories.length ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Item Name & Description</TableCell>
                <TableCell>Type</TableCell>
                <TableCell align="center">Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {itemCategories.map(item => (
                <TableRow key={item.id}>
                  <TableCell>{item.name}</TableCell>
                  <TableCell>{item.description || '-'}</TableCell>
                  <TableCell>{getCategoryTypeLabel(item.type) || '-'}</TableCell>
                  <TableCell align="center">
                    {item.isActive ? (
                      <Typography variant="body2" color="primary">Active</Typography>
                    ) : (
                      <Typography variant="body2" color="text.secondary">Inactive</Typography>
                    )}
                  </TableCell>
                  <TableCell align="right">
                    <IconButton 
                      size="small" 
                      color="primary"
                      onClick={() => handleEditItem(item)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton 
                      size="small" 
                      color="error"
                      onClick={() => handleDeleteClick(item)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
              {itemCategories.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    No item categories found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}
      
      {/* Item Category Form Dialog */}
      <Dialog 
        open={openForm} 
        onClose={() => setOpenForm(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {currentItem ? 'Edit Item Category' : 'Add New Item Category'}
        </DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="name"
              label="Category Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              inputProps={{ maxLength: 100 }}
            />
            <TextField
              margin="normal"
              fullWidth
              id="description"
              label="Description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              multiline
              rows={3}
              inputProps={{ maxLength: 255 }}
            />
            <FormControl fullWidth margin="normal">
              <InputLabel id="type-label">Type</InputLabel>
              <Select
                labelId="type-label"
                id="type"
                value={formData.type}
                onChange={handleItemCategoryTypeChange}
                displayEmpty
                renderValue={(selected) => {
                  return getCategoryTypeLabel(selected);
                }}
              >
                {AVAILABLE_ITEM_CATEGORIES_TYPE.map((category) => (
                  <MenuItem key={category.id} value={category.name}>
                    {category.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  name="isActive"
                  color="primary"
                />
              }
              label="Active"
              sx={{ mt: 1 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenForm(false)}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained"
            disabled={loading || !formData.name}
          >
            {loading ? <CircularProgress size={24} /> : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={() => setOpenDeleteDialog(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete item category "{itemToDelete?.name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleDelete} 
            color="error"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ItemCategoriesPanel;
