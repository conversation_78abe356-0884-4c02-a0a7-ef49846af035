import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  Paper,
  Alert,
  Container,
  CircularProgress,
  Typography,
  Divider,
  Button,
} from '@mui/material';
import { useAuth } from '../../context/AuthContext';
import apiService from '../../services/apiService';
import InvoiceDetails from '../../components/Invoice/InvoiceDetails';
import InvoiceApprovalCard from '../../components/Invoice/InvoiceApprovalCard';
import POApprovalHistory from '../../components/PODetail/POApprovalHistory';
import PODetailInfo from '../../components/PODetail/PODetailInfo';
import POItemsTable from '../../components/PODetail/POItemsTable';
import { Description as DescriptionIcon } from '@mui/icons-material';


const InvoiceDetailsPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [invoice, setInvoice] = useState(null);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [actionError, setActionError] = useState(null);
  const [actionSuccess, setActionSuccess] = useState(null);

  // Fetch invoice, and use prId from API response to fetch timeline
  const fetchDetails = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await apiService.getInvoiceById(id);
      console.log('[fetchDetails] Invoice API response:', res);
      const invoiceData = res.data.invoice || res.data;
      setInvoice(invoiceData);
      
      // No need for debugging in production code

    } catch (e) {
      console.error('[fetchDetails] Error fetching invoice details:', e);
      setError(e?.message || (e?.response?.data?.message) || 'Failed to fetch invoice details');
    }
    setLoading(false);
  };


  useEffect(() => {
    fetchDetails();
    // eslint-disable-next-line
  }, [id]);

  // Get auth context for the user object and roles
  const { user } = useAuth();

  // Check if current user has the required role for approval
  const hasRequiredRole = (role) => {
    if (!role || !user?.roles?.length) return false;
    
    // Special case: admin has all permissions
    const isAdmin = user.roles.some(r => {
      const roleVal = (typeof r === 'object' ? r.role : r);
      return roleVal?.toLowerCase() === 'admin';
    });
    
    if (isAdmin) {
      console.log('[DEBUG] User is admin, granting all role access');
      return true;
    }
    
    // Map of equivalent roles (lowercase)
    const roleEquivalents = {
      'finance': ['finance', 'fin', 'finance_manager'],
      'biz_fin': ['biz_fin', 'business_finance', 'bizfin', 'biz-fin'],
      'business_finance': ['biz_fin', 'business_finance', 'bizfin', 'biz-fin']
    };
    
    // Get the lowercase role to check
    const roleLowerCase = role.toLowerCase();
    
    // Get all possible equivalent roles that would satisfy this requirement
    const acceptableRoles = roleEquivalents[roleLowerCase] || [roleLowerCase];
    console.log('[DEBUG] Acceptable roles for', roleLowerCase, ':', acceptableRoles);
    
    return user.roles.some(r => {
      // Some roles might be in format {role: 'ROLE_NAME'}, others might be just strings
      const userRole = (typeof r === 'object' ? (r.role || '') : r).toLowerCase();
      const hasRole = acceptableRoles.includes(userRole);
      if (hasRole) console.log('[DEBUG] Found matching role:', userRole);
      return hasRole;
    });
  };

  const handleApprovalAction = async (status, remarks) => {
    setActionLoading(true);
    setActionError(null);
    setActionSuccess(null);
    try {
      // Determine which API to call based on the role that's allowed to approve
      const approvalRole = invoice.showInvoiceVerificationToRole;
      
      if (status === 'APPROVED') {
        if (approvalRole?.toLowerCase() === 'finance') {
          await apiService.finApproveInvoice(id, remarks);
          setActionSuccess('Finance approval successful');
        } else if (approvalRole?.toLowerCase() === 'biz_fin') {
          await apiService.bizFinApproveInvoice(id, remarks);
          setActionSuccess('Business Finance approval successful');
        } else {
          await apiService.approveInvoice(id, remarks);
          setActionSuccess('Invoice approved successfully.');
        }
      } else if (status === 'REJECTED') {
        await apiService.rejectInvoice(id, remarks);
        setActionSuccess('Invoice rejected successfully.');
      }
      
      await fetchDetails();
    } catch (e) {
      console.error('Approval error:', e);
      setActionError(e.message || 'Failed to update invoice approval.');
    }
    setActionLoading(false);
  };

  if (loading) {
    return <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}><CircularProgress /></Box>;
  }
  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }
  if (!invoice) {
    return <Alert severity="info">No invoice found.</Alert>;
  }

  return (
<Container maxWidth="xl" sx={{ py: 4 }}>
  <Box sx={{ display: { xs: 'block', md: 'flex' }, gap: 4, mt: 2, width: '100%' }}>
    {/* Left: Invoice Info and PO Details */}
    <Box sx={{ flexBasis: { md: '60%' }, width: '100%' }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        <InvoiceDetails invoice={invoice} onBack={() => navigate('/invoices')} />

        {invoice.poDetails && (
          <Paper elevation={2} sx={{ mb: 3, p: 2, borderRadius: 2, mt: 2 }}>
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Purchase Order Details
              </Typography>
              <POItemsTable items={invoice.poDetails.items} currency={invoice.poDetails.currency} />
            </Box>
            <Box sx={{ mt: 2, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                size="small"
                startIcon={<DescriptionIcon />}
                onClick={() => navigate(`/purchase-orders/${invoice.poDetails.id}`)}
                sx={{ fontWeight: 'bold', textTransform: 'none' }}
              >
                View PO #{invoice.poDetails.id}
              </Button>
              <Button
                variant="contained"
                size="small"
                startIcon={<DescriptionIcon />}
                onClick={() => navigate(`/approval-detail/${invoice.poDetails.prId}`)}
                sx={{ fontWeight: 'bold', textTransform: 'none' }}
              >
                View PR #{invoice.poDetails.prId}
              </Button>
            </Box>
          </Paper>
        )}
      </Box>
    </Box>

    {/* Right: Approval History and Actions */}
    <Box sx={{ flexBasis: { md: '40%' }, width: '100%', mt: { xs: 2, md: 15 } }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        <Paper elevation={3} sx={{ p: 3, borderRadius: 3, minHeight: 350 }}>
          <POApprovalHistory prId={invoice.prId} />
        </Paper>

        {invoice.showInvoiceVerificationDialog && hasRequiredRole(invoice.showInvoiceVerificationToRole) && (
          <Paper elevation={3} sx={{ p: 1, borderRadius: 3, mt: 3, display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ p: 1 }}>
              <InvoiceApprovalCard
                invoiceId={invoice.id}
                onApprove={(remarks) => handleApprovalAction('APPROVED', remarks)}
                onReject={(remarks) => handleApprovalAction('REJECTED', remarks)}
                loading={actionLoading}
                approvalRole={invoice.showInvoiceVerificationToRole}
              />
            </Box>
            {actionError && <Alert severity="error" sx={{ mt: 2 }}>{actionError}</Alert>}
            {actionSuccess && <Alert severity="success" sx={{ mt: 2 }}>{actionSuccess}</Alert>}
          </Paper>
        )}
      </Box>
    </Box>
  </Box>
</Container>


  );
};

export default InvoiceDetailsPage;
