import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  InputBase,
  CircularProgress,
  Alert,
  Tooltip,
  Snackbar
} from '@mui/material';
import { 
  Search as SearchIcon, 
  Visibility as VisibilityIcon, 
  DoneAll as DoneAllIcon, 
  Block as BlockIcon, 
  Undo as UndoIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import LaunchIcon from '@mui/icons-material/Launch';
import apiService from '../../services/apiService';
import apiConfig from '../../config/api.config';

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getStatusColor = (status) => {
  switch ((status || '').toLowerCase()) {
    case 'approved':
      return 'success';
    case 'pending':
      return 'warning';
    case 'rejected':
      return 'error';
    default:
      return 'default';
  }
};

const Invoices = () => {
  const navigate = useNavigate();
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const observerRef = useRef(null);
  const loaderRef = useRef(null);
  const PAGE_SIZE = 10;

  // Fetch invoices with pagination support
  const fetchInvoices = useCallback(async (pageNumber = 1, isLoadMore = false) => {
    try {
      if (pageNumber === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }
      setError(null);

      // Make API call with pagination parameters
      const response = await apiService.getInvoices({
        page: pageNumber,
        limit: PAGE_SIZE
      });

      // Check if response has data
      if (response && response.data && response.data.invoices) {
        if (isLoadMore) {
          setInvoices(prevInvoices => [...prevInvoices, ...response.data.invoices]);
        } else {
          setInvoices(response.data.invoices || []);
        }
        
        // Determine if there are more items to load
        setHasMore(response.data.invoices.length === PAGE_SIZE);
      } else {
        if (!isLoadMore) {
          setInvoices([]);
        }
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error fetching invoices:', error);
      setError('Failed to fetch invoices. Please try again later.');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, []);

  // Function to load more items
  const loadMoreItems = useCallback(() => {
    if (hasMore && !loadingMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchInvoices(nextPage, true);
    }
  }, [hasMore, loadingMore, page, fetchInvoices]);

  // Initial data load
  useEffect(() => {
    setPage(1);
    fetchInvoices(1, false);
  }, [fetchInvoices]);

  // Intersection Observer setup for infinite scrolling
  useEffect(() => {
    const handleObserver = (entries) => {
      const [entry] = entries;
      if (entry.isIntersecting && hasMore && !loading && !loadingMore) {
        loadMoreItems();
      }
    };

    // Cleanup previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Create new observer
    observerRef.current = new IntersectionObserver(handleObserver, {
      rootMargin: '0px 0px 200px 0px',
      threshold: 0.1
    });

    // Observe the loader element if it exists
    if (loaderRef.current) {
      observerRef.current.observe(loaderRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loading, loadingMore, loadMoreItems]);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    // Debounce search reset
    const handler = setTimeout(() => {
      setPage(1);
      fetchInvoices(1, false);
    }, 500);
    return () => clearTimeout(handler);
  };

  const handleInvoiceStatusChange = async (invoiceId, status) => {
    if (!window.confirm(`Are you sure you want to mark this invoice as ${status.toLowerCase()}?`)) {
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      await apiService.updateInvoiceStatus(
        invoiceId, 
        status, 
        `Marked as ${status.toLowerCase()}`
      );
      
      // Update local state to reflect the change
      setInvoices(prevInvoices => 
        prevInvoices.map(inv => 
          inv.id === invoiceId 
            ? { ...inv, status: status } 
            : inv
        )
      );
      
      setSuccess(`Invoice marked as ${status.toLowerCase()} successfully`);
    } catch (error) {
      setError(error.response?.data?.message || `Failed to mark invoice as ${status.toLowerCase()}`);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteInvoice = async (invoiceId) => {
    if (!window.confirm(`Are you sure you want to delete this invoice?`)) {
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      await apiService.deleteInvoice(invoiceId);
      
      // Update local state to reflect the change
      setInvoices(prevInvoices => 
        prevInvoices.filter(inv => inv.id !== invoiceId)
      );
      
      setSuccess(`Invoice deleted successfully`);
    } catch (error) {
      setError(error.response?.data?.message || `Failed to delete invoice`);
    } finally {
      setLoading(false);
    }
  };

  const filteredInvoices = invoices.filter(inv =>
    inv.invoiceNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    inv.vendorName?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCloseError = () => setError(null);
  const handleCloseSuccess = () => setSuccess(null);

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>Invoices</Typography>
      
      {/* Success and Error Notifications */}
      <Snackbar
        open={!!success}
        autoHideDuration={5000}
        onClose={handleCloseSuccess}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSuccess} severity="success" sx={{ width: '100%' }}>
          {success}
        </Alert>
      </Snackbar>
      
      <Snackbar
        open={!!error}
        autoHideDuration={5000}
        onClose={handleCloseError}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseError} severity="error" sx={{ width: '100%' }}>
          {error}
        </Alert>
      </Snackbar>
      
      <Paper sx={{ p: 2, mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <InputBase
            placeholder="Search by invoice number or vendor"
            value={searchTerm}
            onChange={handleSearch}
            sx={{ ml: 1, flex: 1, border: '1px solid #ddd', borderRadius: 1, px: 1 }}
          />
          <IconButton>
            <SearchIcon />
          </IconButton>
        </Box>
      </Paper>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : (
        <TableContainer component={Paper}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Invoice Number</TableCell>
                <TableCell>PO ID</TableCell>
                <TableCell>Vendor</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Amount</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredInvoices.map((inv) => (
                <TableRow hover key={inv.id}>
                  <TableCell>{inv.id}</TableCell>
                  <TableCell>{inv.invoiceNumber}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {inv.po_id}
                      <IconButton 
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(`/purchase-orders/${inv.po_id}`);
                        }}
                        sx={{ p: 0.5, '&:hover': { color: 'primary.main' } }}
                        title="PO Details"
                      >
                        <LaunchIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </TableCell>
                  <TableCell>{inv.vendorName}</TableCell>
                  <TableCell>{formatDate(inv.invoiceDate)}</TableCell>
                  <TableCell>
                    <Chip label={inv.status} color={getStatusColor(inv.status)} size="small" />
                  </TableCell>
                  <TableCell>₹{parseFloat(inv.invoiceValue || 0).toFixed(2)}</TableCell>
                  <TableCell align="right">
                    <Tooltip title="View Details">
                      <IconButton onClick={() => navigate(`/invoices/${inv.id}`)}>
                        <VisibilityIcon />
                      </IconButton>
                    </Tooltip>
                    {
                      inv.status === 'PENDING' ? (
                        <Tooltip title="Mark as Void">
                          <IconButton onClick={() => handleInvoiceStatusChange(inv.id, 'VOID')}>
                            <BlockIcon />
                          </IconButton>
                        </Tooltip>
                      ) : null
                    }
                    {
                      inv.status === 'VOID' ? (
                        <>
                        <Tooltip title="Mark as Pending">
                          <IconButton onClick={() => handleInvoiceStatusChange(inv.id, 'PENDING')}>
                            <UndoIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Invoice">
                          <IconButton onClick={() => handleDeleteInvoice(inv.id)}>
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                       </>
                      ) : null
                    }
                    {inv.status === 'APPROVED' ? (
                    <Tooltip title="Mark as Payment Complete">
                      <IconButton onClick={() => handleInvoiceStatusChange(inv.id, 'PAYMENT_COMPLETED')}>
                        <DoneAllIcon />
                      </IconButton>
                    </Tooltip>) : null}
                  </TableCell>
                </TableRow>
              ))}
              {/* Loading more indicator at the bottom */}
              {hasMore && (
                <TableRow ref={loaderRef}>
                  <TableCell colSpan={8} align="center" sx={{ border: 'none' }}>
                    {loadingMore ? (
                      <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                        <CircularProgress size={24} />
                      </Box>
                    ) : (
                      <Box sx={{ height: '50px' }} /> // Invisible spacer for the observer
                    )}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default Invoices;
