import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  CircularProgress,
  Toolbar,
  InputBase,
  IconButton,
  Tabs,
  Tab,
  Divider,
  Alert,
  Snackbar,
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Add as AddIcon,
  Block as BlockIcon,
  Undo as UndoIcon
} from '@mui/icons-material';

import apiConfig from '../../config/api.config';
import axiosInstance from '../../services/axiosInstance';
import { PR_STATUS, getStatusDisplay, getStatusColor } from '../../utils/statusConstants';
import apiService from '../../services/apiService';
import { useAuth } from '../../context/AuthContext';

const PurchaseRequests = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [purchaseRequests, setPurchaseRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [snackbar, setSnackbar] = useState({ 
    open: false, 
    message: '', 
    severity: 'success' 
  });
  
  // Status counts state
  const [statusCounts, setStatusCounts] = useState({});
  const [loadingCounts, setLoadingCounts] = useState(true);
  
  // Pagination state
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const ITEMS_PER_PAGE = 10;
  
  // Observer reference for infinite scrolling
  const observer = useRef();

  // Fetch status counts from API
  const fetchStatusCounts = async () => {
    try {
      setLoadingCounts(true);
      const response = await apiService.getPurchaseRequestStatusCounts();
      
      if (response && response.data) {
        // Process the nested response format
        const apiResponse = response.data;
        const counts = {};
        
        // Use the provided total for 'all'
        counts.all = apiResponse.total || 0;
        
        // Process the counts object and convert keys to lowercase
        if (apiResponse.counts) {
          Object.entries(apiResponse.counts).forEach(([key, value]) => {
            // Convert status keys to match our status filter values (lowercase)
            let statusKey = key.toLowerCase();
            
            // Special handling for 'pending approval' -> 'pending'
            if (statusKey === 'pending approval') {
              statusKey = 'pending';
            }
            
            counts[statusKey] = value;
          });
        }
        
        setStatusCounts(counts);
      }
    } catch (error) {
      console.error('Error fetching status counts:', error);
    } finally {
      setLoadingCounts(false);
    }
  };

  const fetchData = async (pageNum = 0, isLoadingMore = false) => {
    try {
      if (isLoadingMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      // Fetch purchase requests with pagination
      const offset = pageNum * ITEMS_PER_PAGE;
      const response = await axiosInstance.get(apiConfig.endpoints.purchaseRequests.list, {
        params: { limit: ITEMS_PER_PAGE, offset }
      });

      // Check if response.data.data exists (common API pattern)
      const purchaseRequestsData = response.data?.data || response.data || [];
      const prData = Array.isArray(purchaseRequestsData) ? purchaseRequestsData : [];
      
      // Collect all items with missing category details
      const itemsWithMissingCategories = [];
      prData.forEach(pr => {
        if (pr.items && pr.items.length > 0) {
          pr.items.forEach(item => {
            if (item.itemCategoryId && !item.itemCategory) {
              itemsWithMissingCategories.push({
                prId: pr.id,
                itemId: item.id,
                categoryId: item.itemCategoryId
              });
            }
          });
        }
      });
      
      // If there are items with missing categories, fetch them
      if (itemsWithMissingCategories.length > 0) {
        try {
          // Create a Set of unique category IDs to fetch
          const uniqueCategoryIds = [...new Set(
            itemsWithMissingCategories.map(item => item.categoryId)
          )];
          
          // Fetch details for each category ID
          const categoryDetailsPromises = uniqueCategoryIds.map(id => 
            apiService.itemCategoryService.getItemCategoryById(id)
              .catch(err => {
                console.error(`Error fetching category ID ${id}:`, err);
                return null;
              })
          );
          
          const categoryDetails = await Promise.all(categoryDetailsPromises);
          
          // Create a map of category ID to category details
          const categoryMap = {};
          categoryDetails.forEach(category => {
            if (category) {
              categoryMap[category.id] = category;
            }
          });
          
          // Update items with category details
          prData.forEach(pr => {
            if (pr.items && pr.items.length > 0) {
              pr.items = pr.items.map(item => {
                if (item.itemCategoryId && !item.itemCategory && categoryMap[item.itemCategoryId]) {
                  return {
                    ...item,
                    itemCategory: categoryMap[item.itemCategoryId]
                  };
                }
                return item;
              });
            }
          });
        } catch (categoryError) {
          console.error('Error fetching item categories:', categoryError);
        }
      }
      
      // Update hasMore flag based on whether we received fewer items than requested
      setHasMore(prData.length === ITEMS_PER_PAGE);

      if (isLoadingMore) {
        setPurchaseRequests(prev => [...prev, ...prData]);
      } else {
        setPurchaseRequests(prData);
      }
    } catch (error) {
      console.error('Error fetching purchase requests data:', error);
      if (!isLoadingMore) {
        setPurchaseRequests([]); // Set empty array on error only for initial load
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };
  
  // Load more data when user scrolls to the bottom
  const loadMoreData = useCallback(() => {
    if (!loadingMore && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchData(nextPage, true);
    }
  }, [loadingMore, hasMore, page]);
  
  // Setup intersection observer for infinite scroll
  const lastElementCallback = useCallback(node => {
    if (loading || loadingMore) return;
    
    // Disconnect previous observer if it exists
    if (observer.current) observer.current.disconnect();
    
    // Create new observer that calls loadMoreData when the last element is visible
    observer.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        loadMoreData();
      }
    }, { threshold: 0.5 });
    
    // Observe the last element
    if (node) observer.current.observe(node);
  }, [loading, loadingMore, hasMore, loadMoreData]);

  useEffect(() => {
    setPage(0);
    fetchData(0);
    // Reset pagination when filters change
  }, [searchTerm, statusFilter]);
  
  // Fetch status counts on component mount
  useEffect(() => {
    fetchStatusCounts();
  }, []);
  
  // Cleanup observer on component unmount
  useEffect(() => {
    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, []);

  const handleCreatePR = () => {
    navigate('/create-pr');
  };

  const handleViewPR = (prId) => {
    try {
      // Navigate to approval detail page
      navigate(`/approval-detail/${prId}`);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  // Helper function to check if a status is any kind of pending approval
  const isPendingApproval = (status) => {
    return [
      PR_STATUS.PENDING_APPROVAL,
      PR_STATUS.PENDING_CC_HEAD_APPROVAL,
      PR_STATUS.PENDING_BUSINESS_HEAD_APPROVAL,
      PR_STATUS.PENDING_PROCUREMENT_APPROVAL
    ].includes(status);
  };

  // Define status filter options with counts
  const statusOptions = [
    {
      value: 'all',
      label: 'All',
      filter: () => true,
      count: loadingCounts ? 0 : (statusCounts.all || purchaseRequests.length)
    },
    {
      value: 'pending',
      label: 'Pending Approval',
      filter: pr => isPendingApproval(pr.status),
      count: loadingCounts ? 0 : (statusCounts.pending || purchaseRequests.filter(pr => isPendingApproval(pr.status)).length)
    },
    {
      value: 'approved',
      label: 'Approved',
      filter: pr => pr.status === PR_STATUS.APPROVED,
      count: loadingCounts ? 0 : (statusCounts.approved || purchaseRequests.filter(pr => pr.status === PR_STATUS.APPROVED).length)
    },
    {
      value: 'rejected',
      label: 'Rejected',
      filter: pr => pr.status === PR_STATUS.REJECTED,
      count: loadingCounts ? 0 : (statusCounts.rejected || purchaseRequests.filter(pr => pr.status === PR_STATUS.REJECTED).length)
    },
    {
      value: 'completed',
      label: 'Completed',
      filter: pr => pr.status === PR_STATUS.COMPLETED,
      count: loadingCounts ? 0 : (statusCounts.completed || purchaseRequests.filter(pr => pr.status === PR_STATUS.COMPLETED).length)
    },
    {
      value: 'void',
      label: 'Void',
      filter: pr => pr.status === PR_STATUS.VOID,
      count: loadingCounts ? 0 : (statusCounts.void || purchaseRequests.filter(pr => pr.status === PR_STATUS.VOID).length)
    }
  ];

  // Handle status filter change
  const handleStatusFilterChange = (_, newValue) => {
    setStatusFilter(newValue);
  };

  // Filter purchase requests based on search term and status filter
  const filteredRequests = purchaseRequests.filter(pr => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = (
      pr.id.toString().toLowerCase().includes(searchLower) ||
      pr.businessUnit?.name?.toLowerCase().includes(searchLower) ||
      pr.status?.toLowerCase().includes(searchLower) ||
      getStatusDisplay(pr.status).toLowerCase().includes(searchLower) || // Also search by display status
      pr.createdByUser?.name?.toLowerCase().includes(searchLower) ||
      pr.costCenter?.name?.toLowerCase().includes(searchLower)
    );

    // Apply status filter
    const matchesStatus = statusFilter === 'all' ||
      statusOptions.find(option => option.value === statusFilter)?.filter?.(pr) || false;

    return matchesSearch && matchesStatus;
  });

  const handleUpdatePrStatus = async (prId, status) => {
    try {
      setLoading(true);
      await axiosInstance.put(apiConfig.endpoints.purchaseRequests.update(prId), { status });
      setSnackbar({
        open: true,
        message: `Purchase Request - ${prId} ${status === PR_STATUS.VOID ? 'voided' : 'unvoided'} successfully`,
        severity: 'success',
      });
      fetchData();
    } catch (error) {
      setSnackbar({
        open: true,
        message: `Failed to update PR status - ${error?.response?.data?.message || error.message || error}`,
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };

  return (
    <Box sx={{ p: 2 }}>
      {/* Header with title and Create PR button */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2,
        flexWrap: 'wrap',
        gap: 2
      }}>
        <Box>
          <Typography variant="h5" sx={{ mb: 0.5 }}>
            Purchase Requests
          </Typography>
        </Box>

        {/* Create PR Button */}
        <Button
          variant="contained"
          color="primary"
          onClick={handleCreatePR}
          startIcon={<AddIcon sx={{ fontSize: 20 }} />}
          size="large"
          sx={{
            borderRadius: 2,
            px: 3,
            py: 1,
            fontWeight: 'bold',
            boxShadow: 2,
            '&:hover': {
              boxShadow: 4
            }
          }}
        >
          Create PR
        </Button>
      </Box>

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, width: '100%' }}>
        {/* Purchase Requests Table */}
        <Box sx={{ width: '100%' }}>
          <Paper elevation={0} sx={{ width: '100%', overflow: 'hidden', borderRadius: 2, border: '1px solid rgba(0, 0, 0, 0.12)' }}>
            <Toolbar sx={{ pl: { sm: 2 }, pr: { xs: 1, sm: 1 } }}>
              <Box sx={{ flex: '1 1 100%' }} />
              <Box sx={{ display: 'flex', alignItems: 'center', border: '1px solid #ddd', borderRadius: 1, pl: 1 }}>
                <InputBase
                  sx={{ ml: 1, flex: 1, minWidth: '200px' }}
                  placeholder="Search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <IconButton type="button" sx={{ p: '10px' }} aria-label="search">
                  <SearchIcon />
                </IconButton>
              </Box>
            </Toolbar>

            {/* Status Filter Tabs */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: '#fafafa' }}>
              <Tabs
                value={statusFilter}
                onChange={handleStatusFilterChange}
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  px: 2,
                  '& .MuiTabs-indicator': {
                    height: 2,
                    borderRadius: '2px 2px 0 0'
                  }
                }}
              >
                {statusOptions.map((option) => (
                  <Tab
                    key={option.value}
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {option.label}
                        <Chip
                          label={loadingCounts ? '...' : option.count}
                          size="small"
                          color={statusFilter === option.value ? "primary" : "default"}
                          sx={{
                            height: '22px',
                            fontSize: '0.8rem',
                            fontWeight: 'bold',
                            ml: 0.5,
                            minWidth: '28px'
                          }}
                        />
                      </Box>
                    }
                    value={option.value}
                    sx={{
                      textTransform: 'none',
                      fontWeight: statusFilter === option.value ? 'medium' : 'normal',
                      minWidth: 'auto',
                      px: 1.5,
                      py: 1,
                      '&.Mui-selected': {
                        bgcolor: 'rgba(25, 118, 210, 0.04)',
                        borderRadius: '4px 4px 0 0'
                      }
                    }}
                  />
                ))}
              </Tabs>
            </Box>
            <Divider />
            <TableContainer>
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow>
                    <TableCell>PR ID</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Cost Center Details</TableCell>
                    <TableCell>Raised By</TableCell>
                    <TableCell>Amount (₹)</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Remarks</TableCell>
                    <TableCell>Action</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                   {loading ? (
                    <TableRow>
                      <TableCell colSpan={8}>
                        <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                          <CircularProgress />
                        </Box>
                      </TableCell>
                    </TableRow>
                  ) : filteredRequests.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8}>No purchase requests found</TableCell>
                    </TableRow>
                  ) : (
                    filteredRequests.map((pr, index) => (
                      <TableRow 
                        key={pr.id} 
                        hover
                        ref={index === filteredRequests.length - 1 ? lastElementCallback : null}>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {pr.id}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {pr.prType || 'N/A'}
                          </Typography>
                          {pr.items && pr.items.length > 0 && (
                            <Box sx={{ mt: 0.5, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                              {/* Filter distinct item categories with proper null checks */}
                              {pr.items
                                .filter(item => item.itemCategory || item.itemCategoryId)
                                .reduce((unique, item) => {
                                  // Handle both direct itemCategory object and itemCategoryId
                                  const category = item.itemCategory || 
                                    (item.itemCategoryId ? { 
                                      id: item.itemCategoryId, 
                                      name: `Item Category`, 
                                      type: `#${item.itemCategoryId}` 
                                    } : null);
                                  
                                  if (category && !unique.some(cat => cat.id === category.id)) {
                                    unique.push(category);
                                  }
                                  return unique;
                                }, [])
                                .map((category, idx) => (
                                  <Chip
                                    key={idx}
                                    label={category.name && category.type 
                                      ? `${category.name} (${category.type})` 
                                      : category.name || `Item #${category.id}`}
                                    size="small"
                                    color="primary"
                                    variant="outlined"
                                    sx={{
                                      height: '18px',
                                      fontSize: '0.6rem',
                                      '& .MuiChip-label': {
                                        px: 0.5
                                      }
                                    }}
                                  />
                                ))}
                              {pr.items.length > 0 && !pr.items.some(item => item.itemCategory || item.itemCategoryId) && (
                                <Typography variant="caption" color="text.secondary">
                                  No categories
                                </Typography>
                              )}
                            </Box>
                          )}
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {pr.businessUnit?.name || 'N/A'}
                          </Typography>
                          <Typography variant="caption" display="block" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                            {pr.costCenter?.name || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {pr.createdByUser?.name || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {pr.totalValue?.toLocaleString('en-IN') || '0'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={getStatusDisplay(pr.status)}
                            size="small"
                            color={getStatusColor(pr.status)}
                            variant="outlined"
                            sx={{
                              height: '20px',
                              fontSize: '0.65rem',
                              fontWeight: 'medium',
                              '& .MuiChip-label': {
                                px: 0.75
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography
                            variant="body2"
                            sx={{
                              maxWidth: 200,
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis'
                            }}
                          >
                            {pr.remarks || 'N/A'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<VisibilityIcon />}
                            onClick={() => handleViewPR(pr.id)}
                            sx={{ mr: 1 }}
                          >
                            View
                          </Button>
                          {pr.status === PR_STATUS.PENDING_APPROVAL && pr.createdByUser.id === user.id && (
                            <Button
                              variant="outlined"
                              size="small"
                              startIcon={<BlockIcon />}
                              onClick={() => handleUpdatePrStatus(pr.id, PR_STATUS.VOID)}
                            >
                              Void
                            </Button>
                          )}
                          {pr.status === PR_STATUS.VOID && pr.createdByUser.id === user.id && (
                            <Button
                              variant="outlined"
                              size="small"
                              startIcon={<UndoIcon />}
                              onClick={() => handleUpdatePrStatus(pr.id, PR_STATUS.PENDING_APPROVAL)}
                            >
                              Unvoid
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                  {loadingMore && (
                    <TableRow>
                      <TableCell colSpan={8}>
                        <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                          <CircularProgress size={24} />
                        </Box>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            <Snackbar
              open={snackbar.open}
              autoHideDuration={6000}
              onClose={handleCloseSnackbar}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
            >
              <Alert
                onClose={handleCloseSnackbar}
                severity={snackbar.severity}
                sx={{ width: '100%' }}
              >
                {snackbar.message}
              </Alert>
            </Snackbar>
          </Paper>
        </Box>
      </Box>
    </Box>
  );
};

export default PurchaseRequests;
