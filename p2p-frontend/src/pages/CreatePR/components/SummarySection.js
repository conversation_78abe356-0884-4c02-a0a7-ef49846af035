import React from 'react';
import {
  Paper,
  Typography,
  Grid,
  TextField,
  Box,
  InputAdornment
} from '@mui/material';
import DescriptionIcon from '@mui/icons-material/Description';
import { sectionStyles } from '../constants';
import FileUpload from '../../../components/common/FileUpload';

const SummarySection = ({
  justification,
  attachment,
  handleChange,
  handleFileChange,
  fileUploadRef,
  uploadedFiles = []
}) => {
  return (
    <Paper
      variant="outlined"
      sx={{
        ...sectionStyles,
        position: 'relative'
      }}
    >
      <Typography variant="subtitle2" fontWeight="500" color="text.primary" sx={{ mb: 1, fontSize: '0.8rem' }}>
        Summary
      </Typography>

      <Grid container spacing={1.5} alignItems="flex-start">
        {/* Attachment */}
        <Grid item xs={12} md={6}>
          <Typography variant="body2" gutterBottom sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
            Supporting Documents
          </Typography>
          <Box sx={{ border: '1px solid', borderColor: 'divider', borderRadius: 1, p: 1.5 }}>
            <FileUpload
              ref={fileUploadRef}
              multiple={true}
              acceptedFileTypes="image/*,.pdf,.doc,.docx,.xls,.xlsx"
              onFilesSelected={handleFileChange}
              initialFiles={uploadedFiles}
              uploadImmediately={false}
              showPreview={true}
            />
          </Box>
        </Grid>

        {/* Justification */}
        <Grid item xs={12}>
          <TextField
            required
            fullWidth
            multiline
            rows={2}
            size="small"
            label="Justification / Reason"
            name="justification"
            value={justification}
            onChange={handleChange}
            placeholder="Why is this purchase needed? Provide a detailed justification."
            InputProps={{
              startAdornment: (
                <InputAdornment position="start" sx={{ alignSelf: 'flex-start', pt: 0.5, mr: 0.5 }}>
                  <DescriptionIcon sx={{ fontSize: '0.9rem', color: 'primary.main' }} />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiInputLabel-root': {
                fontSize: '0.75rem',
              },
              '& .MuiInputBase-input': {
                fontSize: '0.75rem',
                paddingLeft: '30px',
              }
            }}
          />
        </Grid>
      </Grid>
    </Paper>
  );
};

export default SummarySection;
