import React from 'react';
import {
  <PERSON>,
  Typography,
  Divider,
  Box,
  Button,
  Card,
  CardContent,
  CardActions,
  Chip,
  Avatar,
  IconButton,
  Tooltip
} from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import DescriptionIcon from '@mui/icons-material/Description';
import BusinessIcon from '@mui/icons-material/Business';

// Sample vendor quotes for testing
const sampleQuotes = [
  {
    id: 1,
    vendorName: 'ABC Supplies Inc.',
    uploadDate: '2025-04-15',
    fileName: 'ABC_quote_2025.pdf',
    fileType: 'application/pdf',
    fileSize: '1.2 MB'
  },
  {
    id: 2,
    vendorName: 'XYZ Corporation',
    uploadDate: '2025-04-20',
    fileName: 'XYZ_price_list.docx',
    fileType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    fileSize: '890 KB'
  },
  {
    id: 3,
    vendorName: 'Global Traders Ltd.',
    uploadDate: '2025-04-22',
    fileName: 'global_quote_apr25.pdf',
    fileType: 'application/pdf',
    fileSize: '2.1 MB'
  },
  {
    id: 4,
    vendorName: 'Local Distributors Co.',
    uploadDate: '2025-04-26',
    fileName: 'local_dist_prices.pdf',
    fileType: 'application/pdf',
    fileSize: '1.8 MB'
  }
];

// File icon based on type
const getFileIcon = (fileType) => {
  if (fileType.includes('pdf')) {
    return <PictureAsPdfIcon color="error" />;
  }
  return <DescriptionIcon color="primary" />;
};

const VendorQuotesSection = ({ handleUploadQuote, handleRemoveQuote }) => {
  return (
    <Paper
      variant="outlined"
      sx={{
        p: 3,
        position: 'relative',
        borderColor: 'primary.light',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        width: '100%',
        maxWidth: '100%'
      }}
    >
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <BusinessIcon color="primary" sx={{ mr: 1.5 }} />
          <Box>
            <Typography variant="h6" fontWeight="600" color="primary.main">
              Vendor Quotes
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Upload vendor quotes for price comparison
            </Typography>
          </Box>
        </Box>
        <Chip
          label={`${sampleQuotes.length} quotes`}
          color="primary"
          variant="outlined"
          size="small"
        />
      </Box>
      <Divider sx={{ mb: 3 }} />

      <Box sx={{ 
        overflowX: 'auto',
        flexGrow: 1,
        maxHeight: '350px',
        width: '100%',
        pr: 1,
        mr: -1,
        '&::-webkit-scrollbar': {
          height: '8px',
        },
        '&::-webkit-scrollbar-thumb': {
          backgroundColor: 'rgba(0,0,0,0.2)',
          borderRadius: '4px',
        }
      }}>
        <Box sx={{ display: 'flex', minWidth: 'min-content', width: 'max-content' }}>
          {sampleQuotes.map((quote) => (
            <Box key={quote.id} sx={{ width: '280px', minWidth: '280px', mr: 2, flexShrink: 0 }}>
              <Card elevation={0} sx={{ 
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 1,
              }}>
                <CardContent sx={{ py: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Avatar sx={{ bgcolor: 'primary.light', width: 32, height: 32, mr: 1 }}>
                      {quote.vendorName.charAt(0)}
                    </Avatar>
                    <Typography variant="subtitle1" fontWeight="500">
                      {quote.vendorName}
                    </Typography>
                  </Box>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    {getFileIcon(quote.fileType)}
                    <Box sx={{ ml: 1, flexGrow: 1 }}>
                      <Typography variant="body2" fontWeight="medium" noWrap>
                        {quote.fileName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {quote.fileSize} • {quote.uploadDate}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
                <Divider />
                <CardActions sx={{ justifyContent: 'space-between', px: 2, py: 1 }}>
                  <Tooltip title="Download quote">
                    <IconButton size="small" color="primary">
                      <DownloadIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Remove quote">
                    <IconButton 
                      size="small" 
                      color="error" 
                      onClick={() => handleRemoveQuote && handleRemoveQuote(quote.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </CardActions>
              </Card>
            </Box>
          ))}
        </Box>
      </Box>

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={handleUploadQuote}
          color="primary"
          sx={{
            width: { xs: '100%', sm: 'auto' },
            py: 1,
            px: 3,
            borderRadius: 2,
            borderWidth: 1,
            '&:hover': {
              borderWidth: 1,
              bgcolor: 'primary.light',
              color: 'white'
            }
          }}
        >
          Add Vendor Quote
        </Button>
      </Box>
    </Paper>
  );
};

export default VendorQuotesSection;
