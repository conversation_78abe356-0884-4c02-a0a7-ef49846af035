import React from 'react';
import {
  Box,
  TextField,
  Grid,
  IconButton,
  Chip,
  MenuItem,
  Fade,
  InputAdornment
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import CategoryIcon from '@mui/icons-material/Category';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import TextFieldsIcon from '@mui/icons-material/TextFields';
import DescriptionIcon from '@mui/icons-material/Description';
import { getCategoryTypeLabel } from '../../../constants/constants';
// PercentIcon import removed as GST field is no longer needed

// Import minimalistic styles from constants
import { minimalisticItemStyles } from '../constants';
// GST options import removed as GST field is no longer needed

const ItemRow = ({
  item,
  index,
  handleItemChange,
  handleRemoveItem,
  isRemovable,
  itemCategories,
  unitsOfMeasurement,
  isLastItem
}) => {
  console.log('Item Categories:', itemCategories);
  // Calculate base amount (no tax as GST field is removed)
  const baseAmount = item.quantity * item.pricePerUnit;
  
  // Calculate total amount (without tax since GST is removed)
  const itemTotal = baseAmount;

  

  return (
    <Fade in={true} timeout={300}>
      <Box sx={{
        ...minimalisticItemStyles,
        mb: isLastItem ? 0 : 1.5,
        borderColor: itemTotal > 0 ? '#bbdefb' : '#e0e0e0',
      }}>
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 1,
          pb: 0.5,
          width: '100%',
          borderBottom: '1px solid #f5f5f5'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Chip
              label={`Item ${index + 1}`}
              color="primary"
              size="small"
              sx={{
                height: '20px',
                mr: 1,
                '& .MuiChip-label': {
                  fontSize: '0.65rem',
                  px: 1,
                  fontWeight: 500
                }
              }}
            />
            {itemTotal > 0 && (
              <Chip
                label={`${itemTotal.toFixed(2)}`}
                color="success"
                variant="outlined"
                size="small"
                sx={{
                  height: '20px',
                  '& .MuiChip-label': {
                    fontSize: '0.65rem',
                    px: 1
                  }
                }}
              />
            )}
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {/* Delete Item Button - Always visible */}
            <IconButton
              size="small"
              color="error"
              onClick={() => handleRemoveItem(index)}
              sx={{
                padding: 0.5,
                mr: 0.5,
                visibility: isRemovable ? 'visible' : 'hidden',
                '&:hover': {
                  bgcolor: 'rgba(211, 47, 47, 0.08)'
                }
              }}
              title="Delete item"
            >
              <DeleteOutlineIcon sx={{ fontSize: '1rem' }} />
            </IconButton>
            
            {/* Close Button (Cancel) */}
            <IconButton
              size="small"
              color="default"
              onClick={() => handleRemoveItem(index)}
              sx={{
                padding: 0.5,
                '&:hover': {
                  bgcolor: 'rgba(0, 0, 0, 0.04)'
                },
                display: !isRemovable ? 'flex' : 'none'
              }}
            >
              <CloseIcon sx={{ fontSize: '0.9rem' }} />
            </IconButton>
          </Box>
        </Box>

        <Grid container spacing={1.5}>
          {/* All fields in a single row */}
          <Grid item xs={12}>
            <Grid container spacing={1.5}>
              {/* Item Category */}
              <Grid item xs={6} sm={1.6}>
                <TextField
                  select
                  required
                  fullWidth
                  size="small"
                  label="Category"
                  name="itemCategoryId"
                  value={item.itemCategoryId}
                  onChange={(e) => handleItemChange(index, e)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <CategoryIcon sx={{ fontSize: '1rem', color: 'primary.main' }} />
                      </InputAdornment>
                    )
                  }}
                  SelectProps={{
                    MenuProps: {
                      PaperProps: {
                        style: {
                          maxHeight: 250,
                          width: 220
                        },
                      },
                    },
                  }}
                  sx={{
                    '& .MuiInputLabel-root': {
                      fontSize: '0.75rem',
                    },
                    '& .MuiSelect-select': {
                      fontSize: '0.75rem'
                    }
                  }}
                >
                  {itemCategories.map((category) => {
                    console.log('Category:', category); // Debug log
                    return (
                      <MenuItem key={category.id} value={category.id} sx={{ fontSize: '0.75rem' }}>
                        {category.type === '' ? category.name : `${category.name} - ${getCategoryTypeLabel(category.type)}`}
                      </MenuItem>
                    );
                  })}
                </TextField>
              </Grid>

              {/* Item Name - New field */}
              <Grid item xs={6} sm={2.4}>
                <TextField
                  required
                  fullWidth
                  size="small"
                  label="Item Name"
                  name="itemName"
                  value={item.itemName}
                  onChange={(e) => handleItemChange(index, e)}
                  placeholder="Enter item name"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <TextFieldsIcon sx={{ fontSize: '1rem', color: 'primary.main' }} />
                      </InputAdornment>
                    )
                  }}
                  sx={{
                    '& .MuiInputLabel-root': {
                      fontSize: '0.75rem',
                    },
                    '& .MuiInputBase-input': {
                      fontSize: '0.75rem'
                    }
                  }}
                />
              </Grid>

              {/* Item Description */}
              <Grid item xs={12} sm={2.75}>
                <TextField
                  required
                  fullWidth
                  size="small"
                  label="Description"
                  name="itemDescription"
                  value={item.itemDescription}
                  onChange={(e) => handleItemChange(index, e)}
                  placeholder="Description"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <DescriptionIcon sx={{ fontSize: '1rem', color: 'primary.main' }} />
                      </InputAdornment>
                    )
                  }}
                  sx={{
                    '& .MuiInputLabel-root': {
                      fontSize: '0.75rem',
                    },
                    '& .MuiInputBase-input': {
                      fontSize: '0.75rem'
                    }
                  }}
                />
              </Grid>

              {/* Quantity */}
              <Grid item xs={4} sm={0.75}>
                <TextField
                  required
                  fullWidth
                  type="number"
                  size="small"
                  label="Qty"
                  name="quantity"
                  value={item.quantity}
                  onChange={(e) => handleItemChange(index, e)}
                  InputProps={{
                    inputProps: { min: 1 }
                  }}
                  sx={{
                    '& .MuiInputLabel-root': {
                      fontSize: '0.7rem',
                    },
                    '& .MuiInputBase-input': {
                      fontSize: '0.75rem',
                      textAlign: 'center'
                    }
                  }}
                />
              </Grid>

              {/* Unit of Measurement */}
              <Grid item xs={8} sm={1.25}>
                <TextField
                  select
                  required
                  fullWidth
                  size="small"
                  label="UOM"
                  name="uom"
                  value={item.uom}
                  onChange={(e) => handleItemChange(index, e)}
                  InputProps={{}}
                  sx={{
                    '& .MuiInputLabel-root': {
                      fontSize: '0.7rem',
                    },
                    '& .MuiSelect-select': {
                      fontSize: '0.75rem'
                    }
                  }}
                >
                  {unitsOfMeasurement.map((option) => (
                    <MenuItem key={option.value} value={option.value} sx={{ fontSize: '0.75rem' }}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              {/* Price Per Unit */}
              <Grid item xs={6} sm={1.25}>
                <TextField
                  required
                  fullWidth
                  type="number"
                  size="small"
                  label="Price/Unit"
                  name="pricePerUnit"
                  value={item.pricePerUnit}
                  onChange={(e) => handleItemChange(index, e)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <CurrencyRupeeIcon sx={{ fontSize: '1rem', color: 'primary.main' }} />
                      </InputAdornment>
                    ),
                    inputProps: { min: 0, step: '0.01' }
                  }}
                  sx={{
                    '& .MuiInputLabel-root': {
                      fontSize: '0.7rem',
                    },
                    '& .MuiInputBase-input': {
                      fontSize: '0.75rem'
                    }
                  }}
                />
              </Grid>

              {/* Tax Amount Field removed as per requirement */}

              {/* Total Amount (Base + Tax) */}
              <Grid item xs={6} sm={0.75}>
                <TextField
                  fullWidth
                  size="small"
                  label="Total"
                  value={`₹ ${itemTotal.toFixed(2)}`}
                  InputProps={{
                    readOnly: true
                  }}
                  variant="outlined"
                  sx={{
                    '& .MuiInputLabel-root': {
                      fontSize: '0.7rem',
                    },
                    '& .MuiInputBase-input': {
                      fontSize: '0.75rem',
                      color: 'success.main',
                      fontWeight: 500
                    }
                  }}
                />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>
    </Fade>
  );
};

export default ItemRow;
