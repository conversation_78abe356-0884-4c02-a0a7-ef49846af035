import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  InputBase,
  Divider,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Tooltip
} from '@mui/material';
import {
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  LocalShipping as ShippingIcon,
  Event as EventIcon
} from '@mui/icons-material';
import { TabPanel } from '../../components/StatusTabs';
import apiService from '../../services/apiService';

// Helper function to format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Helper function to get status color
const getStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'grn completed':
    case 'open':
      return 'success';
    case 'pending grn':
    case 'pending approval':
      return 'warning';
    case 'cancelled':
    case 'rejected':
      return 'error';
    case 'closed':
      return 'info';
    case 'void':
      return 'error';
    default:
      return 'default';
  }
};

const PurchaseOrders = () => {
  const navigate = useNavigate();
  const [purchaseOrders, setPurchaseOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [statusCounts, setStatusCounts] = useState({});
  const [loadingCounts, setLoadingCounts] = useState(true);
  const observerRef = useRef(null);
  const loaderRef = useRef(null);
  const PAGE_SIZE = 10;

  // Define status groups for tabs
  const statusGroups = {
    all: { label: 'All', filter: () => true },
    open: { label: 'Open', filter: po => po.status?.toLowerCase() === 'open' },
    pending: { label: 'Pending', filter: po => po.status?.toLowerCase().includes('pending') },
    closed: { label: 'Closed', filter: po => po.status?.toLowerCase() === 'closed' },
    rejected: { label: 'Rejected', filter: po => ['rejected', 'cancelled'].includes(po.status?.toLowerCase()) },
    void: { label: 'Void', filter: po => po.status?.toLowerCase() === 'void' }
  };

  // Get array of status group keys for tabs
  const statusTabs = Object.keys(statusGroups);

  // Load initial data
  const fetchPurchaseOrders = useCallback(async (pageNumber = 1, isLoadMore = false) => {
    try {
      if (pageNumber === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }
      setError(null);

      // Make API call with pagination parameters
      const response = await apiService.purchaseOrderService.getPurchaseOrders({
        page: pageNumber,
        limit: PAGE_SIZE
      });

      // Check if response has data
      if (response && response.data) {
        if (isLoadMore) {
          setPurchaseOrders(prevOrders => [...prevOrders, ...response.data]);
        } else {
          setPurchaseOrders(response.data || []);
        }
        
        // Determine if there are more items to load
        setHasMore(response.data.length === PAGE_SIZE);
      } else {
        if (!isLoadMore) {
          setPurchaseOrders([]);
        }
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error fetching purchase orders:', error);
      setError('Failed to load purchase orders. Please try again later.');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, []);

  // Function to load more items - memoized to avoid dependency issues
  const loadMoreItems = useCallback(() => {
    if (hasMore && !loadingMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchPurchaseOrders(nextPage, true);
    }
  }, [hasMore, loadingMore, page, fetchPurchaseOrders]);

  // Fetch status counts
  const fetchStatusCounts = useCallback(async () => {
    try {
      setLoadingCounts(true);
      const response = await apiService.purchaseOrderService.getPurchaseOrderStatusCounts();
      
      if (response && response.data) {
        // Process the nested response format
        const apiResponse = response.data;
        const counts = {};
        
        // Use the provided total for 'all'
        counts.all = apiResponse.total || 0;
        
        // Process the counts object and convert keys to lowercase
        if (apiResponse.counts) {
          Object.entries(apiResponse.counts).forEach(([key, value]) => {
            // Convert status keys to match our status groups (lowercase)
            let statusKey = key.toLowerCase();
            
            // Special handling for 'pending approval' -> 'pending'
            if (statusKey === 'pending approval') {
              statusKey = 'pending';
            }
            
            counts[statusKey] = value;
          });
        }
        
        setStatusCounts(counts);
      }
    } catch (error) {
      console.error('Error fetching status counts:', error);
    } finally {
      setLoadingCounts(false);
    }
  }, []);

  // Initial data load
  useEffect(() => {
    setPage(1);
    fetchPurchaseOrders(1, false);
    fetchStatusCounts();
  }, [fetchPurchaseOrders, fetchStatusCounts]);

  // Intersection Observer setup for infinite scrolling
  useEffect(() => {
    const handleObserver = (entries) => {
      const [entry] = entries;
      if (entry.isIntersecting && hasMore && !loading && !loadingMore) {
        loadMoreItems();
      }
    };

    // Cleanup previous observer if it exists
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Create new observer
    observerRef.current = new IntersectionObserver(handleObserver, {
      rootMargin: '0px 0px 200px 0px',
      threshold: 0.1
    });

    // Observe the loader element if it exists
    if (loaderRef.current) {
      observerRef.current.observe(loaderRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loading, loadingMore, loadMoreItems]);

  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
    // Reset pagination when tab changes
    setPage(1);
    fetchPurchaseOrders(1, false);
  };

  // Filter purchase orders based on search term and current tab
  const filteredPurchaseOrders = purchaseOrders
    .filter(statusGroups[statusTabs[tabValue]].filter)
    .filter(po =>
      (po.poNumber?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (po.prId?.toString() || '').includes(searchTerm.toLowerCase()) ||
      (po.businessUnit?.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (po.costCenter?.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (po.status?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
      (po.shipToAddress?.toLowerCase() || '').includes(searchTerm.toLowerCase())
    );

  // Get status count from API or fall back to client-side counting
  const getStatusCount = (statusKey) => {
    // If we have counts from the API and this status exists, use it
    if (!loadingCounts && statusCounts && statusCounts[statusKey] !== undefined) {
      return statusCounts[statusKey];
    }
    // Otherwise fall back to counting client-side
    return purchaseOrders.filter(statusGroups[statusKey].filter).length;
  };

  const handleViewPO = (poId) => {
    navigate(`/purchase-orders/${poId}`);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ maxWidth: 1200, mx: 'auto' }}>
        <Box sx={{ mb: 3 }}>
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            Purchase Orders
          </Typography>
        </Box>

        {/* Search bar */}
        <Paper sx={{ p: '2px 4px', mb: 2, display: 'flex', alignItems: 'center', maxWidth: 400 }}>
          <IconButton sx={{ p: '10px' }} aria-label="search">
            <SearchIcon />
          </IconButton>
          <InputBase
            sx={{ ml: 1, flex: 1 }}
            placeholder="Search purchase orders"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              // Debounce search reset
              const handler = setTimeout(() => {
                setPage(1);
                fetchPurchaseOrders(1, false);
              }, 500);
              return () => clearTimeout(handler);
            }}
            inputProps={{ 'aria-label': 'search purchase orders' }}
          />
        </Paper>

        {/* Error handling */}
        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

        {/* Purchase Orders Table */}
        <Paper sx={{ width: '100%', mb: 2 }}>
          {/* Status tabs */}
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            {statusTabs.map((statusKey, index) => (
              <Tab
                key={statusKey}
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {statusGroups[statusKey].label}
                    <Chip
                      label={loadingCounts ? '...' : getStatusCount(statusKey)}
                      size="small"
                      sx={{ ml: 1, height: 18, fontSize: '0.65rem' }}
                    />
                  </Box>
                }
                value={index}
                sx={{
                  textTransform: 'none',
                  fontWeight: tabValue === index ? 'medium' : 'normal',
                  minWidth: 'auto',
                  px: 1.5,
                  py: 1,
                  borderRadius: '4px 4px 0 0'
                }}
              />
            ))}
          </Tabs>
          <Divider />

          {/* Tab panels */}
          {statusTabs.map((statusKey, index) => (
            <TabPanel key={statusKey} value={tabValue} index={index}>
              <TableContainer>
                <Table sx={{ minWidth: 750 }} aria-labelledby="tableTitle">
                  <TableHead>
                    <TableRow>
                      <TableCell width="10%">PO ID</TableCell>
                      <TableCell width="10%">Identifier</TableCell>
                      <TableCell width="20%">Cost Center Details</TableCell>
                      <TableCell width="20%">Delivery Details</TableCell>
                      <TableCell width="10%">Created Date</TableCell>
                      <TableCell width="10%" align="right">Amount</TableCell>
                      <TableCell width="10%">Status</TableCell>
                      <TableCell width="10%">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {loading && page === 1 ? (
                      <TableRow>
                        <TableCell colSpan={8} align="center">
                          <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                            <CircularProgress />
                          </Box>
                        </TableCell>
                      </TableRow>
                    ) : filteredPurchaseOrders.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} align="center">
                          <Box sx={{ py: 4, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                            <Typography variant="h6" color="text.secondary" gutterBottom>
                              No purchase orders found
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                              {searchTerm
                                ? 'Try adjusting your search criteria'
                                : `No ${statusGroups[statusTabs[tabValue]].label.toLowerCase()} purchase orders available`
                              }
                            </Typography>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredPurchaseOrders.map((po) => (
                        <TableRow key={po.id} hover>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {po.id}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {po.poNumber || 'N/A'}
                            </Typography>
                          </TableCell>

                          {/* Combined Cost Center Details Column */}
                          <TableCell>
                            <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                              <Typography variant="body2" fontWeight="medium">
                                {po.businessUnit?.name || "N/A"}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {po.costCenter?.name || "N/A"}
                              </Typography>
                            </Box>
                          </TableCell>
                          {/* Combined Delivery Column with both Ship To and Expected Delivery */}
                          <TableCell>
                            <Tooltip title={
                              `${po.shipToAddress ? `Ship to: ${po.shipToAddress}` : 'No shipping address specified'}
                              ${po.expectedDeliveryDate ? `\nExpected delivery: ${formatDate(po.expectedDeliveryDate)}` : ''}`
                            }>
                              <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <EventIcon fontSize="small" sx={{ mr: 0.5, color: 'primary.light', opacity: 0.7 }} />
                                  <Typography variant="body2" fontWeight="medium">
                                    {po.expectedDeliveryDate ? formatDate(po.expectedDeliveryDate) : 'No date specified'}
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5, pl: 3 }}>
                                  <ShippingIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary', opacity: 0.7, fontSize: '0.9rem' }} />
                                  <Typography variant="caption" color="text.secondary" 
                                    sx={{ 
                                      maxWidth: 250, 
                                      overflow: 'hidden', 
                                      textOverflow: 'ellipsis', 
                                      whiteSpace: 'nowrap'
                                    }}>
                                    {po.shipToAddress || 'No address specified'}
                                  </Typography>
                                </Box>
                              </Box>
                            </Tooltip>
                          </TableCell>
                          <TableCell>{formatDate(po.createdAt)}</TableCell>
                          <TableCell align="right">
                            <Typography variant="body2" fontWeight="medium">
                              {po.totalAmount ? `₹${po.totalAmount.toLocaleString('en-IN')}` : '-'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={po.status || 'Unknown'}
                              size="small"
                              color={getStatusColor(po.status)}
                              variant="outlined"
                              sx={{
                                height: '20px',
                                fontSize: '0.65rem',
                                fontWeight: 'medium',
                                '& .MuiChip-label': {
                                  px: 0.75
                                }
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="outlined"
                              size="small"
                              startIcon={<VisibilityIcon />}
                              onClick={() => handleViewPO(po.id)}
                            >
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                    {/* Loading more indicator at the bottom */}
                    {hasMore && (
                      <TableRow ref={loaderRef}>
                        <TableCell colSpan={8} align="center" sx={{ border: 'none' }}>
                          {loadingMore ? (
                            <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                              <CircularProgress size={24} />
                            </Box>
                          ) : (
                            <Box sx={{ height: '50px' }} /> // Invisible spacer for the observer
                          )}
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>
          ))}
        </Paper>
      </Box>
    </Box>
  );
};

export default PurchaseOrders;
