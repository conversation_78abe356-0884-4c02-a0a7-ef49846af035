import './App.css';
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import theme from './theme/muiTheme';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { AuthProvider } from './context/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import MainLayout from './components/Layout/MainLayout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import CreatePR from './pages/CreatePR';
import Approvals from './pages/Approvals';
import ApprovalDetail from './pages/ApprovalDetail';
import MyApprovals from './pages/MyApprovals';
import PurchaseRequests from './pages/PurchaseRequests';
import PurchaseOrders from './pages/PurchaseOrders';
import Invoices from './pages/Invoices';
import InvoiceDetails from './pages/Invoices/InvoiceDetails';
import PODetail from './pages/PODetail';
import Settings from './pages/Settings';
import Profile from './pages/Profile';

// Google OAuth Client ID - replace with your actual client ID
const GOOGLE_CLIENT_ID = '178048511656-b30pu3sppgd1pchbfam73u39ftuiiuhh.apps.googleusercontent.com';

// Note: Make sure to add http://localhost:9001 as an authorized redirect URI in Google Cloud Console

function App() {
  return (
    <Router>
      <ThemeProvider theme={theme}>
        <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
          <AuthProvider>
            <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh', bgcolor: 'grey.50' }}>
              <Routes>
                {/* Public routes */}
                <Route path="/login" element={<Login />} />
                {/* Redirect to dashboard by default */}
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                {/* Protected routes */}
                {/* Protected routes with MainLayout */}
                <Route
                  path="/dashboard"
                  element={
                    <ProtectedRoute>
                      <MainLayout>
                        <Dashboard />
                      </MainLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/create-pr"
                  element={
                    <ProtectedRoute>
                      <MainLayout>
                        <CreatePR />
                      </MainLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/approvals"
                  element={<Navigate to="/my-approvals" replace />}
                />
                <Route
                  path="/approval-detail/:prId"
                  element={
                    <ProtectedRoute>
                      <MainLayout>
                        <ApprovalDetail />
                      </MainLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/purchase-orders"
                  element={
                    <ProtectedRoute>
                      <MainLayout>
                        <PurchaseOrders />
                      </MainLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/invoices"
                  element={
                    <ProtectedRoute>
                      <MainLayout>
                        <Invoices />
                      </MainLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/invoices/:id"
                  element={
                    <ProtectedRoute>
                      <MainLayout>
                        <InvoiceDetails />
                      </MainLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/purchase-orders/:poId"
                  element={
                    <ProtectedRoute>
                      <MainLayout>
                        <PODetail />
                      </MainLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/purchase-requests"
                  element={
                    <ProtectedRoute>
                      <MainLayout>
                        <PurchaseRequests />
                      </MainLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/my-approvals"
                  element={
                    <ProtectedRoute>
                      <MainLayout>
                        <MyApprovals />
                      </MainLayout>
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="/my-approvals/:prId"
                  element={
                    <ProtectedRoute>
                      <MainLayout>
                        <ApprovalDetail />
                      </MainLayout>
                    </ProtectedRoute>
                  }
                />
                {/* Admin Settings Route */}
                <Route
                  path="/settings"
                  element={
                    <ProtectedRoute>
                      <MainLayout>
                        <Settings />
                      </MainLayout>
                    </ProtectedRoute>
                  }
                />
                {/* Profile Route */}
                <Route
                  path="/profile"
                  element={
                    <ProtectedRoute>
                      <MainLayout>
                        <Profile />
                      </MainLayout>
                    </ProtectedRoute>
                  }
                />
                {/* Redirect to login by default */}
                <Route path="*" element={<Navigate to="/login" replace />} />
              </Routes>
            </Box>
          </AuthProvider>
        </GoogleOAuthProvider>
      </ThemeProvider>
    </Router>
  );
}

export default App;
