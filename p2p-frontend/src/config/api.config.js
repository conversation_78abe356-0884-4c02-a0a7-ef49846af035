/**
 * API Configuration
 * This file contains environment-specific API configurations
 */

// Environment detection
const ENV = process.env.REACT_APP_ENVIRONMENT || process.env.Environment || 'staging';

// API URLs based on environment
const API_URLS = {
  development: 'http://localhost:5100',
  staging: 'https://apistaging-p2p.vegrow.in',
  production: 'https://api-p2p.vegrow.in'
};

// Velynk service URL
const VELYNK_API_URL = {
  development: 'http://localhost:3000',
  staging: 'https://crmintegration.vegrow.in',
  production: 'https://velynk.vegrow.in',
};

// Select the appropriate API URL based on environment
const API_BASE_URL = API_URLS[ENV] || API_URLS.development;
const VELYNK_BASE_URL = VELYNK_API_URL[ENV] || VELYNK_API_URL.development;

// Log the environment and API URL for debugging
console.log(`Environment: ${ENV}, API URL: ${API_BASE_URL}`);

// API endpoints
const ENDPOINTS = {
  auth: {
    login: '/auth/login',
    googleLogin: '/auth/google-login',
    profile: '/auth/me',
    logout: '/auth/logout'
  },
  admin: {
    users: '/admin/users',
    businessUnits: '/business-units-with-cost-centers',
    costCenters: '/admin/cost-centers'
  },
  purchaseRequests: {
    list: '/purchase-requests',
    detail: (id) => `/purchase-requests/${id}`,
    update: (id) => `/purchase-requests/${id}`,
    approve: (id) => `/purchase-requests/${id}/approve`,
    reject: (id) => `/purchase-requests/${id}/reject`,
    timeline: (id) => `/purchase-requests/${id}/timeline`,
    ccHeadApproval: (id) => `/purchase-requests/${id}/cc-head-approval`,
    bizFinApproval: (id) => `/purchase-requests/${id}/biz-fin-approval`,
    procurementManagerApproval: (id) => `/purchase-requests/${id}/procurement-manager-approval`,
    quotations: (id) => `/purchase-requests/${id}/quotations`,
    statusCounts: '/purchase-requests/status-counts'
  },
  purchaseOrders: {
    list: `${API_BASE_URL}/purchase-orders`,
    detail: (id) => `${API_BASE_URL}/purchase-orders/${id}`,
    create: `${API_BASE_URL}/purchase-orders`,
    update: (id) => `${API_BASE_URL}/purchase-orders/${id}`,
    delete: (id) => `${API_BASE_URL}/purchase-orders/${id}`,
    submitGRN: `${API_BASE_URL}/purchase-orders/grn`,
    updateStatus: (id) => `${API_BASE_URL}/purchase-orders/${id}/status`,
    verifyQuote: (id) => `${API_BASE_URL}/purchase-orders/${id}/verify-quote`,
    timeline: (id) => `${API_BASE_URL}/purchase-orders/${id}/timeline`,
    statusCounts: `${API_BASE_URL}/purchase-orders/status-counts`
  },
  quotations: {
    list: '/quotations',
    detail: (id) => `/quotations/${id}`,
    create: '/quotations',
    update: (id) => `/quotations/${id}`,
    delete: (id) => `/quotations/${id}`,
    approve: (id) => `/quotations/${id}/approve`
  },
  itemCategories: {
    list: '/admin/item-categories',
    detail: (id) => `/admin/item-categories/${id}`,
    create: '/admin/item-categories',
    update: (id) => `/admin/item-categories/${id}`,
    delete: (id) => `/admin/item-categories/${id}`
  },
  invoices: {
    list: '/invoices',
    detail: (id) => `/invoices/${id}`,
    create: '/invoices',
    createWithGRN: '/invoices/bill-with-grn',
    uploadFile: (id) => `/invoices/${id}/upload-file`,
    delete: (id) => `/invoices/${id}`,
    bizFinApproval: (id) => `/invoices/${id}/biz-fin-approval`,
    finApproval: (id) => `/invoices/${id}/fin-approval`
  },
  grns: {
    delete: (id) => `/grns/${id}`
  },
  partners: {
    getPartners: '/partners/get_partners',
    getPartnerDetails: '/partners/get_partner_details'
  },
  velynkAuth:{
    login: '/login'
  }
};

// Create configuration object
const apiConfig = {
  baseUrl: API_BASE_URL,
  velynkUrl: VELYNK_BASE_URL,
  endpoints: ENDPOINTS,
  environment: ENV
};

// Export configuration
export default apiConfig;

// Helper function to get full URL for an endpoint
export const getApiUrl = (endpoint) => `${API_BASE_URL}${endpoint}`;
