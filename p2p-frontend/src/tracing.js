import { getWebAutoInstrumentations } from "@opentelemetry/auto-instrumentations-web";
import { OTLPTraceExporter } from "@opentelemetry/exporter-trace-otlp-http";
import { registerInstrumentations } from "@opentelemetry/instrumentation";
import { resourceFromAttributes } from "@opentelemetry/resources";
import { BatchSpanProcessor } from "@opentelemetry/sdk-trace-base";
import { WebTracerProvider } from "@opentelemetry/sdk-trace-web";
import { SemanticResourceAttributes } from "@opentelemetry/semantic-conventions";

import API_URLS from "./config/api.config.js";
import { env } from "./env.js";

export function initializeTracing() {
  try {
    const TRACING_CONFIG = {
      serviceName: env.REACT_APP_SERVICE_NAME || 'p2p-frontend-test',
      environment: env.REACT_APP_ENVIRONMENT || 'test',
      signozUrl: "https://ingest.in.signoz.cloud:443/v1/traces",
      signozAccessToken: env.REACT_APP_SIGNOZ_ACCESS_TOKEN || "",
    };

    const resource = resourceFromAttributes({
      [SemanticResourceAttributes.SERVICE_NAME]: TRACING_CONFIG.serviceName,
      [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]:
        TRACING_CONFIG.environment,
    });

    const exporter = new OTLPTraceExporter({
      url: TRACING_CONFIG.signozUrl,
      headers: {
        "signoz-access-token": TRACING_CONFIG.signozAccessToken,
      },
    });

    const spanProcessor = new BatchSpanProcessor(exporter, {
      maxQueueSize: 100,
      scheduledDelayMillis: 5000,
      exportTimeoutMillis: 30000,
    });

    const provider = new WebTracerProvider({
      resource,
      spanProcessors: [spanProcessor],
    });

    provider.register();

    const apiUrls = [API_URLS.baseUrl];
    console.log("API URLs:", apiUrls);
    const corsRegexPatterns = apiUrls.map((url) => {
      const trimmedUrl = url.replace(/\/$/, "");
      const regexString = `^${trimmedUrl
        .replace(/\//g, "\\/")
        .replace(/\./g, "\\.")}`;
      return new RegExp(regexString);
    });

    console.log("CORS Regex Patterns:", corsRegexPatterns);
    registerInstrumentations({
      tracerProvider: provider,
      instrumentations: [
        getWebAutoInstrumentations({
          "@opentelemetry/instrumentation-xml-http-request": {
            propagateTraceHeaderCorsUrls: corsRegexPatterns,
            applyCustomAttributesOnSpan: (span) => {
              span.updateName(`GET ${window.location.pathname}`);
            },
          },
          "@opentelemetry/instrumentation-fetch": {
            propagateTraceHeaderCorsUrls: corsRegexPatterns,
          },
        }),
      ],
    });

    // Test tracing
    const tracer = provider.getTracer("test-tracer");
    const testSpan = tracer.startSpan("test-initialization-span");
    testSpan.end();
    return true;
  } catch (error) {
    console.error("Comprehensive Tracing Initialization Error:", {
      message: error.message,
      name: error.name,
      stack: error.stack,
    });
    return false;
  }
}

// Global error tracking
window.addEventListener("error", (event) => {
  console.error("Global Error Tracking:", {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error,
  });
});

window.addEventListener("unhandledrejection", (event) => {
  console.error("Unhandled Promise Rejection:", {
    reason: event.reason,
    promise: event.promise,
  });
});
