import React from 'react';
import { Box, Typography, Grid, Divider } from '@mui/material';

const InfoItem = ({ label, value }) => (
  <Box sx={{ mb: 2 }}>
    <Typography variant="body2" color="text.secondary" gutterBottom>
      {label}
    </Typography>
    <Typography variant="body1" fontWeight="medium">
      {value || 'N/A'}
    </Typography>
  </Box>
);

const InvoiceDetailInfo = ({ invoiceDetails }) => {
  return (
    <>
      <Typography variant="h6" gutterBottom>
        Invoice Information
      </Typography>
      <Divider sx={{ mb: 2 }} />
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={4}>
          <InfoItem label="Vendor" value={invoiceDetails.vendorName} />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <InfoItem label="Business Unit" value={invoiceDetails?.poDetails?.businessUnit || invoiceDetails?.businessUnit} />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <InfoItem label="Cost Center" value={invoiceDetails?.poDetails?.costCenter || invoiceDetails?.costCenter} />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <InfoItem label="Invoice Value" value={invoiceDetails.invoiceValue} />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <InfoItem label="Invoice Date" value={invoiceDetails.invoiceDate ? new Date(invoiceDetails.invoiceDate).toLocaleDateString() : 'N/A'} />
        </Grid>
      </Grid>
    </>
  );
};

export default InvoiceDetailInfo;
