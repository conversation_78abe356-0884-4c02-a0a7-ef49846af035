import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  TextField,
  Grid,
  Paper,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  TableContainer,
  InputAdornment,
  IconButton,
  CircularProgress,
  Alert,
  Divider,
  Select,
  MenuItem,
  FormControl
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ReceiptIcon from '@mui/icons-material/Receipt';
import FileUpload from '../common/FileUpload';
import { formatDate } from '../../utils/dateUtils';

// Define GST rates with parenthesis instead of arrows
const GST_RATES = [
  { label: "0% (No GST)", value: 0 },
  { label: "5% (IGST 5%)", value: 5 },
  { label: "5% (CGST 2.5% + SGST 2.5%)", value: 5, type: 'SPLIT' },
  { label: "12% (IGST 12%)", value: 12 },
  { label: "12% (CGST 6% + SGST 6%)", value: 12, type: 'SPLIT' },
  { label: "18% (IGST 18%)", value: 18 },
  { label: "18% (CGST 9% + SGST 9%)", value: 18, type: 'SPLIT' },
  { label: "28% (IGST 28%)", value: 28 },
  { label: "28% (CGST 14% + SGST 14%)", value: 28, type: 'SPLIT' }
];

/**
 * Dialog component for adding an invoice to a GRN
 * 
 * @param {Object} props Component props
 * @param {boolean} props.open Dialog open state
 * @param {Function} props.onClose Callback when dialog closes
 * @param {Object} props.grn GRN details
 * @param {Function} props.onSubmit Callback when form submits
 */
const InvoiceDialog = ({ open, onClose, grn, onSubmit }) => {
  // State variables
  const [invoiceData, setInvoiceData] = useState({
    grnId: grn?.id || null,
    invoiceNumber: '',
    invoiceDate: new Date(),
    invoiceValue: '',
    remarks: '',
    invoiceItems: [],
    fileKeys: []
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [itemErrors, setItemErrors] = useState({});
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const fileUploadRef = useRef(null); // Add ref for file upload component

  // Initialize invoice items from GRN items
  useEffect(() => {
    if (grn?.items && open) {
      console.log('GRN data for invoice:', grn); // Log GRN data for debugging
      
      // Directly map from the GRN response structure
      const items = grn.items.map(item => {
        // Get poItem directly from the item
        const poItem = item.poItem;
        
        // Use the exact fields from the response
        const quantity = item.grnQuantity;
        
        // Extract unit price correctly from poItem
        // If pricePerUnit exists in poItem, use it, otherwise check for unitPrice
        const unitPrice = poItem.pricePerUnit !== undefined ? 
          poItem.pricePerUnit : 
          (poItem.unitPrice !== undefined ? poItem.unitPrice : 100);
        
        // Calculate amount based on unit price and quantity
        const amount = unitPrice * quantity;
        
        // Get GST percentage directly from poItem
        const gstPercentage = poItem.gstPercentage || 0;
        
        // Calculate tax amount based on amount and GST percentage
        const taxAmount = (amount * gstPercentage) / 100;
        
        console.log('Processing item:', {
          itemName: poItem.itemName,
          unitPrice: unitPrice,
          quantity: quantity,
          amount: amount,
          gstPercentage: gstPercentage,
          taxAmount: taxAmount
        });
        
        // Determine GST type (SPLIT or regular IGST) based on common practices
        // Default to regular IGST if not specified
        const gstType = 'IGST';
        const gstOption = `${gstPercentage}_${gstType}`;
        
        return {
          grnItemId: item.id,
          poItemId: poItem.id,
          itemName: poItem.itemName,
          itemDescription: poItem.itemDescription || '',
          uom: poItem.uom,
          quantity: quantity,
          unitPrice: unitPrice,
          amount: amount,
          taxRate: gstPercentage,
          gstPercentage: gstPercentage,
          gstType: gstType,
          gstOption: gstOption,
          tax: taxAmount
        };
      });
      
      // Calculate total invoice value
      const totalValue = items.reduce((sum, item) => sum + item.amount + item.tax, 0);
      
      console.log('Calculated invoice items:', items);
      console.log('Total invoice value:', totalValue);
      
      setInvoiceData(prev => ({
        ...prev,
        grnId: grn.id,
        invoiceItems: items,
        invoiceValue: totalValue.toFixed(2),
        fileKeys: []
      }));
      
      // Reset form state
      setError('');
      setSuccess(false);
      setUploadedFiles([]);
      setItemErrors({});
    }
  }, [grn, open]);

  // Handle text field changes
  const handleChange = (field, value) => {
    setInvoiceData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Calculate tax amount based on amount and tax rate
  const calculateTax = (amount, taxRate) => {
    return (parseFloat(amount || 0) * parseFloat(taxRate || 0)) / 100;
  };

  // Handle item field changes
  const handleItemChange = (index, field, value) => {
    const updatedItems = [...invoiceData.invoiceItems];
    const item = { ...updatedItems[index] };
    
    // Convert to number if needed
    if (field === 'amount' || field === 'tax' || field === 'quantity' || field === 'taxRate') {
      value = parseFloat(value) || 0;
    }
    
    // Update the field
    item[field] = value;
    
    // Recalculate tax when amount or tax rate changes
    if (field === 'amount' || field === 'taxRate') {
      item.tax = calculateTax(
        field === 'amount' ? value : item.amount,
        field === 'taxRate' ? value : item.taxRate
      );
    }
    
    updatedItems[index] = item;
    
    // Validate quantity
    if (field === 'quantity' && item.poItem && value > item.poItem.quantity) {
      setItemErrors(prev => ({
        ...prev,
        [index]: {
          ...prev[index],
          quantity: 'Cannot exceed PO quantity'
        }
      }));
    } else if (field === 'quantity') {
      setItemErrors(prev => {
        const updated = { ...prev };
        if (updated[index]?.quantity) {
          delete updated[index].quantity;
        }
        if (Object.keys(updated[index] || {}).length === 0) {
          delete updated[index];
        }
        return updated;
      });
    }
    
    // Update the state with the new values
    const newInvoiceValue = updatedItems.reduce(
      (total, item) => total + (parseFloat(item.amount) || 0) + (parseFloat(item.tax) || 0), 
      0
    ).toFixed(2);
    
    setInvoiceData(prev => ({
      ...prev,
      invoiceItems: updatedItems,
      invoiceValue: newInvoiceValue
    }));
  };

  // Handle file selection (files are not uploaded yet)
  const handleFilesSelected = (files) => {
    console.log('Files selected:', files);
    // Store the raw file objects to be uploaded later during submission
    setUploadedFiles(files);
  };

  // Handle file upload error
  const handleFileUploadError = (errorMessage) => {
    console.error('File upload error:', errorMessage);
    setError(errorMessage);
  };

  // Check if form is valid
  const isFormValid = () => {
    // Log validation info to help debug
    console.log('Validation check:', {
      invoiceNumber: invoiceData.invoiceNumber.trim() !== '',
      invoiceDate: !!invoiceData.invoiceDate,
      invoiceValue: parseFloat(invoiceData.invoiceValue) > 0,
      itemsValid: invoiceData.invoiceItems.every(item => parseFloat(item.amount) > 0),
      fileKeysLength: invoiceData.fileKeys.length,
      uploadedFilesLength: uploadedFiles.length,
      noErrors: Object.keys(itemErrors).length === 0
    });

    // Files are selected but not yet uploaded when we validate, so check selectedFiles
    const hasSelectedFiles = fileUploadRef.current && fileUploadRef.current.getSelectedFiles().length > 0;

    return (
      invoiceData.invoiceNumber.trim() !== '' &&
      invoiceData.invoiceDate &&
      parseFloat(invoiceData.invoiceValue) > 0 &&
      invoiceData.invoiceItems.every(item => parseFloat(item.amount) > 0) &&
      (invoiceData.fileKeys.length > 0 || uploadedFiles.length > 0 || hasSelectedFiles) &&
      Object.keys(itemErrors).length === 0
    );
  };

  // Calculate total invoice value based on items
  const calculateTotalValue = () => {
    return invoiceData.invoiceItems.reduce(
      (total, item) => total + item.amount + item.tax, 
      0
    );
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError('');
      console.log('Starting invoice submission process');
      
      // First upload any selected files that haven't been uploaded yet
      let fileKeys = [...invoiceData.fileKeys]; // Start with any existing file keys
      
      // Check if we have a file upload component reference and files to upload
      if (fileUploadRef.current) {
        const selectedFiles = fileUploadRef.current.getSelectedFiles();
        console.log('Selected files for upload:', selectedFiles.length);
        
        if (selectedFiles.length > 0) {
          console.log('Uploading files before submitting invoice');
          
          // Use the FileUpload component's uploadFiles method
          const uploadResult = await fileUploadRef.current.uploadFiles();
          console.log('Upload result:', uploadResult);
          
          if (uploadResult.success && uploadResult.files) {
            // Extract file keys from the upload result
            const newFileKeys = uploadResult.files.map(file => file.fileKey);
            fileKeys = [...fileKeys, ...newFileKeys];
            console.log('File keys after upload:', fileKeys);
          } else {
            throw new Error(uploadResult.error || 'File upload failed');
          }
        }
      }
      
      // Format the invoice date as YYYY-MM-DD
      const formattedDate = invoiceData.invoiceDate instanceof Date ? 
        invoiceData.invoiceDate.toISOString().split('T')[0] : 
        invoiceData.invoiceDate;
      
      // Prepare payload with the fileKeys from upload and formatted date
      const payload = {
        ...invoiceData,
        invoiceDate: formattedDate,
        fileKeys: fileKeys
      };
      
      console.log('Submitting invoice with payload:', payload);
      
      // Call the submit function from parent
      const result = await onSubmit(payload);
      
      if (result) {
        setSuccess(true);
        // Auto close after short delay
        setTimeout(() => {
          resetForm();
          onClose();
        }, 1500);
      }
    } catch (err) {
      console.error('Error submitting invoice:', err);
      setError(err.message || 'Failed to submit invoice. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Reset form when dialog is closed
  const resetForm = () => {
    setUploadedFiles([]);
    setInvoiceData(prev => ({
      ...prev,
      fileKeys: []
    }));
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            Add Invoice to GRN #{grn?.id}
          </Typography>
          <IconButton
            aria-label="close"
            onClick={onClose}
            disabled={loading}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <Divider />
      
      <DialogContent>
        {success ? (
          <Alert severity="success" sx={{ my: 2 }}>
            Invoice added successfully!
          </Alert>
        ) : (
          <>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}
            
            <Grid container spacing={3}>
              {/* GRN Information */}
              <Grid item xs={12}>
                <Paper sx={{ p: 2, mb: 3 }} variant="outlined">
                  <Typography variant="subtitle1" gutterBottom>
                    GRN Details
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={3}>
                      <Typography variant="body2" color="text.secondary">
                        GRN ID
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {grn?.id}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={12} sm={6} md={3}>
                      <Typography variant="body2" color="text.secondary">
                        PO Number
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {grn?.purchaseOrder?.poNumber || `PO #${grn?.poId}`}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={12} sm={6} md={3}>
                      <Typography variant="body2" color="text.secondary">
                        Created Date
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {formatDate(grn?.createdAt)}
                      </Typography>
                    </Grid>
                    
                    <Grid item xs={12} sm={6} md={3}>
                      <Typography variant="body2" color="text.secondary">
                        Created By
                      </Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {grn?.createdBy?.name || `User ${grn?.createdBy?.id || 'Unknown'}`}
                      </Typography>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
              
              {/* Invoice Information */}
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Invoice Number"
                  fullWidth
                  required
                  value={invoiceData.invoiceNumber}
                  onChange={(e) => handleChange('invoiceNumber', e.target.value)}
                  error={invoiceData.invoiceNumber.trim() === ''}
                  helperText={invoiceData.invoiceNumber.trim() === '' ? 'Invoice number is required' : ''}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Invoice Date"
                  type="date"
                  fullWidth
                  required
                  value={invoiceData.invoiceDate ? new Date(invoiceData.invoiceDate).toISOString().split('T')[0] : ''}
                  onChange={(e) => handleChange('invoiceDate', new Date(e.target.value))}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Invoice Value"
                  fullWidth
                  required
                  type="number"
                  InputProps={{
                    startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                  }}
                  value={invoiceData.invoiceValue}
                  onChange={(e) => handleChange('invoiceValue', e.target.value)}
                  error={parseFloat(invoiceData.invoiceValue) <= 0}
                  helperText={parseFloat(invoiceData.invoiceValue) <= 0 ? 'Invoice value must be greater than 0' : ''}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Remarks"
                  fullWidth
                  multiline
                  rows={1}
                  value={invoiceData.remarks}
                  onChange={(e) => handleChange('remarks', e.target.value)}
                />
              </Grid>
              
              {/* Invoice Items */}
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
                  Invoice Items
                </Typography>
                
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow sx={{ backgroundColor: 'primary.light' }}>
                        <TableCell sx={{ color: 'white' }}>Item Name</TableCell>
                        <TableCell sx={{ color: 'white' }}>UOM</TableCell>
                        <TableCell sx={{ color: 'white' }}>GRN Quantity</TableCell>
                        <TableCell sx={{ color: 'white' }}>Unit Price</TableCell>
                        <TableCell sx={{ color: 'white' }}>Amount</TableCell>
                        <TableCell sx={{ color: 'white' }}>Tax Rate</TableCell>
                        <TableCell sx={{ color: 'white' }}>GST Amount</TableCell>
                        <TableCell sx={{ color: 'white' }}>Total Amount</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {invoiceData.invoiceItems.map((item, index) => {
                        // Calculate unit price directly from the item data
                        const unitPrice = item.unitPrice || (item.amount / item.quantity) || 0;
                        const totalItemAmount = (parseFloat(item.amount) || 0) + (parseFloat(item.tax) || 0);
                        return (
                          <TableRow key={index} sx={{ '&:nth-of-type(odd)': { backgroundColor: 'action.hover' } }}>
                            <TableCell>
                              <Typography fontWeight="medium">
                                {item.itemName || 'Unknown'}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              {item.uom || 'Nos'}
                            </TableCell>
                            <TableCell>
                              <TextField
                                type="number"
                                variant="outlined"
                                size="small"
                                value={item.quantity}
                                disabled={true} // Prefilled from GRN and not editable
                                InputProps={{
                                  readOnly: true,
                                }}
                                sx={{ width: '80px' }}
                              />
                            </TableCell>
                            <TableCell>
                              <Typography>₹{parseFloat(unitPrice || 0).toFixed(2)}</Typography>
                            </TableCell>
                            <TableCell>
                              <TextField
                                type="number"
                                variant="outlined"
                                size="small"
                                value={item.amount}
                                onChange={(e) => handleItemChange(index, 'amount', e.target.value)}
                                InputProps={{
                                  startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                                }}
                                inputProps={{ min: 0, step: 1 }}
                                sx={{ width: '100px' }}
                              />
                            </TableCell>
                            <TableCell>
                              <FormControl size="small" sx={{ minWidth: 120 }}>
                                <Select
                                  value={item.gstOption || (item.gstType ? `${item.taxRate || 0}_${item.gstType}` : (item.taxRate ? item.taxRate.toString() : '0'))}
                                  onChange={(e) => {
                                    const selectedValue = e.target.value;
                                    let taxRate, gstType;
                                    
                                    // Check if value contains type information
                                    if (typeof selectedValue === 'string' && selectedValue.includes('_')) {
                                      const [rate, type] = selectedValue.split('_');
                                      taxRate = parseFloat(rate);
                                      gstType = type;
                                    } else {
                                      taxRate = parseFloat(selectedValue);
                                      gstType = 'IGST';
                                    }
                                    
                                    // Calculate tax amount based on the current item amount and selected tax rate
                                    const itemAmount = parseFloat(item.amount) || 0;
                                    const taxAmount = (itemAmount * taxRate) / 100;
                                    
                                    // Update all tax-related fields in a single operation
                                    const updatedItems = [...invoiceData.invoiceItems];
                                    updatedItems[index] = {
                                      ...updatedItems[index],
                                      taxRate,
                                      gstType,
                                      tax: taxAmount,
                                      gstOption: selectedValue
                                    };
                                    
                                    // Update the state with the new values
                                    const newInvoiceValue = updatedItems.reduce(
                                      (total, item) => total + (parseFloat(item.amount) || 0) + (parseFloat(item.tax) || 0), 
                                      0
                                    ).toFixed(2);
                                    
                                    setInvoiceData(prev => ({
                                      ...prev,
                                      invoiceItems: updatedItems,
                                      invoiceValue: newInvoiceValue
                                    }));
                                    
                                    console.log(`Tax updated: Rate ${taxRate}%, Amount: ₹${taxAmount}`);
                                  }}
                                  displayEmpty
                                  variant="outlined"
                                  sx={{
                                    minWidth: '100px',
                                    '.MuiOutlinedInput-input': {
                                      py: 1,
                                      display: 'flex',
                                      alignItems: 'center'
                                    }
                                  }}
                                  renderValue={(selected) => {
                                    // Find the matching GST rate option
                                    const selectedOption = GST_RATES.find(option => {
                                      const optionValue = option.type ? `${option.value}_${option.type}` : option.value.toString();
                                      return optionValue === selected;
                                    });
                                    
                                    // If found, show the full label for better context, otherwise show just the rate
                                    if (selectedOption) {
                                      return <Typography variant="body2" noWrap>{selectedOption.value}%</Typography>;
                                    } else if (selected && typeof selected === 'string' && selected.includes('_')) {
                                      const [rate] = selected.split('_');
                                      return <Typography variant="body2">{rate}%</Typography>;
                                    } else if (selected) {
                                      return <Typography variant="body2">{selected}%</Typography>;
                                    }
                                    return <Typography variant="body2" color="text.secondary">0%</Typography>;
                                  }}
                                >
                                  {GST_RATES.map((option) => {
                                    const menuItemValue = option.type ? `${option.value}_${option.type}` : option.value.toString();
                                    return (
                                      <MenuItem 
                                        key={menuItemValue}
                                        value={menuItemValue}
                                      >
                                        {option.label}
                                      </MenuItem>
                                    );
                                  })}
                                </Select>
                              </FormControl>
                            </TableCell>
                            <TableCell>
                              <TextField
                                type="text"
                                variant="outlined"
                                size="small"
                                value={parseFloat(item.tax || 0).toFixed(2)}
                                InputProps={{
                                  readOnly: true,
                                  startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                                }}
                                sx={{ 
                                  width: '100px',
                                  '& .MuiOutlinedInput-root.Mui-disabled': {
                                    '& > fieldset': {
                                      borderColor: 'rgba(0, 0, 0, 0.23)', // Match default border color
                                    },
                                    '&:hover fieldset': {
                                      borderColor: 'rgba(0, 0, 0, 0.23)', // Keep border color on hover
                                    },
                                  },
                                }}
                              />
                            </TableCell>
                            <TableCell>
                              <Typography fontWeight="medium" color="primary.main">
                                ₹{parseFloat(totalItemAmount || 0).toFixed(2)}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                      <TableRow sx={{ backgroundColor: 'primary.light', color: 'white' }}>
                        <TableCell colSpan={6} align="right" sx={{ fontWeight: 'bold', color: 'white' }}>
                          Totals
                        </TableCell>
                        <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>
                          ₹{parseFloat(invoiceData.invoiceItems.reduce((sum, item) => sum + parseFloat(item.tax || 0), 0) || 0).toFixed(2)}
                        </TableCell>
                        <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>
                          ₹{parseFloat(calculateTotalValue() || 0).toFixed(2)}
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
                
                {calculateTotalValue() !== parseFloat(invoiceData.invoiceValue || 0) && (
                  <Typography variant="body2" color="error" sx={{ mt: 1 }}>
                    Warning: Invoice total (₹{parseFloat(calculateTotalValue() || 0).toFixed(2)}) does not match invoice value (₹{parseFloat(invoiceData.invoiceValue || 0).toFixed(2)})
                  </Typography>
                )}
              </Grid>
              
              {/* File Upload */}
              <Grid item xs={12}>
                <Paper variant="outlined" sx={{ p: 2, mt: 2, mb: 2, border: '1px dashed #1976d2', backgroundColor: 'rgba(25, 118, 210, 0.05)' }}>
                  <Typography variant="subtitle1" gutterBottom sx={{ color: 'primary.main' }}>
                    Invoice Document(s)
                  </Typography>
                  
                  <FileUpload
                    ref={fileUploadRef} // Add the ref here
                    multiple={true}
                    onFilesSelected={handleFilesSelected}
                    onError={handleFileUploadError}
                    initialFiles={uploadedFiles}
                    showPreview={true}
                    disabled={loading}
                    uploadImmediately={false}
                    acceptedFileTypes="image/*,.pdf,.doc,.docx,.xls,.xlsx"
                  />
                  
                  {uploadedFiles.length === 0 && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      Please select at least one invoice file (image, PDF, or document)
                    </Alert>
                  )}
                  
                  {uploadedFiles.length > 0 && (
                    <Alert severity="success" sx={{ mt: 2 }}>
                      {uploadedFiles.length} file(s) selected and ready for submission
                    </Alert>
                  )}
                </Paper>
              </Grid>
            </Grid>
          </>
        )}
      </DialogContent>
      
      <DialogActions sx={{ px: 3, pb: 3, mt: 2 }}>
        <Button
          onClick={() => {
            resetForm();
            onClose();
          }}
          variant="outlined"
          color="secondary"
          disabled={loading}
          startIcon={<CloseIcon />}
        >
          Cancel
        </Button>
        
        <Box sx={{ flex: '1 1 auto' }} />
        
        <Button
          variant="contained"
          color="primary"
          onClick={handleSubmit}
          disabled={!isFormValid() || loading || success}
          startIcon={loading ? <CircularProgress size={20} /> : <ReceiptIcon />}
          sx={{ px: 3 }}
        >
          {loading ? 'Submitting...' : 'Submit Invoice'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default InvoiceDialog;
