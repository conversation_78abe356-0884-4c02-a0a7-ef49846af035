import React, { useEffect, useState } from 'react';
import { Box, Paper, Typography, Divider, TableContainer, Table, TableHead, TableBody, TableRow, TableCell, Chip, IconButton, Grid } from '@mui/material';
import { RemoveRedEye } from '@mui/icons-material';
import InvoiceDetailHeader from './InvoiceDetailHeader';
import InvoiceDetailInfo from './InvoiceDetailInfo';
import FilePreview, { getFileIcon } from '../common/FilePreview';

// Attachments card component
const InvoiceAttachmentsCard = ({ attachments }) => {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  
  const handleOpenPreview = (file) => {
    setSelectedFile(file);
    setPreviewOpen(true);
  };
  
  const handleClosePreview = () => {
    setPreviewOpen(false);
  };
  
  return (
    <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
      <Typography variant="h6" gutterBottom>Attachments</Typography>
      <Divider sx={{ mb: 2 }} />
      <Box>
        {attachments && attachments.length > 0 ? (
          <Grid container spacing={2}>
            {attachments.map((file, idx) => (
              <Grid item xs={12} sm={6} md={4} key={idx}>
                <Paper 
                  variant="outlined" 
                  sx={{ 
                    p: 1.5,
                    display: 'flex',
                    alignItems: 'center', 
                    '&:hover': { bgcolor: 'action.hover' }
                  }}
                >
                  <Box sx={{ mr: 1.5 }}>
                    {getFileIcon(file.fileName, file.contentType)}
                  </Box>
                  <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
                    <Typography variant="body2" noWrap title={file.fileName}>
                      {file.fileName}
                    </Typography>
                  </Box>
                  <IconButton 
                    size="small" 
                    color="primary" 
                    onClick={() => handleOpenPreview(file)}
                    title="Preview document"
                  >
                    <RemoveRedEye fontSize="small" />
                  </IconButton>
                </Paper>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Typography variant="body2" color="text.secondary">No attachments linked.</Typography>
        )}
      </Box>
      
      {/* File Preview Dialog */}
      {selectedFile && (
        <FilePreview 
          open={previewOpen}
          onClose={handleClosePreview}
          fileUrl={selectedFile.fileUrl}
          fileName={selectedFile.fileName}
          contentType={selectedFile.contentType}
          title={selectedFile.fileName}
          stopLoadingOnNonPreviewable={true}
          openDownloadsInNewTab={true}
        />
      )}
    </Paper>
  );
};

/**
 * Component to display invoice details
 * 
 * @param {Object} props - Component props
 * @param {Object} props.invoice - The invoice or GRN containing invoice details
 * @param {Function} props.onBack - Navigation callback
 */
const InvoiceDetails = ({ invoice, onBack }) => {
  // Get the invoice data from either direct invoice object or from GRN's invoice property
  const invoiceData = invoice.invoice || invoice;
  const [attachments, setAttachments] = useState([]);

  useEffect(() => {
    setAttachments(invoiceData.attachments || []);
  }, [invoiceData]);

  const items = invoiceData.items || [];
  const hasItems = items && items.length > 0;

  return (
    <Box>
      <InvoiceDetailHeader invoiceDetails={invoiceData} onBack={onBack} />
      <InvoiceDetailInfo invoiceDetails={invoiceData} />
      <InvoiceAttachmentsCard attachments={attachments} />

      {/* Invoice Items Section */}
      {hasItems && (
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6">Invoice Items ({items.length})</Typography>
          <Divider sx={{ mt: 1, mb: 2 }} />
          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow sx={{ backgroundColor: 'primary.light' }}>
                  <TableCell sx={{ color: 'white' }}>Item Name</TableCell>
                  <TableCell sx={{ color: 'white' }}>Description</TableCell>
                  <TableCell sx={{ color: 'white' }}>Category</TableCell>
                  <TableCell align="right" sx={{ color: 'white' }}>Quantity</TableCell>
                  <TableCell align="right" sx={{ color: 'white' }}>Unit Price</TableCell>
                  <TableCell align="right" sx={{ color: 'white' }}>Amount</TableCell>
                  <TableCell align="right" sx={{ color: 'white' }}>Tax</TableCell>
                  <TableCell align="right" sx={{ color: 'white' }}>Total</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {items.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>{item.itemName}</TableCell>
                    <TableCell>{item.itemDescription || 'N/A'}</TableCell>
                    <TableCell>
                      {item.itemCategoryDetails?.name ? (
                        <Chip 
                          label={item.itemCategoryDetails.name} 
                          size="small" 
                          variant="outlined" 
                        />
                      ) : 'N/A'}
                    </TableCell>
                    <TableCell align="right">{item.invoiceQuantity}</TableCell>
                    <TableCell align="right">₹{parseFloat(item.pricePerUnit || 0).toFixed(2)}</TableCell>
                    <TableCell align="right">₹{parseFloat(item.amount || 0).toFixed(2)}</TableCell>
                    <TableCell align="right">₹{parseFloat(item.tax || 0).toFixed(2)}</TableCell>
                    <TableCell align="right">₹{parseFloat(item.totalPrice || 0).toFixed(2)}</TableCell>
                  </TableRow>
                ))}
                <TableRow>
                  <TableCell colSpan={5} />
                  <TableCell align="right" sx={{ fontWeight: 'bold' }}>Total</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                    ₹{items.reduce((sum, item) => sum + parseFloat(item.tax || 0), 0).toFixed(2)}
                  </TableCell>
                  <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                    ₹{parseFloat(invoiceData.invoiceValue || 0).toFixed(2)}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      )}
      {/* Remarks Section if available */}
      {invoiceData.remarks && (
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6">Remarks</Typography>
          <Divider sx={{ mt: 1, mb: 2 }} />
          <Paper variant="outlined" sx={{ p: 2 }}>
            <Typography variant="body1">{invoiceData.remarks}</Typography>
          </Paper>
        </Box>
      )}
    </Box>
  );
};

export default InvoiceDetails;
