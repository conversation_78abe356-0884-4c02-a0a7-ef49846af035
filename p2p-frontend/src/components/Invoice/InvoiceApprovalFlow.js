import React, { useEffect, useState } from 'react';
import { <PERSON>, Typo<PERSON>, Button, TextField, Chip, Stack, Paper, CircularProgress } from '@mui/material';
import { useSnackbar } from 'notistack';
import axios from 'axios';

/**
 * InvoiceApprovalFlow: shows approval steps, status, remarks, and allows eligible users to approve/reject
 * Props:
 *   invoiceId: number (required)
 *   currentUser: { id: number, roles: string[] } (required)
 */
const approvalRoleLabels = {
  CC_HEAD: 'Cost Center Head',
  BIZ_FIN: 'Business Finance',
  PROCUREMENT_MANAGER: 'Procurement Manager',
};

const statusColor = {
  APPROVED: 'success',
  REJECTED: 'error',
  PENDING_APPROVAL: 'warning',
  PENDING: 'default',
};

const InvoiceApprovalFlow = ({ invoiceId, currentUser }) => {
  const { enqueueSnackbar } = useSnackbar();
  const [approvals, setApprovals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [remarks, setRemarks] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  // Fetch approval history
  const fetchApprovals = async () => {
    setLoading(true);
    try {
      const res = await axios.get(`/api/invoices/${invoiceId}/approval-history`);
      setApprovals(res.data.approvals || []);
    } catch (e) {
      enqueueSnackbar('Failed to load approval flow', { variant: 'error' });
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchApprovals();
    // eslint-disable-next-line
  }, [invoiceId]);

  // Find current step
  const pendingApproval = approvals.find(a => a.status === 'PENDING_APPROVAL');
  const canAct = pendingApproval && currentUser.roles.includes(pendingApproval.approvalRole);

  // Approve/Reject handlers
  const handleAction = async (action) => {
    setActionLoading(true);
    try {
      await axios.post(`/api/invoices/${invoiceId}/${action}`, { remarks });
      enqueueSnackbar(`Invoice ${action === 'approve' ? 'approved' : 'rejected'} successfully`, { variant: 'success' });
      setRemarks('');
      fetchApprovals();
    } catch (e) {
      enqueueSnackbar(e.response?.data?.message || `Failed to ${action} invoice`, { variant: 'error' });
    }
    setActionLoading(false);
  };

  return (
    <Paper variant="outlined" sx={{ p: 2, mt: 3 }}>
      <Typography variant="h6" gutterBottom>Approval Flow</Typography>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}><CircularProgress /></Box>
      ) : (
        <Stack spacing={2}>
          {approvals.map((step, idx) => (
            <Box key={step.id || idx} sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Chip label={approvalRoleLabels[step.approvalRole] || step.approvalRole} color="primary" size="small" />
              <Chip label={step.status.replace('_', ' ')} color={statusColor[step.status] || 'default'} size="small" />
              {step.verifiedBy && (
                <Typography variant="body2" sx={{ ml: 1 }}>
                  By: {step.verifiedBy} {step.verifiedAt ? `on ${new Date(step.verifiedAt).toLocaleString()}` : ''}
                </Typography>
              )}
              {step.remarks && (
                <Typography variant="body2" sx={{ ml: 1, fontStyle: 'italic' }}>
                  Remarks: {step.remarks}
                </Typography>
              )}
            </Box>
          ))}

          {canAct && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 2 }}>
              <TextField
                label="Remarks"
                size="small"
                value={remarks}
                onChange={e => setRemarks(e.target.value)}
                sx={{ minWidth: 220 }}
              />
              <Button
                variant="contained"
                color="success"
                disabled={actionLoading}
                onClick={() => handleAction('approve')}
              >
                Approve
              </Button>
              <Button
                variant="outlined"
                color="error"
                disabled={actionLoading}
                onClick={() => handleAction('reject')}
              >
                Reject
              </Button>
            </Box>
          )}
        </Stack>
      )}
    </Paper>
  );
};

export default InvoiceApprovalFlow;
