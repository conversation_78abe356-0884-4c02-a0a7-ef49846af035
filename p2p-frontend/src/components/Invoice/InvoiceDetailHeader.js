import React from 'react';
import { Box, Typography, IconButton, Chip } from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';
// Add print/download icons if needed
// import { Print as PrintIcon, FileDownload as DownloadIcon } from '@mui/icons-material';

const InvoiceDetailHeader = ({ invoiceDetails, onBack }) => {
  return (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={onBack} sx={{ mr: 1 }}>
          <ArrowBackIcon />
        </IconButton>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="h5">Invoice Details</Typography>
        </Box>
      </Box>
      <Box sx={{
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        mb: 3
      }}>
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Typography variant="h6" sx={{ mr: 2 }}>
              {`#${invoiceDetails.id}`}
            </Typography>
            <Chip
              label={invoiceDetails.status || 'N/A'}
              color="primary"
              size="small"
              sx={{ fontWeight: 'bold', textTransform: 'uppercase' }}
            />
          </Box>
          <Typography variant="body2" color="text.secondary">
            Created on: {invoiceDetails.invoiceDate ? new Date(invoiceDetails.invoiceDate).toLocaleString() : 'N/A'}
          </Typography>
        </Box>
      </Box>
    </>
  );
};

export default InvoiceDetailHeader;
