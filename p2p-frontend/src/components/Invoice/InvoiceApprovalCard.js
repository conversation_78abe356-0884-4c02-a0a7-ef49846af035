import React, { useState } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  CircularProgress,
  Chip,
  Paper,
  Typography
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';

// Utility for role name display
const getRoleDisplayName = (role) => {
  const roleMap = {
    'biz_fin': 'Business Finance',
    'finance': 'Finance',
    'cc_head': 'Cost Center Head',
    'procurement_manager': 'Procurement Manager'
  };
  return roleMap[role] || role;
};

const InvoiceApprovalCard = ({ 
  invoiceId, 
  onApprove, 
  onReject, 
  loading = false,
  approvalRole = ''
}) => {
  // State for dialogs
  const [approveDialogOpen, setApproveDialogOpen] = useState(false);
  const [rejectDialogOpen, setRejectDialogOpen] = useState(false);
  const [remarks, setRemarks] = useState('');
  
  // Handlers for dialogs
  const handleOpenApproveDialog = () => {
    setApproveDialogOpen(true);
    setRemarks('');
  };

  const handleOpenRejectDialog = () => {
    setRejectDialogOpen(true);
    setRemarks('');
  };

  const handleDialogClose = () => {
    setApproveDialogOpen(false);
    setRejectDialogOpen(false);
    setRemarks('');
  };

  const handleApprove = () => {
    onApprove(remarks);
    handleDialogClose();
  };

  const handleReject = () => {
    onReject(remarks);
    handleDialogClose();
  };

  return (
    <>
      <Paper sx={{ p: 3, mt: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="h6" gutterBottom>
            Invoice Approval Actions
          </Typography>
        </Box>
        <Box p={2}>
          <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
            <Button 
              variant="contained" 
              color="success" 
              startIcon={<CheckCircleIcon />} 
              onClick={handleOpenApproveDialog}
              fullWidth
              disabled={loading}
            >
              Approve
            </Button>
            <Button 
              variant="contained" 
              color="error" 
              startIcon={<CancelIcon />}
              onClick={handleOpenRejectDialog}
              fullWidth
              disabled={loading}
            >
              Reject
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* Approve Dialog */}
      <Dialog open={approveDialogOpen} onClose={handleDialogClose}>
        <DialogTitle>
          Approve Invoice
          {approvalRole && (
            <Chip 
              label={`As ${getRoleDisplayName(approvalRole)}`}
              color="primary"
              size="small"
              sx={{ ml: 1, fontWeight: 'medium' }}
            />
          )}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Please provide any remarks for this approval.
          </DialogContentText>
          
          <TextField
            autoFocus
            margin="dense"
            id="remarks"
            label="Remarks (Optional)"
            type="text"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={remarks}
            onChange={(e) => setRemarks(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} disabled={loading}>Cancel</Button>
          <Button 
            onClick={handleApprove} 
            color="success" 
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Approve'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={rejectDialogOpen} onClose={handleDialogClose}>
        <DialogTitle>
          Reject Invoice
          {approvalRole && (
            <Chip 
              label={`As ${getRoleDisplayName(approvalRole)}`}
              color="error"
              size="small"
              sx={{ ml: 1, fontWeight: 'medium' }}
            />
          )}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Please provide a reason for rejecting this invoice.
          </DialogContentText>
          
          <TextField
            autoFocus
            margin="dense"
            id="remarks"
            label="Rejection Reason"
            type="text"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={remarks}
            onChange={(e) => setRemarks(e.target.value)}
            required
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose} disabled={loading}>Cancel</Button>
          <Button 
            onClick={handleReject} 
            variant="contained" 
            color="error" 
            disabled={loading || !remarks.trim()}
          >
            {loading ? <CircularProgress size={24} /> : 'Reject'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default InvoiceApprovalCard;
