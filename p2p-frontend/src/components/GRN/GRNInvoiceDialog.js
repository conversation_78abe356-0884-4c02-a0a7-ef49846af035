import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Divider,
  CircularProgress,
  Alert,
  Grid
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SaveIcon from '@mui/icons-material/Save';

// Import sub-components
import GRNItemTable from './GRNItemTable';
import InvoiceInfoForm from './InvoiceInfoForm';



/**
 * Dialog component for GRN and Invoice creation
 * 
 * @param {Object} props Component props
 * @param {boolean} props.open Dialog open state
 * @param {Function} props.onClose Callback when dialog closes
 * @param {Object} props.poDetails Purchase order details
 * @param {Function} props.onSubmit Callback when form submits
 */
const GRNInvoiceDialog = ({ open, onClose, poDetails, onSubmit }) => {
  // State variables
  const [grnItems, setGrnItems] = useState([]);
  const [invoiceInfo, setInvoiceInfo] = useState({
    invoiceNumber: '',
    invoiceValue: '',
    invoiceDate: '',
    invoiceFile: null
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Reset state when dialog opens
  useEffect(() => {
    if (open) {

      setGrnItems([]);
      setInvoiceInfo({
        invoiceNumber: '',
        invoiceValue: '',
        invoiceDate: '',
        invoiceFile: null
      });
      setError('');
      setSuccess(false);
    }
  }, [open]);

  // Initialize GRN items from PO items
  useEffect(() => {
    if (poDetails && poDetails.items) {
      const initializedItems = poDetails.items.map(item => ({
        ...item,
        grnQuantity: 0
      }));
      setGrnItems(initializedItems);
    }
  }, [poDetails]);



  // Handle GRN item quantity changes
  const handleGRNItemsChange = (updatedItems) => {
    setGrnItems(updatedItems);
  };

  // Handle invoice info changes
  const handleInvoiceInfoChange = (updatedInfo) => {
    setInvoiceInfo(updatedInfo);
  };

  // Check if form is valid and can proceed
  const isFormValid = () => {
    // Check GRN items - ensure at least one item has a quantity > 0
    const hasValidGrnItems = grnItems.some(item => item.grnQuantity > 0);
    
    // Check invoice details
    const hasValidInvoiceInfo = (
      invoiceInfo.invoiceNumber.trim() !== '' &&
      invoiceInfo.invoiceValue > 0 &&
      invoiceInfo.invoiceDate !== '' &&
      invoiceInfo.invoiceFile !== null
    );
    
    return hasValidGrnItems && hasValidInvoiceInfo;
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError('');
      
      // 1. First create the GRN to get the GRN ID
      const grnPayload = {
        poId: poDetails.id,
        grnItems: grnItems
          .filter(item => item.grnQuantity > 0)
          .map(item => ({
            poItemId: item.id,
            quantity: item.grnQuantity // Make sure quantity field name matches backend expectation
          }))
      };
      
      // Make sure we have items
      if (grnPayload.grnItems.length === 0) {
        throw new Error('At least one item must have a quantity greater than zero');
      }
      
      // Make sure we have the file
      if (!invoiceInfo.invoiceFile) {
        throw new Error('Invoice file is required');
      }
      
      // Log the GRN payload for debugging
      console.log('GRN Payload:', grnPayload);
      
      // 2. Prepare the complete invoice payload
      // This matches the format needed by the API:
      // grnId, invoiceNumber, invoiceDate, invoiceValue, remarks, invoiceItems, fileKeys
      
      // Upload file and create GRN with invoice
      // The parent component will handle file upload and providing fileKeys
      const fileData = new FormData();
      fileData.append('file', invoiceInfo.invoiceFile);
      
      // The complete payload with GRN data, invoice info, and file
      // onSubmit will handle creating GRN first, then adding invoice with the GRN ID
      await onSubmit({
        grnData: grnPayload,
        invoiceData: {
          invoiceNumber: invoiceInfo.invoiceNumber,
          invoiceDate: invoiceInfo.invoiceDate,
          invoiceValue: parseFloat(invoiceInfo.invoiceValue),
          remarks: invoiceInfo.remarks || 'Created from PO: ' + poDetails.id
        },
        fileData
      });
      
      // Show success and reset form
      setSuccess(true);
      
      // Close dialog after a brief delay
      setTimeout(() => {
        onClose();
      }, 2000);
      
    } catch (error) {
      console.error('Error submitting GRN and invoice:', error);
      setError(error.message || error.response?.data?.message || 'Failed to submit GRN and invoice');
    } finally {
      setLoading(false);
    }
  };



  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            Create GRN and Invoice
          </Typography>
          <Button 
            variant="text" 
            color="inherit" 
            onClick={onClose} 
            disabled={loading}
            startIcon={<CloseIcon />}
          >
            Close
          </Button>
        </Box>
      </DialogTitle>
      
      <Divider />
      

      
      <DialogContent>
        {success ? (
          <Alert severity="success" sx={{ my: 2 }}>
            GRN and Invoice created successfully!
          </Alert>
        ) : (
          <>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>Goods Receipt Details</Typography>
                <GRNItemTable 
                  items={poDetails?.items || []} 
                  onItemQuantityChange={handleGRNItemsChange} 
                />
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>Invoice Information</Typography>
                <InvoiceInfoForm 
                  onInvoiceInfoChange={handleInvoiceInfoChange}
                />
              </Grid>
            </Grid>
          </>
        )}
      </DialogContent>
      
      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button
          onClick={onClose}
          disabled={loading}
        >
          Cancel
        </Button>
        
        <Box sx={{ flex: '1 1 auto' }} />
        
        <Button
          variant="contained"
          color="primary"
          onClick={handleSubmit}
          disabled={!isFormValid() || loading || success}
          startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
        >
          {loading ? 'Submitting...' : 'Create GRN & Invoice'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default GRNInvoiceDialog;
