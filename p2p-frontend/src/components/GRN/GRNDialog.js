import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Divider,
  CircularProgress,
  Alert,
  TextField,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Paper,
  TableContainer
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ReceiptIcon from '@mui/icons-material/Receipt';
import SaveIcon from '@mui/icons-material/Save';

/**
 * Dialog component for GRN creation
 * 
 * @param {Object} props Component props
 * @param {boolean} props.open Dialog open state
 * @param {Function} props.onClose Callback when dialog closes
 * @param {Object} props.poDetails Purchase order details
 * @param {Function} props.onSubmit Callback when form submits
 */
const GRNDialog = ({ open, onClose, poDetails, onSubmit }) => {
  // State variables
  const [grnItems, setGrnItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Reset state when dialog opens
  useEffect(() => {
    if (open && poDetails?.items) {
      initializeGRNItems();
      setError('');
      setSuccess(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, poDetails]);

  // Initialize GRN items from PO items
  const initializeGRNItems = () => {
    if (poDetails && poDetails.items) {
      // Use the remainingGrnQuantity directly from the PO item data
      // This is more accurate than calculating it manually
      const itemsWithQuantities = poDetails.items.map(item => ({
        poItemId: item.id,
        grnQuantity: 0,
        poItem: item,
        maxQuantity: parseFloat(item.quantity),
        // Use remainingGrnQuantity directly from the PO item
        remainingQuantity: parseFloat(item.remainingGrnQuantity || 0)
      }));
      setGrnItems(itemsWithQuantities);
    }
  };

  // Handle GRN item quantity changes
  const handleGRNItemChange = (index, value) => {
    const updatedItems = [...grnItems];
    const numValue = parseInt(value, 10) || 0;
    
    // We'll allow the user to enter more than the remaining quantity
    // but we'll show validation errors and disable the submit button
    updatedItems[index].grnQuantity = numValue;
    setGrnItems(updatedItems);
  };

  // Check if form is valid and can proceed
  const isFormValid = () => {
    // Check if at least one item has a quantity > 0
    const hasItems = grnItems.some(item => item.grnQuantity > 0);
    
    // Check if any item exceeds remaining quantity
    const hasExceededQuantity = grnItems.some(item => 
      item.grnQuantity > item.remainingQuantity
    );
    
    return hasItems && !hasExceededQuantity;
  };
  
  // Check if there are any quantity validation errors
  const hasQuantityErrors = () => {
    return grnItems.some(item => item.grnQuantity > item.remainingQuantity);
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError('');
      
      // Prepare the payload in the exact format from the curl example
      const payload = {
        poId: poDetails.id,
        grnItems: grnItems
          .filter(item => item.grnQuantity > 0)
          .map(item => ({
            poItemId: item.poItemId,
            grnQuantity: item.grnQuantity
          }))
      };
      
      // Call the API
      await onSubmit(payload);
      
      // Show success and reset form
      setSuccess(true);
      
      // Close dialog immediately after successful submission
      onClose();
      
    } catch (error) {
      console.error('Error submitting GRN:', error);
      setError(error.response?.data?.message || 'Failed to submit GRN');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6" display="flex" alignItems="center">
            <ReceiptIcon sx={{ mr: 1 }} />
            Create Goods Receipt Note
          </Typography>
          <Button 
            variant="text" 
            color="inherit" 
            onClick={onClose} 
            disabled={loading}
            startIcon={<CloseIcon />}
          >
            Close
          </Button>
        </Box>
      </DialogTitle>
      
      <Divider />
      
      <DialogContent>
        {success ? (
          <Alert severity="success" sx={{ my: 2 }}>
            GRN created successfully!
          </Alert>
        ) : (
          <>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}
            
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Enter received quantities for PO items
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                The quantities should not exceed the available quantities in the PO.
              </Typography>
              
              {hasQuantityErrors() && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  Some items exceed the remaining quantity available. Please correct the quantities.
                </Alert>
              )}
              
              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow sx={{ backgroundColor: 'primary.light' }}>
                      <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>Item Name</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>PO Quantity</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>Remaining Qty</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>UOM</TableCell>
                      <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>GRN Quantity</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {grnItems.map((item, index) => (
                      <TableRow key={item.poItemId} sx={{ 
                        '&:nth-of-type(odd)': { backgroundColor: 'action.hover' },
                        backgroundColor: item.grnQuantity > item.remainingQuantity ? 'rgba(211, 47, 47, 0.08)' : 'inherit'
                      }}>
                        <TableCell>{item.poItem.itemName}</TableCell>
                        <TableCell>{item.poItem.quantity}</TableCell>
                        <TableCell>
                          <Typography 
                            color={item.remainingQuantity > 0 ? 'success.main' : 'error.main'}
                            fontWeight="medium"
                          >
                            {item.remainingQuantity}
                          </Typography>
                        </TableCell>
                        <TableCell>{item.poItem.uom || 'Nos'}</TableCell>
                        <TableCell>
                          <TextField
                            type="number"
                            variant="outlined"
                            size="small"
                            value={item.grnQuantity}
                            onChange={(e) => handleGRNItemChange(index, e.target.value)}
                            inputProps={{ 
                              min: 0, 
                              max: item.remainingQuantity,
                              step: 1
                            }}
                            error={item.grnQuantity > item.remainingQuantity}
                            helperText={item.grnQuantity > item.remainingQuantity ? "Cannot exceed remaining quantity" : ""}
                            sx={{ width: '100px' }}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </>
        )}
      </DialogContent>
      
      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button
          onClick={onClose}
          disabled={loading}
        >
          Cancel
        </Button>
        
        <Box sx={{ flex: '1 1 auto' }} />
        
        <Button
          variant="contained"
          color="primary"
          onClick={handleSubmit}
          disabled={!isFormValid() || loading || success}
          startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
        >
          {loading ? 'Submitting...' : 'Create GRN'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default GRNDialog;
