import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Typography,
  Box
} from '@mui/material';

/**
 * Component to display and edit GRN quantities for PO items
 * 
 * @param {Object} props Component props
 * @param {Array} props.items PO items array
 * @param {Function} props.onItemQuantityChange Callback when GRN quantity changes
 */
const GRNItemTable = ({ items, onItemQuantityChange }) => {
  const [grnItems, setGrnItems] = useState([]);

  // Initialize GRN items from PO items
  useEffect(() => {
    if (items && items.length > 0) {
      const initializedItems = items.map(item => ({
        ...item,
        grnQuantity: 0,
        // Use item's remainingGrnQuantity if provided, otherwise use the full quantity
        remainingGrnQuantity: item.remainingGrnQuantity !== undefined ? 
          item.remainingGrnQuantity : item.quantity
      }));
      setGrnItems(initializedItems);
    }
  }, [items]);

  // Handle quantity change for an item
  const handleQuantityChange = (index, value) => {
    // Parse input value as integer
    const quantity = parseInt(value, 10) || 0;
    
    // Create a copy of the current items
    const updatedItems = [...grnItems];
    
    // Limit quantity to not exceed remaining available quantity
    const remainingQuantity = updatedItems[index].remainingGrnQuantity !== undefined ? 
      updatedItems[index].remainingGrnQuantity : updatedItems[index].quantity;
    const limitedQuantity = Math.min(quantity, remainingQuantity);
    
    // Update the item with new quantity
    updatedItems[index] = {
      ...updatedItems[index],
      grnQuantity: limitedQuantity
    };
    
    // Update state
    setGrnItems(updatedItems);
    
    // Notify parent component
    onItemQuantityChange(updatedItems);
  };

  if (!items || items.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', p: 3 }}>
        <Typography variant="body1">No items found in this purchase order</Typography>
      </Box>
    );
  }

  return (
    <TableContainer component={Paper} sx={{ mt: 2 }}>
      <Table size="small">
        <TableHead>
          <TableRow sx={{ backgroundColor: 'primary.light' }}>
            <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>Item Name</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>Description</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>PO Quantity</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>UOM</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>Price/Unit</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>Remaining Quantity</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: 'white' }}>GRN Quantity</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {grnItems.map((item, index) => (
            <TableRow key={item.id} sx={{ '&:nth-of-type(odd)': { backgroundColor: 'action.hover' } }}>
              <TableCell>{item.itemName}</TableCell>
              <TableCell>{item.itemDescription || '-'}</TableCell>
              <TableCell>{item.quantity}</TableCell>
              <TableCell>{item.uom || 'Nos'}</TableCell>
              <TableCell>₹{item.pricePerUnit?.toFixed(2) || '0.00'}</TableCell>
              <TableCell>
                <Typography 
                  color={item.remainingGrnQuantity > 0 ? 'success.main' : 'error.main'}
                  fontWeight="medium"
                >
                  {item.remainingGrnQuantity !== undefined ? item.remainingGrnQuantity : item.quantity}
                </Typography>
              </TableCell>
              <TableCell>
                <TextField
                  type="number"
                  variant="outlined"
                  size="small"
                  value={item.grnQuantity}
                  onChange={(e) => handleQuantityChange(index, e.target.value)}
                  inputProps={{ 
                    min: 0, 
                    max: item.remainingGrnQuantity !== undefined ? item.remainingGrnQuantity : item.quantity,
                    step: 1
                  }}
                  error={item.grnQuantity > (item.remainingGrnQuantity !== undefined ? item.remainingGrnQuantity : item.quantity)}
                  helperText={item.grnQuantity > (item.remainingGrnQuantity !== undefined ? item.remainingGrnQuantity : item.quantity) ? "Cannot exceed remaining quantity" : ""}
                  sx={{ width: '100px' }}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default GRNItemTable;
