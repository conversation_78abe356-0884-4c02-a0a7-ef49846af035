import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Grid,
  Tab,
  Tabs,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton
} from '@mui/material';
import ReceiptIcon from '@mui/icons-material/Receipt';
import DescriptionIcon from '@mui/icons-material/Description';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import PersonIcon from '@mui/icons-material/Person';
import DateRangeIcon from '@mui/icons-material/DateRange';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import AddIcon from '@mui/icons-material/Add';
import ReceiptLongIcon from '@mui/icons-material/ReceiptLong';
import CloseIcon from '@mui/icons-material/Close';
import VisibilityIcon from '@mui/icons-material/Visibility';
import FileIcon from '@mui/icons-material/InsertDriveFile';
import apiService from '../../services/apiService';
import { formatDateTime, formatDate } from '../../utils/dateUtils';
import { getFileUrlFromKey } from '../../utils/fileUtils';
import InvoiceDialog from '../Invoice/InvoiceDialog';
import InvoiceDetails from '../Invoice/InvoiceDetails';
import { Delete } from '@mui/icons-material';

/**
 * Component to display the list of GRNs for a Purchase Order
 * 
 * @param {Object} props Component props
 * @param {Object} props.poDetails Purchase order details
 * @param {Function} props.onRefresh Callback to refresh parent component
 */
const GRNTable = ({ poDetails, onRefresh, canViewInvoice }) => {
  const [grns, setGRNs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [selectedGRN, setSelectedGRN] = useState(null);
  const [invoiceDialogOpen, setInvoiceDialogOpen] = useState(false);
  const [invoiceViewDialogOpen, setInvoiceViewDialogOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Open invoice dialog
  const handleAddInvoice = (grn) => {
    setSelectedGRN(grn);
    setInvoiceDialogOpen(true);
  };

  // Close invoice dialog
  const handleCloseInvoiceDialog = () => {
    setInvoiceDialogOpen(false);
    fetchGRNs();
    if (onRefresh) onRefresh();
  };

  // View invoice details
  const handleViewInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setInvoiceViewDialogOpen(true);
  };

  // Close invoice view dialog
  const handleCloseInvoiceViewDialog = () => {
    setInvoiceViewDialogOpen(false);
  };

  // Use callback to memoize the fetchGRNs function
  const fetchGRNs = useCallback(async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await apiService.purchaseOrderService.getGRNsForPO(poDetails?.id);
      
      if (response && response.grns) {
        setGRNs(response.grns);
      }
    } catch (error) {
      console.error('Error fetching GRNs:', error);
      setError('Failed to load GRNs. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [poDetails?.id]);

  // Submit invoice handler
  const handleSubmitInvoice = async (formData) => {
    try {
      await apiService.purchaseOrderService.addInvoiceToGRN(formData);
      await fetchGRNs();
      if (onRefresh) onRefresh();
      return true;
    } catch (error) {
      console.error('Error adding invoice:', error);
      setError('Failed to add invoice. Please try again.');
      throw error;
    }
  };

  const handleDeleteGRN = async (grnId) => {
    try {
      await apiService.deleteGRN(grnId);
      await fetchGRNs();
      if (onRefresh) onRefresh();
      return true;
    } catch (error) {
      console.error('Error deleting GRN:', error);
      setError('Failed to delete GRN. Please try again.');
      throw error;
    }
  };

  useEffect(() => {
    if (poDetails?.id) {
      fetchGRNs();
    }
  }, [poDetails?.id, fetchGRNs]);

  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" display="flex" alignItems="center">
          <ReceiptIcon sx={{ mr: 1 }} />
          Goods Receipt Notes
        </Typography>
      </Box>
      
      <Tabs 
        value={activeTab} 
        onChange={handleTabChange} 
        sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
      >
        <Tab 
          icon={<ReceiptIcon fontSize="small" />} 
          label="GRN List" 
          iconPosition="start"
        />
        {canViewInvoice && (
          <Tab 
            icon={<ReceiptLongIcon fontSize="small" />} 
            label="Invoices" 
            iconPosition="start"
          />
        )}
      </Tabs>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={24} />
        </Box>
      ) : error ? (
        <Alert severity="error">{error}</Alert>
      ) : grns.length === 0 ? (
        <Typography variant="body2" color="text.secondary" sx={{ p: 1 }}>
          No GRNs found for this purchase order.
        </Typography>
      ) : activeTab === 0 ? (
        // GRN Tab
        <Box>
          {grns.map((grn) => (
            <Accordion key={grn.id} sx={{ mb: 1 }}>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                sx={{
                  borderLeft: '4px solid',
                  borderLeftColor: 'primary.main',
                  '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography sx={{ width: '80px', flexShrink: 0, display: 'flex', alignItems: 'center' }}>
                      <ReceiptIcon fontSize="small" sx={{ mr: 1 }} />
                      GRN #{grn.id}
                    </Typography>
                    
                    <Typography sx={{ color: 'text.secondary', ml: 3, display: 'flex', alignItems: 'center' }}>
                      <PersonIcon fontSize="small" sx={{ mr: 0.5, color: 'primary.light' }} />
                      Created by: {grn.createdBy?.name || `User ${grn.createdBy?.id || 'Unknown'}`}
                      <DateRangeIcon fontSize="small" sx={{ ml: 2, mr: 0.5, color: 'primary.light' }} />
                      {formatDate(grn.createdAt)}
                    </Typography>
                  </Box>
                  {canViewInvoice && (
                  <Box>
                    {grn.invoice ? (
                      <Button
                        variant="outlined"
                        size="small"
                        startIcon={<VisibilityIcon />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleViewInvoice(grn);
                        }}
                      >
                        View Invoice
                      </Button>
                    ) : poDetails.status==="Open" && (
                      <Button
                        variant="contained"
                        size="small"
                        startIcon={<AddIcon />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAddInvoice(grn);
                        }}
                      >
                        Add Invoice
                      </Button>
                    )}

                    {!grn.invoice && (
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteGRN(grn.id);
                        }}
                        aria-label="delete"
                      >
                        <Delete color='error'/>
                      </IconButton>
                    )}

                  </Box>
                  )}
                </Box>
              </AccordionSummary>
              
              <AccordionDetails sx={{ pt: 0 }}>
                <Divider sx={{ mb: 2 }} />
                
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      GRN Details
                    </Typography>
                    
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Box sx={{ display: 'flex' }}>
                        <Typography variant="body2" sx={{ width: '140px', color: 'text.secondary' }}>
                          GRN ID:
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {grn.id}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex' }}>
                        <Typography variant="body2" sx={{ width: '140px', color: 'text.secondary' }}>
                          PO Number:
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {grn.purchaseOrder?.poNumber || `PO #${grn.poId}`}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex' }}>
                        <Typography variant="body2" sx={{ width: '140px', color: 'text.secondary' }}>
                          Created By:
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {grn.createdBy?.name || `User ID: ${grn.createdBy?.id || 'Unknown'}`}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex' }}>
                        <Typography variant="body2" sx={{ width: '140px', color: 'text.secondary' }}>
                          Created Date:
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {formatDateTime(grn.createdAt)}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex' }}>
                        <Typography variant="body2" sx={{ width: '140px', color: 'text.secondary' }}>
                          Updated Date:
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {formatDateTime(grn.updatedAt)}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom display="flex" alignItems="center">
                      <ShoppingCartIcon fontSize="small" sx={{ mr: 1 }} />
                      Received Items
                    </Typography>
                    
                    <TableContainer component={Paper} variant="outlined" sx={{ mt: 1 }}>
                      <Table size="small">
                        <TableHead>
                          <TableRow sx={{ backgroundColor: 'grey.100' }}>
                            <TableCell>Item Name</TableCell>
                            <TableCell>PO Quantity</TableCell>
                            <TableCell>GRN Quantity</TableCell>
                            <TableCell>UOM</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {grn.items.map((item) => (
                            <TableRow key={item.id}>
                              <TableCell>{item.poItem.itemName}</TableCell>
                              <TableCell>{item.poItem.quantity}</TableCell>
                              <TableCell>
                                <Typography fontWeight="medium" color="primary.main">
                                  {item.grnQuantity}
                                </Typography>
                              </TableCell>
                              <TableCell>{item.poItem.uom}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>
                  
                  {grn.fileKeys && grn.fileKeys.length > 0 && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                        Attachments
                      </Typography>
                      
                      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                        {grn.fileKeys.map((fileKey, index) => (
                          <Chip 
                            key={index}
                            label={fileKey.split('-').pop()}
                            icon={<DescriptionIcon />}
                            variant="outlined"
                            onClick={() => window.open(getFileUrlFromKey(fileKey), '_blank')}
                            clickable
                            sx={{ m: 0.5 }}
                          />
                        ))}
                      </Box>
                    </Grid>
                  )}
                  
                  {/* Invoice info if available */}
                  {grn.invoice && (
                    <Grid item xs={12}>
                      <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                        <Typography variant="subtitle2" gutterBottom color="primary">
                          Invoice Information
                        </Typography>
                        
                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={6} md={3}>
                            <Typography variant="body2" color="text.secondary">
                              Invoice Number
                            </Typography>
                            <Typography variant="body1" fontWeight="medium">
                              {grn.invoice.invoiceNumber}
                            </Typography>
                          </Grid>
                          
                          <Grid item xs={12} sm={6} md={3}>
                            <Typography variant="body2" color="text.secondary">
                              Invoice Date
                            </Typography>
                            <Typography variant="body1" fontWeight="medium">
                              {formatDate(grn.invoice.invoiceDate)}
                            </Typography>
                          </Grid>
                          
                          <Grid item xs={12} sm={6} md={3}>
                            <Typography variant="body2" color="text.secondary">
                              Invoice Value
                            </Typography>
                            <Typography variant="body1" fontWeight="medium">
                              ₹{grn.invoice.invoiceValue?.toFixed(2)}
                            </Typography>
                          </Grid>

                          <Grid item xs={12} sm={6} md={3}>
                            <Typography variant="body2" color="text.secondary">
                              Items
                            </Typography>
                            <Typography variant="body1" fontWeight="medium">
                              {grn.invoice.items?.length || 0} items
                            </Typography>
                          </Grid>
                          
                          {canViewInvoice && (
                          <Grid item xs={12} sm={6} md={12}>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Button
                                variant="outlined"
                                size="small"
                                startIcon={<VisibilityIcon />}
                                onClick={() => handleViewInvoice(grn)}
                              >
                                View Invoice Details
                              </Button>
                              {grn.fileKeys && grn.fileKeys.length > 0 && (
                                <Button
                                  variant="outlined"
                                  color="secondary"
                                  size="small"
                                  startIcon={<FileIcon />}
                                  onClick={() => handleViewInvoice(grn)}
                                >
                                  View Invoice Files ({grn.fileKeys.length})
                                </Button>
                              )}
                            </Box>
                          </Grid>
                          )}
                        </Grid>
                      </Box>
                    </Grid>
                  )}
                </Grid>
              </AccordionDetails>
            </Accordion>
          ))}
        </Box>
      ) : (
        // Invoices Tab
        <Box>
          {grns.filter(grn => grn.invoice).length === 0 ? (
            <Typography variant="body2" color="text.secondary" sx={{ p: 1 }}>
              No invoices found for this purchase order.
            </Typography>
          ) : (
            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: 'primary.light' }}>
                    <TableCell sx={{ color: 'white' }}>GRN #</TableCell>
                    <TableCell sx={{ color: 'white' }}>Invoice Number</TableCell>
                    <TableCell sx={{ color: 'white' }}>Invoice Date</TableCell>
                    <TableCell sx={{ color: 'white' }}>Invoice Value</TableCell>
                    <TableCell sx={{ color: 'white' }}>Files</TableCell>
                    <TableCell sx={{ color: 'white' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {grns.filter(grn => grn.invoice).map((grn) => (
                    <TableRow key={grn.id}>
                      <TableCell>GRN #{grn.id}</TableCell>
                      <TableCell>{grn.invoice.invoiceNumber}</TableCell>
                      <TableCell>{formatDate(grn.invoice.invoiceDate)}</TableCell>
                      <TableCell>₹{grn.invoice.invoiceValue?.toFixed(2)}</TableCell>
                      <TableCell>
                        {grn.invoice.items ? (
                          <Chip 
                            color="success" 
                            variant="outlined" 
                            size="small" 
                            label={`${grn.invoice.items.length} Items`} 
                            sx={{ mr: 1 }}
                          />
                        ) : null}
                        {grn.fileKeys && grn.fileKeys.length > 0 ? (
                          <Chip 
                            icon={<FileIcon />} 
                            label={`${grn.fileKeys.length} file${grn.fileKeys.length > 1 ? 's' : ''}`} 
                            color="primary" 
                            variant="outlined" 
                            size="small" 
                            onClick={() => handleViewInvoice(grn)} 
                          />
                        ) : (
                          <Typography variant="body2" color="text.secondary">No files</Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<VisibilityIcon />}
                            onClick={() => handleViewInvoice(grn)}
                          >
                            View Details
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Box>
      )}
      
      {/* Invoice Dialog */}
      {selectedGRN && (
        <InvoiceDialog
          open={invoiceDialogOpen}
          onClose={handleCloseInvoiceDialog}
          grn={selectedGRN}
          onSubmit={handleSubmitInvoice}
        />
      )}
      
      {/* Invoice View Dialog */}
      <Dialog
        open={invoiceViewDialogOpen}
        onClose={handleCloseInvoiceViewDialog}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" display="flex" alignItems="center">
              <ReceiptLongIcon sx={{ mr: 1 }} />
              Invoice Details
            </Typography>
            <IconButton onClick={handleCloseInvoiceViewDialog}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedInvoice && <InvoiceDetails invoice={selectedInvoice} />}
        </DialogContent>
      </Dialog>
    </Paper>
  );
};

export default GRNTable;
