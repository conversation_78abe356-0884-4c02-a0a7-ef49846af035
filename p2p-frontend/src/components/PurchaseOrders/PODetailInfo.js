import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid, 
  Chip 
} from '@mui/material';

/**
 * PODetailInfo component for displaying basic PO information
 * 
 * @param {Object} props
 * @param {Object} props.poDetails - Purchase Order details object
 */
const PODetailInfo = ({ poDetails }) => {
  if (!poDetails) return null;

  // Format date helper
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status color helper
  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'grn completed':
        return 'success';
      case 'pending grn':
        return 'warning';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle2" color="text.secondary">
            Purchase Order ID
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'medium', mb: 2 }}>
            {poDetails.id}
          </Typography>
          
          <Typography variant="subtitle2" color="text.secondary">
            PR ID
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'medium', mb: 2 }}>
            {poDetails.prId}
          </Typography>
          
          <Typography variant="subtitle2" color="text.secondary">
            Business Unit
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'medium', mb: 2 }}>
            {poDetails.businessUnit}
          </Typography>
          
          <Typography variant="subtitle2" color="text.secondary">
            Cost Center
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'medium', mb: 2 }}>
            {poDetails.costCenter}
          </Typography>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle2" color="text.secondary">
            Status
          </Typography>
          <Box sx={{ mb: 2 }}>
            <Chip 
              label={poDetails.status} 
              color={getStatusColor(poDetails.status)} 
              size="small"
            />
          </Box>
          
          <Typography variant="subtitle2" color="text.secondary">
            Created On
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'medium', mb: 2 }}>
            {formatDate(poDetails.createdOn)}
          </Typography>
          
          <Typography variant="subtitle2" color="text.secondary">
            Raised By
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'medium', mb: 2 }}>
            {poDetails.raisedBy}
          </Typography>
          
          <Typography variant="subtitle2" color="text.secondary">
            Total Amount
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'medium', mb: 2 }}>
            ₹{poDetails.totalAmount.toLocaleString('en-IN')}
          </Typography>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default PODetailInfo;
