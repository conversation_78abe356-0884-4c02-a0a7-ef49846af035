import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid 
} from '@mui/material';

/**
 * SelectedQuote component for displaying the selected vendor quote
 * 
 * @param {Object} props
 * @param {Object} props.quote - Selected quote object
 */
const SelectedQuote = ({ quote }) => {
  if (!quote) return null;

  // Format date helper
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Selected Vendor Quote
      </Typography>
      <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Vendor: <strong>{quote.vendorName}</strong>
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Amount: <strong>₹{quote.amount.toLocaleString()}</strong>
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Payment Terms: <strong>{quote.paymentTerms}</strong>
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              File: <Box component="span" sx={{ color: 'primary.main' }}>{quote.fileName}</Box>
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <Typography variant="caption" color="text.secondary">
              Uploaded on {formatDate(quote.uploadedOn)}
            </Typography>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default SelectedQuote;
