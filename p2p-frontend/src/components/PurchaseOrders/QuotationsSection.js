import React from 'react';
import { 
  Box, 
  Typography, 
  Paper,
  Divider,
  Grid
} from '@mui/material';
import BusinessIcon from '@mui/icons-material/Business';
import POQuotationCard from '../PODetail/POQuotationCard';

/**
 * QuotationsSection component for displaying all proposed quotes in PO details
 * 
 * @param {Object} props
 * @param {Array} props.quotes - Array of all quotations
 * @param {Object} props.selectedQuote - The selected quote for this PO
 * @param {String} props.currency - Currency to display amounts in
 */
const QuotationsSection = ({ quotes = [], selectedQuote, currency = 'INR' }) => {
  if (!quotes || quotes.length === 0) return null;

  // Make sure selectedQuote is in the quotes list
  const allQuotes = [...quotes];
  if (selectedQuote && !allQuotes.some(q => q.id === selectedQuote.id)) {
    allQuotes.push(selectedQuote);
  }

  return (
    <Paper
      variant="outlined"
      sx={{
        p: 3,
        borderColor: 'primary.light',
        mb: 3
      }}
    >
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <BusinessIcon color="primary" sx={{ mr: 1.5 }} />
          <Box>
            <Typography variant="h6" fontWeight="600" color="primary.main">
              Vendor Quotations
            </Typography>
            <Typography variant="body2" color="text.secondary">
              All proposed quotes with the selected quote highlighted
            </Typography>
          </Box>
        </Box>
      </Box>
      
      <Divider sx={{ mb: 3 }} />

      <Box>
        <POQuotationCard 
          vendorQuotes={allQuotes} 
          quotation={selectedQuote}
          disabled={true} 
          currency={currency}
        />
      </Box>
    </Paper>
  );
};

export default QuotationsSection;
