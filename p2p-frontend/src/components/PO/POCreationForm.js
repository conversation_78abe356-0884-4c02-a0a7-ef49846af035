import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import axiosInstance from '../../services/axiosInstance';
import { Box, Button, CircularProgress, TextField, Typography, Paper } from '@mui/material';

const PurchaseOrderForm = () => {
  const [formData, setFormData] = useState({
    vendorId: '',
    items: [],
    totalAmount: '',
    remarks: ''
  });
  const [submitting, setSubmitting] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSubmitting(true);

      const response = await axiosInstance.post('/api/purchase-orders', formData);

      // Show success notification
      enqueueSnackbar('Purchase Order created successfully', { variant: 'success' });

      // Redirect to PR listing page instead of dashboard
      navigate('/purchase-requisitions'); // Use the correct path for your PR listing page
    } catch (error) {
      console.error('Error creating PO:', error);
      enqueueSnackbar(error.response?.data?.message || 'Failed to create Purchase Order', {
        variant: 'error',
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>
        Create Purchase Order
      </Typography>
      <Box component="form" onSubmit={handleSubmit}>
        <TextField
          required
          fullWidth
          label="Vendor ID"
          name="vendorId"
          value={formData.vendorId}
          onChange={handleChange}
          sx={{ mb: 2 }}
        />
        <TextField
          required
          fullWidth
          label="Total Amount"
          name="totalAmount"
          value={formData.totalAmount}
          onChange={handleChange}
          sx={{ mb: 2 }}
        />
        <TextField
          fullWidth
          label="Remarks"
          name="remarks"
          value={formData.remarks}
          onChange={handleChange}
          multiline
          rows={3}
          sx={{ mb: 2 }}
        />
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            type="button"
            variant="outlined"
            onClick={() => navigate('/dashboard')}
            disabled={submitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            {submitting ? 'Submitting...' : 'Submit'}
          </Button>
        </Box>
      </Box>
    </Paper>
  );
};

export default PurchaseOrderForm;