import React, { useState } from 'react';
import { Box, Button, TextField, Stack, Chip } from '@mui/material';

// Utility for role name display
const getRoleDisplayName = (role) => {
  if (!role) return '';
  
  const roleMap = {
    'biz_fin': 'Business Finance',
    'finance': 'Finance'
  };
  return roleMap[role.toLowerCase()] || role;
};

const InvoiceApprovalActions = ({ invoiceId, onAction, loading, approvalRole }) => {
  const [remarks, setRemarks] = useState('');

  const handleApprove = () => {
    onAction('APPROVED', remarks);
  };
  const handleReject = () => {
    onAction('REJECTED', remarks);
  };

  const displayRole = getRoleDisplayName(approvalRole);

  return (
    <Box sx={{ mt: 2 }}>
      <Stack spacing={2} direction="column">
        <TextField
          label="Remarks"
          value={remarks}
          onChange={e => setRemarks(e.target.value)}
          size="small"
          multiline
          minRows={2}
        />
        <Stack direction="row" spacing={2}>
          <Button
            variant="contained"
            color="success"
            onClick={handleApprove}
            disabled={loading}
            startIcon={displayRole && <Chip size="small" label={displayRole} sx={{ color: 'white', bgcolor: 'rgba(255,255,255,0.2)' }} />}
          >
            Approve
          </Button>
          <Button
            variant="outlined"
            color="error"
            onClick={handleReject}
            disabled={loading}
          >
            Reject
          </Button>
        </Stack>
      </Stack>
    </Box>
  );
};

export default InvoiceApprovalActions;
