import React from 'react';
import { Box, Typography, Chip } from '@mui/material';

const ApprovalHeader = ({ title, subtitle, count }) => {
  return (
    <Box sx={{ mb: 4 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
        <Typography variant="h5" component="h1" fontWeight="bold">
          {title}
        </Typography>
        {count > 0 && (
          <Chip 
            label={count} 
            color="primary" 
            size="small" 
            sx={{ ml: 2 }}
          />
        )}
      </Box>
      {subtitle && (
        <Typography variant="body1" color="text.secondary">
          {subtitle}
        </Typography>
      )}
    </Box>
  );
};

export default ApprovalHeader;
