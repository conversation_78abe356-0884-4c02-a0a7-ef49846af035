import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Chip,
  Button,
  Divider
} from '@mui/material';
import { ReceiptLong as ReceiptLongIcon } from '@mui/icons-material';
import { getStatusColor } from '../../utils/statusConstants';

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const PRInfoCard = ({ prDetails, onViewPR }) => {
  if (!prDetails) return null;

  return (
    <Paper sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <ReceiptLongIcon sx={{ mr: 1, color: 'primary.main' }} />
        <Typography variant="h6">
          Related Purchase Request
        </Typography>
      </Box>
      
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="body2" color="text.secondary">PR ID</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <Typography variant="body1" fontWeight="medium">
                {prDetails.id}
              </Typography>
              <Button 
                variant="outlined" 
                size="small" 
                onClick={() => onViewPR(prDetails.id)} 
                sx={{ ml: 1 }}
              >
                View
              </Button>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="body2" color="text.secondary">Status</Typography>
            <Box sx={{ mt: 0.5 }}>
              <Chip 
                label={prDetails.status} 
                color={getStatusColor(prDetails.status)}
                size="small"
              />
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="body2" color="text.secondary">Created On</Typography>
            <Typography variant="body1">
              {formatDate(prDetails.createdAt)}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="body2" color="text.secondary">Created By</Typography>
            <Typography variant="body1">
              {prDetails.creator?.name || 'N/A'}
            </Typography>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <Typography variant="body2" color="text.secondary">Type</Typography>
            <Typography variant="body1">
              {prDetails.type || 'N/A'}
            </Typography>
          </Grid>
        </Grid>
      </Box>
      
      {prDetails.remarks && (
        <>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Justification
            </Typography>
            <Paper variant="outlined" sx={{ p: 2, backgroundColor: 'grey.50' }}>
              <Typography variant="body2">
                {prDetails.remarks}
              </Typography>
            </Paper>
          </Box>
        </>
      )}
    </Paper>
  );
};

export default PRInfoCard;
