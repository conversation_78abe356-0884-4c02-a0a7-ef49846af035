import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  Button,
  IconButton,
  Grid,
  Stack,
  Divider,
  Tooltip
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Print as PrintIcon,
  FileDownload as DownloadIcon
} from '@mui/icons-material';
import { getStatusColor } from '../../utils/statusConstants';
import { generatePOPDF, downloadPDF, printPDF } from '../../utils/pdfUtils';

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }) + ' ' + date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

const PODetailHeader = ({ 
  poDetails, 
  onBack, 
  onViewPR 
}) => {
  const handlePrint = () => {
    if (!poDetails) return;
    const doc = generatePOPDF(poDetails);
    printPDF(doc);
  };

  const handleDownload = () => {
    if (!poDetails) return;
    const doc = generatePOPDF(poDetails);
    downloadPDF(doc, `PO-${poDetails.poNumber || 'document'}`);
  };
  return (
    <>
      {/* Back button and title - matching PR detail header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={onBack} sx={{ mr: 1 }}>
          <ArrowBackIcon />
        </IconButton>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="h5">Purchase Order Details</Typography>
        </Box>
      </Box>
      
      {/* PO Details header with status and total amount */}
      <Box sx={{ 
        display: 'flex', 
        flexDirection: { xs: 'column', sm: 'row' }, 
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        mb: 3
      }}>
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Typography variant="h4" component="h1" fontWeight="medium">
              {poDetails.poNumber}
            </Typography>
            <Chip 
              label={poDetails.status} 
              color={getStatusColor(poDetails.status)}
              sx={{ ml: 2 }}
            />
          </Box>
          
          <Grid container spacing={2}>
            <Grid item>
              <Typography variant="body2" color="text.secondary">
                Created on: <strong>{formatDate(poDetails.createdAt)}</strong>
              </Typography>
            </Grid>
            <Grid item>
              <Typography variant="body2" color="text.secondary">
                PR #
                <Button 
                  variant="text" 
                  size="small" 
                  onClick={() => onViewPR(poDetails.prId)} 
                  sx={{ ml: 0.5, p: 0, minWidth: 'auto', textTransform: 'none' }}
                >
                  <strong>{poDetails.prId}</strong>
                </Button>
              </Typography>
            </Grid>
            <Grid item>
              <Chip 
                label={poDetails.type} 
                color="primary"
                variant="outlined"
                size="small"
                sx={{ fontWeight: 'bold' }}
              />
            </Grid>
          </Grid>
        </Box>
        
        <Box sx={{ mt: { xs: 2, sm: 0 } }}>
          <Box sx={{ textAlign: 'right' }}>
            <Typography variant="h5" color="primary" fontWeight="bold">
              ₹ {poDetails.totalAmount?.toLocaleString('en-IN')}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Total Amount
            </Typography>
            <Divider sx={{ my: 1 }} />
            <Stack direction="row" spacing={1} justifyContent="flex-end">
              <Tooltip title="Print PO">
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<PrintIcon />}
                  onClick={handlePrint}
                  sx={{ textTransform: 'none' }}
                >
                  Print
                </Button>
              </Tooltip>
              <Tooltip title="Download PDF">
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<DownloadIcon />}
                  onClick={handleDownload}
                  sx={{ textTransform: 'none' }}
                >
                  Download
                </Button>
              </Tooltip>
            </Stack>
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default PODetailHeader;
