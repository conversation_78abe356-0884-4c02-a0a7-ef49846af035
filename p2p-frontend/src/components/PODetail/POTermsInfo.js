import React from 'react';
import { Grid, Typography, Box, Divider } from '@mui/material';
import { LocalShipping as LocalShippingIcon, Payment as PaymentIcon } from '@mui/icons-material';

// Info item component for consistent styling with icon
const InfoItemWithIcon = ({ label, value, icon }) => (
  <Box sx={{ mb: 2, display: 'flex', alignItems: 'flex-start' }}>
    <Box sx={{ mr: 2, mt: 0.5, color: 'primary.main' }}>
      {icon}
    </Box>
    <Box>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        {label}
      </Typography>
      <Typography variant="body1" fontWeight="medium">
        {value || 'N/A'}
      </Typography>
    </Box>
  </Box>
);

const POTermsInfo = ({ poDetails }) => {
  return (
    <>
      <Typography variant="h6" gutterBottom>
        Terms & Conditions
      </Typography>
      <Divider sx={{ mb: 2 }} />
      
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6}>
          <InfoItemWithIcon 
            label="Payment Terms" 
            value={poDetails.paymentTerms} 
            icon={<PaymentIcon />}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <InfoItemWithIcon 
            label="Delivery Terms" 
            value={poDetails.deliveryTerms} 
            icon={<LocalShippingIcon />}
          />
        </Grid>
      </Grid>
    </>
  );
};

export default POTermsInfo;
