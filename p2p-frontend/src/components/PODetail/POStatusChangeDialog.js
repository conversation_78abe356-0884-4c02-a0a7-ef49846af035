import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  <PERSON>alogContent,
  DialogActions,
  DialogContentText,
  Button,
  TextField,
  CircularProgress
} from '@mui/material';

/**
 * Highly reusable component for displaying a confirmation dialog with remarks field
 * for any action that requires confirmation and remarks
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the dialog is open
 * @param {Function} props.onClose - Function to call when dialog is closed
 * @param {Function} props.onConfirm - Function to call when action is confirmed, with remarks
 * @param {boolean} props.loading - Whether the action is loading
 * @param {string} props.title - Dialog title
 * @param {string} props.message - Dialog message
 * @param {string} props.buttonText - Confirm button text
 * @param {string} props.loadingText - Button text when loading
 * @param {string} props.color - Button and icon color (primary, secondary, error, etc.)
 * @param {React.ReactNode} props.icon - Icon to display in dialog title
 * @param {string} props.remarksLabel - Label for remarks field
 * @param {string} props.remarksHelperText - Helper text when remarks are empty
 */
const StatusChangeDialog = ({ 
  open, 
  onClose, 
  onConfirm, 
  loading = false,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed with this action?',
  buttonText = 'Confirm',
  loadingText = 'Processing...',
  color = 'primary',
  icon = null,
  remarksLabel = 'Remarks *',
  remarksHelperText = 'Remarks are required'
}) => {
  const [remarks, setRemarks] = useState('');

  const handleConfirm = () => {
    onConfirm(remarks);
    setRemarks(''); // Reset remarks after submission
  };

  const handleClose = () => {
    setRemarks(''); // Reset remarks when closing
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {icon && icon}
        {title}
      </DialogTitle>
      
      <DialogContent>
        <DialogContentText>
          {message}
        </DialogContentText>
        
        <TextField
          autoFocus
          margin="dense"
          id="remarks"
          label={remarksLabel}
          type="text"
          fullWidth
          multiline
          rows={3}
          variant="outlined"
          value={remarks}
          onChange={(e) => setRemarks(e.target.value)}
          required
          error={!remarks.trim()}
          helperText={!remarks.trim() ? remarksHelperText : ''}
          sx={{ mt: 2 }}
        />
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleConfirm}
          color={color}
          variant="contained"
          startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
          disabled={loading || !remarks.trim()}
        >
          {loading ? loadingText : buttonText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default StatusChangeDialog;
