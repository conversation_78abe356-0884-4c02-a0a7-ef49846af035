import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Box,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Chip,
  Divider,
  Paper,
  CircularProgress
} from '@mui/material';
import axiosInstance from '../../services/axiosInstance';
import apiConfig from '../../config/api.config';
import { getStatusDisplay, getStatusColor } from '../../utils/statusConstants';

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return 'Pending';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }) + ' ' + date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

const getEventTypeLabel = (eventType) => {
  switch (eventType) {
    case 'PO_CREATED':
      return 'Purchase Order Created';
    case 'PO_APPROVAL':
      return 'Approval Step';
    case 'PO_REJECTED':
      return 'Purchase Order Rejected';
    default:
      return eventType?.replace(/_/g, ' ') || 'Unknown';
  }
};

const POApprovalHistory = ({ prId, timeline: timelineProp }) => {
  const [timeline, setTimeline] = useState(Array.isArray(timelineProp) ? timelineProp : []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // If timelineProp is provided, use it directly
    if (Array.isArray(timelineProp)) {
      setTimeline(timelineProp);
      setLoading(false);
      setError(null);
      return;
    }
    // Otherwise, fetch timeline from API using prId
    const fetchTimeline = async () => {
      if (!prId) return;
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get(
          apiConfig.endpoints.purchaseRequests.timeline(prId)
        );
        const timelineData = response.data?.timeline || [];
        setTimeline(timelineData);
      } catch (err) {
        console.error('Error fetching PR timeline for PO:', err);
        setError('Failed to load approval history');
        setTimeline([]);
      } finally {
        setLoading(false);
      }
    };
    fetchTimeline();
  }, [prId, timelineProp]);

  return (
    <Paper elevation={0} variant="outlined" sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        Approval History
      </Typography>
      <Divider sx={{ mb: 2 }} />
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
          <CircularProgress size={24} />
        </Box>
      ) : error ? (
        <Box sx={{ py: 2 }}>
          <Typography variant="body2" color="error">
            {error}
          </Typography>
        </Box>
      ) : timeline.length === 0 ? (
        <Box sx={{ py: 2 }}>
          <Typography variant="body2" color="text.secondary">
            No approval history available yet.
          </Typography>
        </Box>
      ) : (
        <Box sx={{ 
          maxHeight: '300px', 
          overflowY: 'auto',
          pr: 1,
          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#e0e0e0',
            borderRadius: '4px',
          }
        }}>
          <Stepper orientation="vertical" activeStep={-1}>
            {timeline.map((event, index) => (
              <Step key={index} expanded>
                <StepLabel>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                    <Typography variant="body1">{getEventTypeLabel(event.eventType)}</Typography>
                    <Chip 
                      label={getStatusDisplay(event.status) || event.status} 
                      size="small" 
                      color={getStatusColor(event.status)} 
                    />
                  </Box>
                </StepLabel>
                <StepContent>
                  <Box sx={{ ml: 1 }}>
                    <Box sx={{ display: 'flex', gap: 2, mt: 0.5 }}>
                      {event.actioner && (
                        <Typography variant="body2" color="text.secondary">
                          By: <strong>{`${event.actioner.firstName || ''} ${event.actioner.lastName || ''}`}</strong>
                        </Typography>
                      )}
                    </Box>
                    {event.actionedAt && (
                      <Typography variant="body2" color="text.secondary" sx={{ fontFamily: 'monospace' }}>
                        {formatDate(event.actionedAt)}
                      </Typography>
                    )}
                    {/* {event.role && (
                      <Typography variant="body2" color="text.secondary">
                        Role: {event.role.replace(/_/g, ' ')}
                      </Typography>
                    )} */}
                    {event.remarks && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        Remarks: {event.remarks}
                      </Typography>
                    )}
                  </Box>
                </StepContent>
              </Step>
            ))}
          </Stepper>
        </Box>
      )}
    </Paper>
  );
};

export default POApprovalHistory;
