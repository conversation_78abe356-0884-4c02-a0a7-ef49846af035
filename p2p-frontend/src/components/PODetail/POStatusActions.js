import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Box,
  Button,
  CircularProgress
} from '@mui/material';
import {
  Lock as LockIcon,
  LockOpen as LockOpenIcon
} from '@mui/icons-material';
import apiService from '../../services/apiService';
import StatusChangeDialog from './POStatusChangeDialog';

/**
 * Component for displaying and changing purchase order status
 * 
 * @param {Object} props - Component props
 * @param {Object} props.poDetails - Purchase order details
 * @param {Function} props.onStatusChange - Callback when status changes
 * @param {Function} props.onActionSuccess - Callback to show success/error messages
 */
const POStatusActions = ({ poDetails, onStatusChange, onActionSuccess }) => {
  const [isActionLoading, setIsActionLoading] = useState(false);
  // Dialog state - combined for both close and reopen actions
  const [statusDialog, setStatusDialog] = useState({
    open: false,
    type: null // 'close' or 'reopen'
  });

  // Handle closing a PO
  const handleClosePO = async (remarks) => {
    try {
      setIsActionLoading(true);
      
      // Call API to update PO status to Closed with the provided remarks
      await apiService.updatePOStatus(poDetails.id, 'Closed', remarks || 'PO closed by user');
      
      // Show success message
      onActionSuccess({
        type: 'success',
        message: 'Purchase Order closed successfully'
      });
      
      // Notify parent component to refresh data
      onStatusChange();
    } catch (error) {
      console.error('Error closing PO:', error);
      onActionSuccess({
        type: 'error',
        message: error.response?.data?.message || 'Failed to close Purchase Order'
      });
    } finally {
      setIsActionLoading(false);
      setStatusDialog({ open: false, type: null });
    }
  };
  
  // Handle reopening a PO
  const handleReopenPO = async (remarks) => {
    try {
      setIsActionLoading(true);
      
      // Call API to update PO status to Open with mandatory remarks
      await apiService.updatePOStatus(poDetails.id, 'Open', remarks);
      
      // Show success message
      onActionSuccess({
        type: 'success',
        message: 'Purchase Order reopened successfully'
      });
      
      // Notify parent component to refresh data
      onStatusChange();
    } catch (error) {
      console.error('Error reopening PO:', error);
      onActionSuccess({
        type: 'error',
        message: error.response?.data?.message || 'Failed to reopen Purchase Order'
      });
    } finally {
      setIsActionLoading(false);
      setStatusDialog({ open: false, type: null });
    }
  };

  return (
    <>
      
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
          {poDetails.status === 'Open' ? (
            <Button
              variant="outlined"
              color="error"
              startIcon={isActionLoading ? <CircularProgress size={20} /> : <LockIcon />}
              onClick={() => setStatusDialog({ open: true, type: 'close' })}
              fullWidth
              disabled={isActionLoading}
            >
              {isActionLoading ? 'Closing...' : 'Close PO'}
            </Button>
          ) : poDetails.status === 'Closed' ? (
            <Button
              variant="outlined"
              color="primary"
              startIcon={isActionLoading ? <CircularProgress size={20} /> : <LockOpenIcon />}
              onClick={() => setStatusDialog({ open: true, type: 'reopen' })}
              fullWidth
              disabled={isActionLoading}
            >
              {isActionLoading ? 'Reopening...' : 'Reopen PO'}
            </Button>
          ) : (
            <Typography variant="body2" color="textSecondary" align="center">
              Status change not available for {poDetails.status} POs
            </Typography>
          )}
        </Box>

      {/* Reusable status change dialog component */}
      {statusDialog.open && (
        <StatusChangeDialog
          open={statusDialog.open}
          onClose={() => setStatusDialog({ open: false, type: null })}
          onConfirm={statusDialog.type === 'close' ? handleClosePO : handleReopenPO}
          loading={isActionLoading}
          
          // Pass all UI elements as props instead of determining them inside the component
          title={statusDialog.type === 'close' ? 'Close Purchase Order' : 'Reopen Purchase Order'}
          message={statusDialog.type === 'close' 
            ? 'Are you sure you want to close this Purchase Order? This will prevent further GRNs from being created.'
            : 'Are you sure you want to reopen this Purchase Order? This will allow creating additional GRNs.'}
          buttonText={statusDialog.type === 'close' ? 'Close PO' : 'Reopen PO'}
          loadingText={statusDialog.type === 'close' ? 'Closing...' : 'Reopening...'}
          color={statusDialog.type === 'close' ? 'error' : 'primary'}
          icon={statusDialog.type === 'close' 
            ? <LockIcon color="error" /> 
            : <LockOpenIcon color="primary" />}
          remarksLabel="Remarks *"
          remarksHelperText="Remarks are required for status change"
        />
      )}
    </>
  );
};

export default POStatusActions;
