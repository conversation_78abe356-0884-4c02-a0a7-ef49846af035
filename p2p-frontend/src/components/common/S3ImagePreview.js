import React, { useState, useEffect } from 'react';
import { Box, Typography, CircularProgress } from '@mui/material';
import { BrokenImage as BrokenImageIcon } from '@mui/icons-material';

/**
 * Component to handle displaying images from S3 with complex URLs
 * Handles loading states and errors gracefully
 */
const S3ImagePreview = ({ 
  imageUrl, 
  alt = 'Preview image',
  maxWidth = '100%',
  maxHeight = 300,
  sx = {}
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  // Handle image load event
  const handleImageLoad = () => {
    setLoading(false);
    setError(false);
  };

  // Handle image error event
  const handleImageError = () => {
    setLoading(false);
    setError(true);
  };

  // Reset loading state when imageUrl changes
  useEffect(() => {
    setLoading(true);
    setError(false);
  }, [imageUrl]);

  return (
    <Box 
      sx={{ 
        position: 'relative',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        border: '1px solid',
        borderColor: error ? 'error.light' : 'grey.300',
        borderRadius: 1,
        p: 1,
        minHeight: 100,
        ...sx
      }}
    >
      {loading && (
        <Box sx={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <CircularProgress size={24} />
        </Box>
      )}
      
      {error ? (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 2 }}>
          <BrokenImageIcon color="error" sx={{ fontSize: 40, mb: 1 }} />
          <Typography variant="caption" color="error">
            Unable to load image
          </Typography>
        </Box>
      ) : (
        <Box sx={{ 
          width: '100%', 
          visibility: loading ? 'hidden' : 'visible',
          display: 'flex',
          justifyContent: 'center'
        }}>
          <img
            src={imageUrl}
            alt={alt}
            onLoad={handleImageLoad}
            onError={handleImageError}
            style={{ 
              maxWidth: maxWidth, 
              maxHeight: maxHeight,
              objectFit: 'contain'
            }}
          />
        </Box>
      )}
    </Box>
  );
};

export default S3ImagePreview;
