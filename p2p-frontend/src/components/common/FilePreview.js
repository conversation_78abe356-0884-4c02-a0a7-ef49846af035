import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  IconButton,
  Box,
  Typography,
  Tooltip,
  Button,
  CircularProgress
} from '@mui/material';
import {
  Close as CloseIcon,
  Download as DownloadIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Refresh as ResetIcon,
  PictureAsPdf,
  InsertDriveFile,
  Image
} from '@mui/icons-material';

// Return appropriate icon based on file name or content type
export const getFileIcon = (fileName = '', contentType = '') => {
  const fileNameLower = fileName.toLowerCase();
  const contentTypeLower = contentType.toLowerCase();
  
  if (
    fileNameLower.endsWith('.pdf') ||
    contentTypeLower === 'application/pdf'
  ) {
    return <PictureAsPdf color="error" />;
  } else if (
    fileNameLower.endsWith('.jpg') ||
    fileNameLower.endsWith('.jpeg') ||
    fileNameLower.endsWith('.png') ||
    fileNameLower.endsWith('.gif') ||
    fileNameLower.endsWith('.webp') ||
    contentTypeLower.startsWith('image/')
  ) {
    return <Image color="primary" />;
  } else {
    return <InsertDriveFile color="action" />;
  }
};

// Format file size in human-readable format
export const formatFileSize = (bytes) => {
  if (!bytes || isNaN(bytes)) return 'Unknown size';
  
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 Bytes';
  
  const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)), 10);
  if (i === 0) return `${bytes} ${sizes[i]}`;
  
  return `${(bytes / (1024 ** i)).toFixed(2)} ${sizes[i]}`;
};

// Check if file is an image based on file name or content type
export const isImageFile = (fileName = '', contentType = '') => {
  const fileNameLower = fileName.toLowerCase();
  const contentTypeLower = contentType.toLowerCase();
  
  return (
    fileNameLower.endsWith('.jpg') ||
    fileNameLower.endsWith('.jpeg') ||
    fileNameLower.endsWith('.png') ||
    fileNameLower.endsWith('.gif') ||
    fileNameLower.endsWith('.webp') ||
    contentTypeLower.startsWith('image/')
  );
};

// Check if file is a PDF based on file name or content type
export const isPdfFile = (fileName = '', contentType = '') => {
  const fileNameLower = fileName.toLowerCase();
  const contentTypeLower = contentType.toLowerCase();
  
  return (
    fileNameLower.endsWith('.pdf') ||
    contentTypeLower === 'application/pdf'
  );
};

/**
 * A versatile file preview component that supports various file types
 * including images, PDFs, and other documents
 * @param {boolean} stopLoadingOnNonPreviewable - If true, stops showing loading indicator for non-previewable files
 * @param {boolean} openDownloadsInNewTab - If true, opens downloads in a new tab
 */
const FilePreview = ({ 
  open, 
  onClose, 
  fileUrl, 
  fileName, 
  contentType, 
  title,
  stopLoadingOnNonPreviewable = false,
  openDownloadsInNewTab = false
}) => {
  const [scale, setScale] = useState(1);
  const [loading, setLoading] = useState(true);
  
  const isImage = isImageFile(fileName, contentType);
  const isPdf = isPdfFile(fileName, contentType);
  
  // If content is not previewable and stopLoadingOnNonPreviewable is true, stop loading
  useEffect(() => {
    if (stopLoadingOnNonPreviewable && !isImage && !isPdf) {
      setLoading(false);
    }
  }, [stopLoadingOnNonPreviewable, isImage, isPdf]);
  // Handle zoom in
  const handleZoomIn = () => {
    setScale(prev => Math.min(prev + 0.25, 3));
  };
  
  // Handle zoom out
  const handleZoomOut = () => {
    setScale(prev => Math.max(prev - 0.25, 0.5));
  };
  
  // Reset zoom level
  const handleReset = () => {
    setScale(1);
  };
  
  const handleDownload = () => {
    if (!fileUrl) return;
    
    if (openDownloadsInNewTab) {
      // Open in new tab
      window.open(fileUrl, '_blank');
    } else {
      // Create a temporary anchor element
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName || 'file';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 1,
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between',
        alignItems: 'center',
        borderBottom: '1px solid',
        borderColor: 'divider',
        py: 1.5
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, maxWidth: '80%' }}>
          {getFileIcon(fileName, contentType)}
          <Typography variant="h6" component="div" noWrap>
            {title || fileName || 'File Preview'}
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon fontSize="small" />
        </IconButton>
      </DialogTitle>
      
      <DialogContent sx={{ p: 0, position: 'relative', overflow: 'hidden' }}>
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          minHeight: '400px',
          maxHeight: 'calc(90vh - 120px)',
          bgcolor: 'action.hover',
          position: 'relative',
          p: 1
        }}>
          {loading && (
            <Box sx={{ 
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 10
            }}>
              <CircularProgress size={40} />
            </Box>
          )}
          
          {isImage ? (
            <Box sx={{ overflow: 'auto', height: '100%', width: '100%', display: 'flex', justifyContent: 'center' }}>
              <img
                src={fileUrl}
                alt={title || fileName || "Image preview"}
                style={{
                  maxWidth: `${100 * scale}%`,
                  maxHeight: `${100 * scale}%`,
                  transition: 'transform 0.2s ease',
                  transform: `scale(${scale})`,
                  transformOrigin: 'center',
                }}
                onLoad={() => setLoading(false)}
              />
            </Box>
          ) : isPdf ? (
            <Box sx={{ width: '100%', height: '100%' }}>
              <iframe
                src={fileUrl}
                title={fileName || "PDF preview"}
                width="100%"
                height="100%"
                style={{ border: 'none', backgroundColor: 'white', minHeight: '500px' }}
                onLoad={() => setLoading(false)}
              />
            </Box>
          ) : (
            <Box sx={{ textAlign: 'center', p: 3 }}>
              <Box sx={{ fontSize: 64, mb: 2 }}>
                {getFileIcon(fileName, contentType)}
              </Box>
              <Typography variant="body1" gutterBottom>
                Preview not available for this file type
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {fileName || 'Unknown file'}
              </Typography>
              <Button
                variant="contained"
                color="primary"
                onClick={handleDownload}
                startIcon={<DownloadIcon />}
                sx={{ mt: 2 }}
              >
                Download File
              </Button>
            </Box>
          )}
        </Box>
        
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          p: 1.5,
          borderTop: '1px solid',
          borderColor: 'divider'
        }}>
          {isImage && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Tooltip title="Zoom in">
                <IconButton onClick={handleZoomIn} size="small" color="primary">
                  <ZoomInIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Zoom out">
                <IconButton onClick={handleZoomOut} size="small" color="primary">
                  <ZoomOutIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Reset zoom">
                <IconButton onClick={handleReset} size="small" color="primary">
                  <ResetIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Typography variant="body2" color="text.secondary">
                {Math.round(scale * 100)}%
              </Typography>
            </Box>
          )}
          
          {/* Push download button to right if no zoom controls */}
          <Box sx={{ ml: isImage ? 0 : 'auto' }}>
            <Button
              startIcon={<DownloadIcon />}
              onClick={handleDownload}
              variant="outlined"
              size="small"
              disabled={!fileUrl}
            >
              Download
            </Button>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

// Export the main component
export default FilePreview;
