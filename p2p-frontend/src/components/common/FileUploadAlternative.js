import React, { useState, useRef, forwardRef, useImperative<PERSON><PERSON>le } from 'react';
import {
  Box,
  Button,
  Typography,
  Alert,
  CircularProgress
} from '@mui/material';
import { CloudUpload as CloudUploadIcon } from '@mui/icons-material';
import axiosInstance from '../../services/axiosInstance';

/**
 * Simplified file upload component focused only on getting uploads working
 */
const FileUploadAlternative = forwardRef(({ 
  multiple = false,
  onFilesUploaded,
  onError,
  acceptedFileTypes = "*"
}, ref) => {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(null);
  const fileInputRef = useRef(null);

  const handleUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    try {
      setUploading(true);
      setError(null);
      
      const formData = new FormData();
      
      // Changed field name from 'files' to 'file'
      files.forEach(file => {
        console.log(`Adding file:`, file.name, file.size, file.type);
        formData.append('file', file); // Changed from 'files' to 'file'
      });

      console.log(`Uploading ${files.length} files`);
      
      // Don't set any headers - let browser handle multipart boundaries
      const response = await axiosInstance.post('/api/documents/upload', formData);
      
      console.log('Upload successful:', response.data);
      
      if (response.data?.files) {
        if (onFilesUploaded) onFilesUploaded(response.data.files);
        return { success: true, files: response.data.files };
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Upload failed:', error);
      console.error('Error details:', error.response?.data);
      
      const errorMsg = error.response?.data?.message || 'Failed to upload files';
      setError(errorMsg);
      if (onError) onError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setUploading(false);
    }
  };

  useImperativeHandle(ref, () => ({
    triggerFileSelect: () => fileInputRef.current.click()
  }));

  return (
    <Box>
      <input
        ref={fileInputRef}
        type="file"
        multiple={multiple}
        accept={acceptedFileTypes}
        onChange={handleUpload}
        style={{ display: 'none' }}
      />
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Button
        variant="contained"
        color="primary"
        startIcon={uploading ? <CircularProgress size={16} color="inherit" /> : <CloudUploadIcon />}
        onClick={() => fileInputRef.current.click()}
        disabled={uploading}
      >
        {uploading ? 'Uploading...' : (multiple ? 'Upload Files' : 'Upload File')}
      </Button>
    </Box>
  );
});

export default FileUploadAlternative;
