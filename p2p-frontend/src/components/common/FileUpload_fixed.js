import React, { useState, useRef, forwardRef, useImperative<PERSON><PERSON>le } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  IconButton,
  CircularProgress,
  Grid,
  Alert,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Tooltip
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  Description as DescriptionIcon,
  Image as ImageIcon,
  InsertDriveFile as FileIcon,
  Close as CloseIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { getFileUrlFromKey, isImageFile } from '../../utils/fileUtils';
import axiosInstance from '../../services/axiosInstance';

/**
 * Reusable file upload component with preview and delete capabilities
 * 
 * @param {Object} props Component props
 * @param {Boolean} props.multiple Allow multiple file uploads
 * @param {Function} props.onFilesSelected Callback when files are selected, receives array of File objects
 * @param {Function} props.onFilesUploaded Callback when files are uploaded, receives array of file objects with fileKey
 * @param {Function} props.onError Callback when error occurs
 * @param {Array} props.initialFiles Initial files to display (optional) [{fileKey, fileName, fileUrl, contentType}]
 * @param {String} props.acceptedFileTypes File types to accept (e.g. "image/*,.pdf")
 * @param {Number} props.maxFileSize Maximum file size in bytes (default 10MB)
 * @param {Boolean} props.showPreview Show preview of uploaded files
 * @param {Boolean} props.disabled Disable the upload functionality
 */
const FileUpload = forwardRef((props, ref) => {
  const {
    multiple = false,
    onFilesSelected,
    onFilesUploaded,
    onError,
    initialFiles = [],
    acceptedFileTypes = "*",
    maxFileSize = 10 * 1024 * 1024, // 10MB
    showPreview = true,
    disabled = false
  } = props;

  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploadedFiles, setUploadedFiles] = useState(initialFiles || []);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewFile, setPreviewFile] = useState(null);
  
  const fileInputRef = useRef(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    uploadFiles: handleUpload,
    getSelectedFiles: () => selectedFiles,
    clearSelectedFiles: () => setSelectedFiles([])
  }));

  // Handle file selection via input
  const handleFileChange = (event) => {
    const files = Array.from(event.target.files);
    
    if (files.length === 0) return;
    
    // Check file size
    const oversizedFiles = files.filter(file => file.size > maxFileSize);
    if (oversizedFiles.length > 0) {
      const errorMsg = `File${oversizedFiles.length > 1 ? 's' : ''} exceed${oversizedFiles.length === 1 ? 's' : ''} size limit of ${(maxFileSize / (1024 * 1024)).toFixed(1)}MB: ${oversizedFiles.map(f => f.name).join(', ')}`;
      setError(errorMsg);
      if (onError) onError(errorMsg);
      return;
    }
    
    // If not multiple, replace selected files, otherwise add to them
    const newSelectedFiles = multiple ? [...selectedFiles, ...files] : files;
    setSelectedFiles(newSelectedFiles);
    setError(null);
    
    // Notify parent component of selected files
    if (onFilesSelected) {
      onFilesSelected(newSelectedFiles);
    }
  };
  
  // Trigger file input click
  const handleSelectFiles = () => {
    fileInputRef.current.click();
  };
  
  // Upload files
  const handleUpload = async (filesToUpload = null) => {
    // Use provided files or fall back to selected files
    const files = filesToUpload || selectedFiles;
    
    if (files.length === 0) return { success: false, error: 'No files to upload' };
    
    try {
      setUploading(true);
      setError(null);
      
      const formData = new FormData();
      
      // Changed field name from 'files' to 'file'
      files.forEach((file) => {
        console.log(`Adding file:`, file.name, file.size);
        formData.append('file', file); // Changed from 'files' to 'file'
      });
      
      // Don't set Content-Type header - let browser handle it with boundary
      const response = await axiosInstance.post('/api/documents/upload', formData);
      
      console.log('Upload response:', response.data);
      
      if (response.data && response.data.files) {
        const newFiles = response.data.files;
        
        setUploadedFiles(prev => multiple ? [...prev, ...newFiles] : newFiles);
        setSelectedFiles([]);
        
        if (onFilesUploaded) {
          onFilesUploaded(multiple ? [...uploadedFiles, ...newFiles] : newFiles);
        }
        
        return { success: true, files: newFiles };
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      const errorMsg = error.response?.data?.message || 'Failed to upload files';
      setError(errorMsg);
      if (onError) onError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setUploading(false);
    }
  };
  
  // Remove a selected file (before upload)
  const handleRemoveSelectedFile = (index) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);
    if (onFilesSelected) {
      onFilesSelected(newFiles);
    }
  };
  
  // Remove an uploaded file
  const handleRemoveUploadedFile = (index) => {
    setUploadedFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
    
    if (onFilesUploaded) {
      const updatedFiles = uploadedFiles.filter((_, i) => i !== index);
      onFilesUploaded(updatedFiles);
    }
  };
  
  // Open file preview
  const handleOpenPreview = (file) => {
    setPreviewFile(file);
    setPreviewOpen(true);
  };
  
  // Close file preview
  const handleClosePreview = () => {
    setPreviewOpen(false);
    setPreviewFile(null);
  };
  
  // Format file size for display
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  // Determine file icon based on content type
  const getFileIcon = (file) => {
    const fileName = file.fileName || file.name || '';
    
    if (isImageFile(fileName)) {
      return <ImageIcon fontSize="large" />;
    }
    
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    switch(extension) {
      case 'pdf':
        return <DescriptionIcon fontSize="large" color="error" />;
      case 'doc':
      case 'docx':
        return <DescriptionIcon fontSize="large" color="primary" />;
      case 'xls':
      case 'xlsx':
        return <DescriptionIcon fontSize="large" color="success" />;
      case 'ppt':
      case 'pptx':
        return <DescriptionIcon fontSize="large" color="warning" />;
      default:
        return <FileIcon fontSize="large" />;
    }
  };

  return (
    <Box>
      {/* File selection section */}
      <Box sx={{ mb: 2 }}>
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={acceptedFileTypes}
          onChange={handleFileChange}
          style={{ display: 'none' }}
          disabled={disabled}
        />
        
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<CloudUploadIcon />}
            onClick={handleSelectFiles}
            disabled={disabled || uploading}
          >
            {multiple ? 'Select Files' : 'Select File'}
          </Button>
          
          {uploading && (
            <CircularProgress size={24} sx={{ ml: 2 }} />
          )}
        </Box>
        
        {/* Error message */}
        {error && (
          <Alert severity="error" onClose={() => setError(null)} sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
      </Box>
      
      {/* Selected files (waiting to be uploaded) */}
      {selectedFiles.length > 0 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Selected Files:
          </Typography>
          
          <Paper variant="outlined" sx={{ p: 1, bgcolor: 'background.paper' }}>
            {selectedFiles.map((file, index) => (
              <Box 
                key={index} 
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  p: 1,
                  borderBottom: index < selectedFiles.length - 1 ? '1px solid #eee' : 'none'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', flex: 1, overflow: 'hidden' }}>
                  {getFileIcon({ fileName: file.name })}
                  <Box sx={{ ml: 1, overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>
                    <Typography variant="body2" noWrap>{file.name}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatFileSize(file.size)}
                    </Typography>
                  </Box>
                </Box>
                
                <IconButton 
                  size="small" 
                  onClick={() => handleRemoveSelectedFile(index)}
                  disabled={disabled || uploading}
                  color="error"
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Box>
            ))}
          </Paper>
        </Box>
      )}
      
      {/* Uploaded files */}
      {showPreview && uploadedFiles.length > 0 && (
        <Box sx={{ mt: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            Uploaded Files:
          </Typography>
          
          <Grid container spacing={2}>
            {uploadedFiles.map((file, index) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                <Card variant="outlined" sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  {isImageFile(file.fileName) && file.fileUrl ? (
                    <CardMedia
                      component="img"
                      image={file.fileUrl || getFileUrlFromKey(file.fileKey)}
                      alt={file.fileName}
                      sx={{ height: 140, objectFit: 'cover' }}
                      onClick={() => handleOpenPreview(file)}
                    />
                  ) : (
                    <Box 
                      sx={{ 
                        height: 140, 
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        bgcolor: 'action.hover' 
                      }}
                    >
                      {getFileIcon(file)}
                    </Box>
                  )}
                  
                  <CardContent sx={{ py: 1 }}>
                    <Tooltip title={file.fileName}>
                      <Typography variant="body2" noWrap>
                        {file.fileName}
                      </Typography>
                    </Tooltip>
                    {file.fileSize && (
                      <Typography variant="caption" color="text.secondary">
                        {formatFileSize(file.fileSize)}
                      </Typography>
                    )}
                  </CardContent>
                  
                  <CardActions sx={{ justifyContent: 'space-between', pt: 0 }}>
                    <IconButton 
                      size="small"
                      onClick={() => handleOpenPreview(file)}
                    >
                      <VisibilityIcon fontSize="small" />
                    </IconButton>
                    
                    <IconButton 
                      size="small" 
                      color="error"
                      onClick={() => handleRemoveUploadedFile(index)}
                      disabled={disabled}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
      
      {/* Preview Dialog */}
      {previewOpen && previewFile && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            bgcolor: 'rgba(0, 0, 0, 0.7)',
            zIndex: 9999,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            p: 3
          }}
          onClick={handleClosePreview}
        >
          <Box 
            sx={{ 
              position: 'absolute', 
              top: 16, 
              right: 16, 
              bgcolor: 'background.paper',
              borderRadius: '50%'
            }}
          >
            <IconButton onClick={handleClosePreview}>
              <CloseIcon />
            </IconButton>
          </Box>
          
          <Box 
            sx={{ 
              maxWidth: '90%', 
              maxHeight: '90%', 
              bgcolor: 'background.paper',
              borderRadius: 1,
              overflow: 'auto',
              p: isImageFile(previewFile.fileName) ? 0 : 3,
              textAlign: 'center'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {isImageFile(previewFile.fileName) ? (
              <img 
                src={previewFile.fileUrl || getFileUrlFromKey(previewFile.fileKey)}
                alt={previewFile.fileName}
                style={{ maxWidth: '100%', maxHeight: '80vh' }}
              />
            ) : (
              <>
                <Box sx={{ mb: 3 }}>
                  {getFileIcon(previewFile)}
                  <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                    {previewFile.fileName}
                  </Typography>
                  {previewFile.fileSize && (
                    <Typography variant="body2" color="text.secondary">
                      {formatFileSize(previewFile.fileSize)}
                    </Typography>
                  )}
                </Box>
                
                <Button 
                  variant="contained"
                  href={previewFile.fileUrl || getFileUrlFromKey(previewFile.fileKey)}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Open File
                </Button>
              </>
            )}
          </Box>
        </Box>
      )}
    </Box>
  );
});

export default FileUpload;
