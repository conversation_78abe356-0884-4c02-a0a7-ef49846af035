import React from 'react';
import { Box, Typography, Paper } from '@mui/material';

/**
 * DashboardTiles component displays summary information
 */
const DashboardTiles = () => {
  return (
    <Paper
      elevation={0}
      sx={{
        p: 3,
        mb: 2,
        borderRadius: 2,
        border: '1px solid rgba(0, 0, 0, 0.08)',
        backgroundColor: '#FAFAFA'
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1
        }}
      >
        <Typography variant="h6" color="text.primary" sx={{ mb: 1 }}>
          Procurement Dashboard
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Welcome to your procurement dashboard. Here you can manage all your purchase requests and orders.
          Use the table below to view and filter your purchase requests.
        </Typography>
      </Box>
    </Paper>
  );
};

export default DashboardTiles;
