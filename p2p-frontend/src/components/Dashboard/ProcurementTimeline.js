import React from 'react';
import { Box, Stepper, Step, StepLabel, StepConnector, styled } from '@mui/material';
import {
  NoteAdd as RequestIcon,
  Approval as ApprovalIcon,
  RequestQuote as QuoteIcon,
  Handshake as VendorIcon,
  Receipt as POIcon,
  Inventory as GRNIcon,
  Payments as PaymentIcon,
  TaskAlt as CloseIcon
} from '@mui/icons-material';

// Custom styled connector for the stepper
const ColorlibConnector = styled(StepConnector)(() => ({
  [`&.MuiStepConnector-alternativeLabel`]: {
    top: 22,
  },
  [`&.MuiStepConnector-active`]: {
    [`& .MuiStepConnector-line`]: {
      backgroundColor: '#4CAF50',
    },
  },
  [`&.MuiStepConnector-completed`]: {
    [`& .MuiStepConnector-line`]: {
      backgroundColor: '#4CAF50',
    },
  },
  [`& .MuiStepConnector-line`]: {
    height: 2,
    border: 0,
    backgroundColor: '#4CAF50',
    borderRadius: 1,
  },
}));

// Custom styled step icon
const ColorlibStepIconRoot = styled('div')(() => ({
  backgroundColor: '#E8F5E9',
  zIndex: 1,
  color: '#4CAF50',
  width: 44,
  height: 44,
  display: 'flex',
  borderRadius: '50%',
  justifyContent: 'center',
  alignItems: 'center',
  border: '2px solid #4CAF50',
}));

// Custom step icon component
function ColorlibStepIcon(props) {
  const { active, completed, className, icon } = props;

  const icons = {
    1: <RequestIcon />,
    2: <ApprovalIcon />,
    3: <QuoteIcon />,
    4: <VendorIcon />,
    5: <POIcon />,
    6: <ApprovalIcon />,
    7: <GRNIcon />,
    8: <PaymentIcon />,
    9: <CloseIcon />,
  };

  return (
    <ColorlibStepIconRoot ownerState={{ completed, active }} className={className}>
      {icons[icon]}
    </ColorlibStepIconRoot>
  );
}

/**
 * ProcurementTimeline component displays the procurement process flow
 */
const ProcurementTimeline = () => {
  // Define the procurement process steps
  const steps = [
    'Creation of Purchase Request',
    'Multi Level Approval',
    'Request for Quotations',
    'Vendor Quote Finalization',
    'Purchase Order Creation',
    'Multi Level Approval',
    'GRN & Invoicing Upload',
    'Payment Request',
    'Purchase Order Closure'
  ];

  return (
      <Box sx={{
        width: '100%',
        overflowX: 'auto',
        pb: 1
      }}>
        <Stepper
          alternativeLabel
          connector={<ColorlibConnector />}
          sx={{
            minWidth: 'max-content',
            '& .MuiStepLabel-label': {
              fontSize: '0.75rem',
              fontWeight: 'medium',
              color: 'text.primary',
              marginTop: 1,
              lineHeight: 1.2
            }
          }}
        >
          {steps.map((label, index) => (
            <Step key={index} active={true}>
              <StepLabel StepIconComponent={ColorlibStepIcon}>
                {label}
              </StepLabel>
            </Step>
          ))}
        </Stepper>
      </Box>
  );
};

export default ProcurementTimeline;
