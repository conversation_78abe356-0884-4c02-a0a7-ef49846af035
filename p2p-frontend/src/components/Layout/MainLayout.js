import React, { useState, useMemo } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  Typography,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  useMediaQuery,
  useTheme
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  Description as DocumentIcon,
  ShoppingCart as ShoppingCartIcon,
  Settings as SettingsIcon,
  Person as PersonIcon,
  Logout as LogoutIcon,
  ReceiptLong as ReceiptLongIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';

// Drawer width
const drawerWidth = 220;

const MainLayout = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Separate states for mobile and desktop drawer
  const [mobileOpen, setMobileOpen] = useState(false);
  const [desktopOpen, setDesktopOpen] = useState(true); // Default to open on desktop
  const [anchorEl, setAnchorEl] = useState(null);
  const [menuIconRotation, setMenuIconRotation] = useState(0);

  // Store previous drawer state to handle transitions properly
  const [isDrawerClosing, setIsDrawerClosing] = useState(false);

  // Check if user has admin role
  const isAdmin = useMemo(() => {
    if (!user || !user.roles) return false;
    return user.roles.some(role => role.role === 'admin');
  }, [user]);

  // Get role display text
  const getRoleDisplay = useMemo(() => {
    // TODO: remove this check after confirming the BE Code
    // if (!user || !user.roles || !Array.isArray(user.roles)) return 'User';
    
    // If user has multiple roles, display "Multi-role"
    if (user.roles.length > 1) return 'Multi-role';
    
    // If user has one role, display that role
    if (user.roles.length === 1) {
      const roleMap = {
        'admin': 'Admin',
        'procurement_manager': 'Procurement Manager',
        'cc_head': 'Cost Center Head',
        'biz_fin': 'Business Finance'
      };
      return roleMap[user.roles[0].role] || user.roles[0].role || 'User';
    }
    
    return 'User';
  }, [user]);

  const handleDrawerToggle = () => {
    // Rotate menu icon
    setMenuIconRotation(prev => prev === 0 ? 180 : 0);

    if (isMobile) {
      setMobileOpen(!mobileOpen);
    } else {
      // For desktop, add a small animation effect
      if (desktopOpen) {
        setIsDrawerClosing(true);
        setTimeout(() => {
          setDesktopOpen(false);
          setIsDrawerClosing(false);
        }, 150);
      } else {
        setDesktopOpen(true);
      }
    }
  };

  const handleProfileMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleProfileMenuClose();
    logout();
  };

  const handleProfileClick = () => {
    handleProfileMenuClose();
    navigate('/profile');
  };

  // Navigation is now defined directly in the drawer component

  // Settings items - conditionally include Settings based on admin role
  const settingsItems = useMemo(() => {
    const items = [];

    // Only show Settings for admin users
    if (isAdmin) {
      items.push({ text: 'Settings', icon: <SettingsIcon />, path: '/settings' });
    }

    return items;
  }, [isAdmin]);

  const drawer = (
    <div>
      <Toolbar sx={{
        justifyContent: 'center',
        padding: '0 8px',
        minHeight: '64px',
        backgroundColor: '#FFFFFF' // White background for logo area
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Typography variant="h6" noWrap component="div" sx={{ fontWeight: 'bold', color: '#4CAF50' }}>
            Vegrow - Procure to Pay
          </Typography>
        </Box>
      </Toolbar>
      <Divider />

      {/* Main Navigation */}
      <Box sx={{ py: 1 }}>
        <Typography
          variant="overline"
          sx={{
            px: 3,
            py: 0.5,
            display: 'block',
            color: location.pathname === '/dashboard' ? '#4CAF50' : 'text.secondary',
            fontWeight: 'medium',
            backgroundColor: location.pathname === '/dashboard' ? 'rgba(76, 175, 80, 0.08)' : 'transparent'
          }}
        >
          Main
        </Typography>
        <List>
          {/* Dashboard */}
          <ListItem
            button
            component={Link}
            to="/dashboard"
            selected={location.pathname === '/dashboard'}
            onClick={isMobile ? () => setMobileOpen(false) : undefined}
            sx={{
              '&.Mui-selected': {
                backgroundColor: 'rgba(76, 175, 80, 0.08)',
                borderLeft: '4px solid #4CAF50',
                paddingLeft: '12px',
                '&:hover': {
                  backgroundColor: 'rgba(76, 175, 80, 0.12)',
                },
                '& .MuiListItemIcon-root': {
                  color: '#4CAF50'
                },
                '& .MuiListItemText-primary': {
                  fontWeight: 'bold',
                  color: '#4CAF50'
                }
              },
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                transition: 'background-color 0.2s ease'
              },
            }}
          >
            <ListItemIcon>
              <DashboardIcon />
            </ListItemIcon>
            <ListItemText primary="Dashboard" />
          </ListItem>
        </List>
      </Box>

      {/* Procurement Section */}
      <Box sx={{ py: 1 }}>
        <Typography
          variant="overline"
          sx={{
            px: 3,
            py: 0.5,
            display: 'block',
            color: ['/purchase-requests', '/purchase-orders', '/my-approvals'].includes(location.pathname) ? '#4CAF50' : 'text.secondary',
            fontWeight: 'medium',
            backgroundColor: ['/purchase-requests', '/purchase-orders', '/my-approvals'].includes(location.pathname) ? 'rgba(76, 175, 80, 0.08)' : 'transparent'
          }}
        >
          Procurement
        </Typography>
        <List>
          {/* Purchase Requests */}
          <ListItem
            button
            component={Link}
            to="/purchase-requests"
            selected={location.pathname === '/purchase-requests'}
            onClick={isMobile ? () => setMobileOpen(false) : undefined}
            sx={{
              '&.Mui-selected': {
                backgroundColor: 'rgba(76, 175, 80, 0.08)',
                borderLeft: '4px solid #4CAF50',
                paddingLeft: '12px',
                '&:hover': {
                  backgroundColor: 'rgba(76, 175, 80, 0.12)',
                },
                '& .MuiListItemIcon-root': {
                  color: '#4CAF50'
                },
                '& .MuiListItemText-primary': {
                  fontWeight: 'bold',
                  color: '#4CAF50'
                }
              },
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                transition: 'background-color 0.2s ease'
              },
            }}
          >
            <ListItemIcon>
              <DocumentIcon />
            </ListItemIcon>
            <ListItemText primary="Purchase Requests" />
          </ListItem>

          {/* Purchase Orders */}
          <ListItem
            button
            component={Link}
            to="/purchase-orders"
            selected={location.pathname === '/purchase-orders'}
            onClick={isMobile ? () => setMobileOpen(false) : undefined}
            sx={{
              '&.Mui-selected': {
                backgroundColor: 'rgba(76, 175, 80, 0.08)',
                borderLeft: '4px solid #4CAF50',
                paddingLeft: '12px',
                '&:hover': {
                  backgroundColor: 'rgba(76, 175, 80, 0.12)',
                },
                '& .MuiListItemIcon-root': {
                  color: '#4CAF50'
                },
                '& .MuiListItemText-primary': {
                  fontWeight: 'bold',
                  color: '#4CAF50'
                }
              },
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                transition: 'background-color 0.2s ease'
              },
            }}
          >
            <ListItemIcon>
              <ShoppingCartIcon />
            </ListItemIcon>
            <ListItemText primary="Purchase Orders" />
          </ListItem>

          {/* My Approvals tab removed */}
        {/* Invoices */}
        <ListItem
          button
          component={Link}
          to="/invoices"
          selected={location.pathname === '/invoices'}
          onClick={isMobile ? () => setMobileOpen(false) : undefined}
          sx={{
            '&.Mui-selected': {
              backgroundColor: 'rgba(76, 175, 80, 0.08)',
              borderLeft: '4px solid #4CAF50',
              paddingLeft: '12px',
              '&:hover': {
                backgroundColor: 'rgba(76, 175, 80, 0.12)',
              },
              '& .MuiListItemIcon-root': {
                color: '#4CAF50'
              },
              '& .MuiListItemText-primary': {
                fontWeight: 'bold',
                color: '#4CAF50'
              }
            },
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
              transition: 'background-color 0.2s ease'
            },
          }}
        >
          <ListItemIcon>
            <ReceiptLongIcon />
          </ListItemIcon>
          <ListItemText primary="Invoices" />
        </ListItem>
        </List>
      </Box>

      {/* Settings Section */}
      {settingsItems.length > 0 && (
        <>
          <Divider />
          <Box sx={{ py: 1 }}>
            <Typography
              variant="overline"
              sx={{
                px: 3,
                py: 0.5,
                display: 'block',
                color: location.pathname === '/settings' ? '#4CAF50' : 'text.secondary',
                fontWeight: 'medium',
                backgroundColor: location.pathname === '/settings' ? 'rgba(76, 175, 80, 0.08)' : 'transparent'
              }}
            >
              Administration
            </Typography>
            <List>
              {settingsItems.map((item) => (
                <ListItem
                  button
                  key={item.text}
                  component={item.path ? Link : 'div'}
                  to={item.path}
                  onClick={item.onClick || (isMobile ? () => setMobileOpen(false) : undefined)}
                  selected={item.path && location.pathname === item.path}
                  sx={{
                    '&.Mui-selected': {
                      backgroundColor: 'rgba(76, 175, 80, 0.08)',
                      borderLeft: '4px solid #4CAF50',
                      paddingLeft: '12px',
                      '&:hover': {
                        backgroundColor: 'rgba(76, 175, 80, 0.12)',
                      },
                      '& .MuiListItemIcon-root': {
                        color: '#4CAF50'
                      },
                      '& .MuiListItemText-primary': {
                        fontWeight: 'bold',
                        color: '#4CAF50'
                      }
                    },
                    '&:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)',
                      transition: 'background-color 0.2s ease'
                    },
                  }}
                >
                  <ListItemIcon>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText primary={item.text} />
                </ListItem>
              ))}
            </List>
          </Box>
        </>
      )}
    </div>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          width: '100%',
          transition: theme.transitions.create(['margin', 'width'], {
            easing: theme.transitions.easing.easeOut,
            duration: theme.transitions.duration.standard,
          }),
          borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
          backgroundColor: '#FFFFFF', // White top bar
          color: theme.palette.text.primary,
          zIndex: (theme) => theme.zIndex.drawer + 1
        }}
      >
        <Toolbar sx={{ minHeight: { xs: 64, sm: 64 } }}>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{
              mr: 2,
              color: '#4CAF50',
              '&:hover': {
                backgroundColor: 'rgba(76, 175, 80, 0.08)'
              }
            }}
          >
            <MenuIcon sx={{
              transform: `rotate(${menuIconRotation}deg)`,
              transition: 'transform 0.3s ease-in-out'
            }} />
          </IconButton>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                color: '#4CAF50',
                fontWeight: 600,
                display: { xs: 'none', sm: 'block' }
              }}
            >
              Vegrow - Procure to Pay
            </Typography>
          </Box>

          <Box sx={{ flexGrow: 1 }} />

          {/* User profile menu */}
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{
              flexDirection: 'column',
              alignItems: 'flex-end',
              mr: 1.5,
              display: { xs: 'none', sm: 'flex' }
            }}>
              {user && (
                <>
                  <Typography variant="body2" sx={{ fontWeight: 'medium', lineHeight: 1.2 }}>
                    {user.username}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ lineHeight: 1.2 }}>
                    {getRoleDisplay}
                  </Typography>
                </>
              )}
            </Box>
            <IconButton
              size="small"
              edge="end"
              aria-label="account of current user"
              aria-haspopup="true"
              onClick={handleProfileMenuOpen}
              color="inherit"
              sx={{
                p: 0.5,
                border: '2px solid #E8F5E9',
                '&:hover': {
                  backgroundColor: '#E8F5E9'
                }
              }}
            >
              <Avatar sx={{ width: 32, height: 32, bgcolor: '#4CAF50' }}>
                {(user?.name || 'U')[0]}
              </Avatar>
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Desktop drawer */}
      <Drawer
        variant="persistent"
        sx={{
          display: { xs: 'none', sm: 'block' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            boxShadow: '0 1px 4px rgba(0, 0, 0, 0.05)',
            borderRight: '1px solid rgba(0, 0, 0, 0.05)',
            backgroundColor: '#FAFAFA', // Almost white sidebar background
            transition: theme.transitions.create('transform', {
              easing: theme.transitions.easing.easeOut,
              duration: theme.transitions.duration.standard,
            }),
            transform: desktopOpen ? 'translateX(0)' : `translateX(-${drawerWidth}px)`,
            opacity: isDrawerClosing ? 0.5 : 1,
            height: '100%',
            zIndex: theme.zIndex.appBar - 1
          },
        }}
        open={desktopOpen}
      >
        {drawer}
      </Drawer>

      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          padding: 3,
          width: '100%',
          marginTop: '64px',
          transition: theme.transitions.create(['margin', 'width'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          ml: { sm: desktopOpen ? `${drawerWidth}px` : 0 },
          minHeight: 'calc(100vh - 64px)',
          overflow: 'auto'
        }}
      >
        {children}
      </Box>

      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.1)',
            backgroundColor: '#FAFAFA', // Almost white sidebar background
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* Profile menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleProfileMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleProfileClick}>
          <ListItemIcon>
            <PersonIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Profile" />
        </MenuItem>
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <LogoutIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText primary="Logout" />
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default MainLayout;
