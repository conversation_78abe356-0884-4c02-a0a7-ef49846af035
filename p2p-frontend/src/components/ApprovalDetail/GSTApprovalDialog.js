import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Box,
  Typography,
  Grid,
  TextField,
  useTheme,
  useMediaQuery,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  InputAdornment
} from '@mui/material';
import apiService from '../../services/apiService';
import axiosInstance from '../../services/axiosInstance';

// Import the new component files
import ItemsTable from './GSTApprovalDialog/ItemsTable';
import { UNITS_OF_MEASUREMENT } from '../../pages/CreatePR/constants';
import TermsConditions from './GSTApprovalDialog/TermsConditions';
import DialogActionButtons from './GSTApprovalDialog/DialogActionButtons';
import VendorDetails from './GSTApprovalDialog/VendorDetails';
import QuotationComparison from './GSTApprovalDialog/QuotationComparison';
import apiConfig from '../../config/api.config';

// Define GST rates with the improved arrow format for better readability
const GST_RATES = [
  { label: "0% → No GST", value: 0 },
  { label: "5% → IGST 5%", value: 5 },
  { label: "5% → CGST 2.5% + SGST 2.5%", value: 5, type: 'SPLIT' },
  { label: "12% → IGST 12%", value: 12 },
  { label: "12% → CGST 6% + SGST 6%", value: 12, type: 'SPLIT' },
  { label: "18% → IGST 18%", value: 18 },
  { label: "18% → CGST 9% + SGST 9%", value: 18, type: 'SPLIT' },
  { label: "28% → IGST 28%", value: 28 },
  { label: "28% → CGST 14% + SGST 14%", value: 28, type: 'SPLIT' }
];

const GSTApprovalDialog = ({ open, onClose, prDetails, setPRDetails, onApprove, onReject, isActionLoading }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('md'));
  
  // Initialize items with GST information
  const [items, setItems] = useState([]);
  const [paymentTerms, setPaymentTerms] = useState('');
  const [deliveryTerms, setDeliveryTerms] = useState('');
  const [billTo, setBillTo] = useState('');
  const [billingAddresses, setBillingAddresses] = useState([]);
  const [isLoadingAddresses, setIsLoadingAddresses] = useState(false);
  const [selectedBillingAddressId, setSelectedBillingAddressId] = useState('');
  const [shipToAddress, setShipToAddress] = useState('');
  const [expectedDeliveryDate, setExpectedDeliveryDate] = useState(null);
  const [remarks, setRemarks] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [poDetails, setPODetails] = useState(null);
  const [itemCategories, setItemCategories] = useState([]);

  // Extract vendor information from the proposed quote or the existing PO for editing
  const isEditingRejectedPO = prDetails?.isEditingRejectedPO || false;
  const vendorDetails = poDetails?.vendorDetails || 
                       (isEditingRejectedPO ? prDetails?.poDetails?.vendorDetails || {} : prDetails?.proposedQuote || {});

  // Function to fetch purchase order details
  const fetchPurchaseOrderDetails = async (poId) => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await axiosInstance.get(apiConfig.endpoints.purchaseOrders.detail(poId));
      
      if (response && response.data) {
        const poData = response.data;
        setPODetails(poData);
        console.log('Successfully fetched PO details:', poData);
        return poData;
      } else {
        throw new Error('Purchase order details not found');
      }
    } catch (error) {
      console.error('Error fetching PO details:', error);
      setError(error.response?.data?.message || 'Failed to load purchase order details');
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch billing addresses from API
  const fetchBillingAddresses = async () => {
    try {
      setIsLoadingAddresses(true);
      const response = await axiosInstance.get('/admin/billing-addresses');
      if (response && response.data && response.data.data) {
        setBillingAddresses(response.data.data);
        console.log('Fetched billing addresses:', response.data.data);
      }
    } catch (error) {
      console.error('Error fetching billing addresses:', error);
    } finally {
      setIsLoadingAddresses(false);
    }
  };

  // Call fetchBillingAddresses and fetchItemCategories on component mount
  useEffect(() => {
    if (open) {
      fetchBillingAddresses();
      // Fetch item categories
      (async () => {
        try {
          const itemCategoriesResponse = await apiService.itemCategoryService.getActiveItemCategories();
          setItemCategories(itemCategoriesResponse?.data || itemCategoriesResponse || []);
        } catch (error) {
          setItemCategories([]);
          console.error('Failed to fetch item categories', error);
        }
      })();
    }
  }, [open]);

  useEffect(() => {
    const initializeData = async () => {
      if (!prDetails) return;
      
      let sourcePODetails = null;
      
      const isRejectedPO = prDetails.poStatus === "Rejected";
      const shouldFetchPODetails = (isRejectedPO || isEditingRejectedPO) && prDetails.poId;
      
      if (shouldFetchPODetails) {
        console.log('Fetching PO details for rejected or editing PO:', prDetails.poId);
        sourcePODetails = await fetchPurchaseOrderDetails(prDetails.poId);
        
        if (sourcePODetails) {
          setBillTo(sourcePODetails.billTo || '');
          setShipToAddress(sourcePODetails.shipToAddress || '');
          if (sourcePODetails.expectedDeliveryDate) {
            console.log('Expected delivery date from API:', sourcePODetails.expectedDeliveryDate);
            try {
              setExpectedDeliveryDate(new Date(sourcePODetails.expectedDeliveryDate));
            } catch (e) {
              console.error('Error parsing date:', e);
              setExpectedDeliveryDate(null);
            }
          } else {
            setExpectedDeliveryDate(null);
          }

          if (billingAddresses.length > 0 && sourcePODetails.billTo) {
            const matchingAddress = billingAddresses.find(addr => 
              addr.address === sourcePODetails.billTo
            );
            if (matchingAddress) {
              setSelectedBillingAddressId(matchingAddress.id);
            }
          }
        }
      }
      
      const sourceItems = sourcePODetails?.purchaseOrder?.items || sourcePODetails?.items || 
                         (isEditingRejectedPO ? prDetails.poDetails?.items : prDetails.items);
      
      if (sourceItems) {
        const itemsWithGst = sourceItems.map(item => {
          if (isEditingRejectedPO || prDetails.poStatus === "Rejected") {
            const categoryId = item.category?.id || item.itemCategoryId || item.categoryId;
            const categoryName = item.category?.name || 'N/A';
            
            return {
              ...item,
              itemName: item.itemName || item.name || '',
              itemCategoryId: categoryId,
              itemCategory: item.itemCategory || item.category || { id: categoryId, name: categoryName },
              estimatedPricePerQuantity: item.pricePerUnit || item.pricePerQuantity || item.estimatedPricePerQuantity || 0,
              gstRate: item.gstRate || item.gstPercentage || 18,
              gstAmount: item.gstAmount || calculateGstAmount(item.pricePerUnit || item.pricePerQuantity || item.estimatedPricePerQuantity || 0, item.quantity, item.gstRate || item.gstPercentage || 18),
              totalWithGst: item.totalValue || item.totalWithGst || calculateTotalWithGst(item.pricePerUnit || item.pricePerQuantity || item.estimatedPricePerQuantity || 0, item.quantity, item.gstRate || item.gstPercentage || 18),
              isNewItem: false
            };
          } else {
            return {
              ...item,
              itemName: item.itemName || item.name || '',
              itemCategory: item.itemCategory || item.category || { id: item.itemCategoryId || item.categoryId, name: 'N/A' },
              estimatedPricePerQuantity: item.estimatedPricePerQuantity || 0,
              gstRate: 0,
              gstAmount: 0,
              totalWithGst: item.quantity * (item.estimatedPricePerQuantity || 0),
              isNewItem: false
            };
          }
        });
        
        setItems(itemsWithGst);
      }
      
      if (sourcePODetails) {
        const poData = sourcePODetails.purchaseOrder || sourcePODetails;
        setPaymentTerms(poData.paymentTerms || '');
        setDeliveryTerms(poData.deliveryTerms || '');
        
        if (prDetails.poStatus === "Rejected" && !isEditingRejectedPO) {
          setPRDetails(prev => ({
            ...prev,
            isEditingRejectedPO: true,
            poDetails: poData
          }));
        }
      } else if (isEditingRejectedPO && prDetails.poDetails) {
        setPaymentTerms(prDetails.poDetails.paymentTerms || '');
        setDeliveryTerms(prDetails.poDetails.deliveryTerms || '');
      } else if (prDetails.proposedQuote) {
        setPaymentTerms(prDetails.proposedQuote.paymentTerms || '');
        setDeliveryTerms(prDetails.proposedQuote.deliveryTerms || '');
      }

      
    };
    
    initializeData();
  }, [prDetails, isEditingRejectedPO, setPRDetails, billingAddresses]);

  // Handler to update item category for an item
  const handleCategoryChange = (index, categoryId) => {
    setItems(prevItems => {
      const updated = [...prevItems];
      const selectedCategory = itemCategories.find(cat => cat.id === categoryId);
      updated[index] = {
        ...updated[index],
        itemCategoryId: categoryId,
        itemCategory: selectedCategory || { id: categoryId, name: '' },
      };
      return updated;
    });
  };

  // Handler to update unit of measurement for an item
  const handleUomChange = (index, uom) => {
    setItems(prevItems => {
      const updated = [...prevItems];
      updated[index] = {
        ...updated[index],
        uom: uom,
      };
      return updated;
    });
  };

  const calculateGstAmount = (price, quantity, gstRate) => {
    const baseAmount = parseFloat(price) * parseFloat(quantity);
    return (baseAmount * (parseFloat(gstRate) / 100)).toFixed(2);
  };

  const calculateTotalWithGst = (price, quantity, gstRate) => {
    const baseAmount = parseFloat(price) * parseFloat(quantity);
    const gstAmount = baseAmount * (parseFloat(gstRate) / 100);
    return (baseAmount + gstAmount).toFixed(2);
  };

  const handleBillingAddressChange = (event) => {
    const addressId = event.target.value;
    setSelectedBillingAddressId(addressId);
    
    const selectedAddress = billingAddresses.find(addr => addr.id === addressId);
    if (selectedAddress) {
      setBillTo(selectedAddress.address);
    } else {
      setBillTo('');
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setError(null);
      
      const invalidItems = items.filter(item => {
        if (!item.itemName?.trim()) {
          return true;
        }
        
        if (!item.quantity || parseFloat(item.quantity) <= 0) {
          return true;
        }
        
        const price = parseFloat(item.pricePerUnit || item.estimatedPricePerQuantity || 0);
        if (isNaN(price) || price <= 0) {
          return true;
        }
        
        return false;
      });
      
      if (invalidItems.length > 0) {
        const updatedItems = [...items];
        invalidItems.forEach(item => {
          const index = items.findIndex(i => i.id === item.id);
          if (index !== -1) {
            updatedItems[index] = {
              ...updatedItems[index],
              hasError: true
            };
          }
        });
        setItems(updatedItems);
        
        throw new Error('Please complete all required fields for each item (name, quantity, price).');
      }
      
      if (!paymentTerms?.trim()) {
        throw new Error('Payment terms are required');
      }
      
      if (!deliveryTerms?.trim()) {
        throw new Error('Delivery terms are required');
      }
      
      const totalAmount = parseFloat(calculateTotal());
      
      let vendorId = vendorDetails.vendorId;
      
      if (isEditingRejectedPO && !vendorId && prDetails.poDetails) {
        vendorId = prDetails.poDetails.vendorId;
      }
      
      if (!vendorId) {
        throw new Error('Vendor ID is required');
      }
      
      let vendorDetailsForPayload = { ...vendorDetails };
      
      if (isEditingRejectedPO && prDetails.poDetails && prDetails.poDetails.vendorDetails) {
        vendorDetailsForPayload = {
          ...vendorDetailsForPayload,
          ...prDetails.poDetails.vendorDetails,
          vendorId: vendorId
        };
      } else if (prDetails.proposedQuote) {
        vendorDetailsForPayload = {
          ...vendorDetailsForPayload,
          email: prDetails.proposedQuote.email || vendorDetailsForPayload.email || '',
          phone: prDetails.proposedQuote.phone || vendorDetailsForPayload.phone || '',
          gstVerified: prDetails.proposedQuote.gstVerified || vendorDetailsForPayload.gstVerified || false,
          panVerified: prDetails.proposedQuote.panVerified || vendorDetailsForPayload.panVerified || false,
          bankVerified: prDetails.proposedQuote.bankVerified || vendorDetailsForPayload.bankVerified || false,
          msmeVerified: prDetails.proposedQuote.msmeVerified || vendorDetailsForPayload.msmeVerified || false,
        };
      }
      
      let quotationId;
      
      if (isEditingRejectedPO || prDetails.poStatus === "Rejected") {
        quotationId = prDetails.selectedQuoteId || 
                     (poDetails?.quotation?.id) || 
                     (prDetails.poDetails?.quotation?.id);
        
        console.log('Using quotation ID for rejected PO:', quotationId);
      } else {
        quotationId = vendorDetails.id || prDetails.selectedQuoteId;
      }
      
      if (!quotationId && prDetails.proposedQuote) {
        quotationId = prDetails.proposedQuote.id;
      }
      
      if (!quotationId) {
        console.warn('No quotation ID found, using fallback value');
        quotationId = 1;
      }
      
      const validationErrors = [];
      
      if (!prDetails.id) validationErrors.push('PR ID is missing');
      
      if (!quotationId) validationErrors.push('Quotation ID is missing');
      
      if (!vendorId) validationErrors.push('Vendor ID is missing');
      
      if (!items || items.length === 0) {
        validationErrors.push('No items to process');
      } else {
        const itemErrors = items.filter(item => 
          !item.itemName || 
          parseFloat(item.quantity || 0) <= 0 ||
          parseFloat(item.estimatedPricePerQuantity || item.pricePerQuantity || 0) <= 0
        );
        
        if (itemErrors.length > 0) {
          validationErrors.push(`${itemErrors.length} items have missing or invalid data`);
        }
      }
      
      if (validationErrors.length > 0) {
        throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
      }
      
      const processedItems = items.map(item => {
        const price = parseFloat(item.estimatedPricePerQuantity || item.pricePerQuantity || 0).toFixed(2);
        const quantity = parseInt(item.quantity || 0);
        const gstPercentage = parseFloat(item.gstRate || item.gstPercentage || 0);
        
        const categoryId = item.itemCategory?.id || item.itemCategoryId || item.categoryId || 0;
        
        return {
          itemCategoryId: categoryId,
          itemName: item.itemName || "",
          itemDescription: item.itemDescription || "",
          quantity: quantity,
          uom: item.uom || "Unit",
          pricePerUnit: parseFloat(price),
          gstPercentage: gstPercentage,
          gstAmount: parseFloat(item.gstAmount || 0),
          totalValue: parseFloat(item.totalWithGst || 0)
        };
      });
      
      const businessUnitId = prDetails?.businessUnitId || 
                            (prDetails?.businessUnit?.id ? Number(prDetails.businessUnit.id) : null);
      const costCenterId = prDetails?.costCenterId || 
                          (prDetails?.costCenter?.id ? Number(prDetails.costCenter.id) : null);
      
      const poType = prDetails?.type || "OneTime";
      
      const payload = {
        prId: prDetails?.id,
        quotationId: quotationId,
        type: poType,
        vendorId: vendorId,
        businessUnitId: businessUnitId,
        costCenterId: costCenterId,
        paymentTerms: paymentTerms,
        deliveryTerms: deliveryTerms,
        totalAmount: parseFloat(calculateTotal()),
        currency: prDetails?.currency || "INR",
        recurrenceFrequency: poType === "Recurring" ? prDetails?.recurrenceFrequency : undefined,
        startDate: poType === "Recurring" ? prDetails?.startDate : undefined,
        endDate: poType === "Recurring" ? prDetails?.endDate : undefined,
        billTo: billTo,
        shipToAddress: shipToAddress,
        expectedDeliveryDate: expectedDeliveryDate ? expectedDeliveryDate.toISOString().split('T')[0] : null,
        items: processedItems
      };
      
      let response;
      
      if (isEditingRejectedPO && prDetails.poId) {
        const updatePayload = {
          ...payload,
          id: prDetails.poId
        };
        
        console.log('Updating PO with payload:', JSON.stringify(updatePayload));
        try {
          const config = {
            headers: {
              'Content-Type': 'application/json'
            }
          };
          
          response = await axiosInstance.put(
            `/purchase-orders/${prDetails.poId}/update-rejected`, 
            updatePayload,
            config
          );
          
          console.log('Update PO success response:', response.data);
          
        } catch (updateError) {
          console.error('Error updating PO:', updateError);
          console.error('Error details:', updateError.response?.data);
          
          const errorMsg = updateError.response?.data?.message || 
                          updateError.response?.statusText || 
                          updateError.message || 
                          'Failed to update purchase order';
                          
          throw new Error(errorMsg);
        }
      } else {
        console.log('Creating PO with payload:', JSON.stringify(payload));
        try {
          response = await apiService.createPurchaseOrder(payload);
          console.log('Create PO success response:', response);
        } catch (createError) {
          console.error('Error creating PO:', createError);
          console.error('Error details:', createError.response?.data);
          
          const errorMsg = createError.response?.data?.message || 
                          createError.response?.statusText || 
                          createError.message || 
                          'Failed to create purchase order';
                          
          throw new Error(errorMsg);
        }
      }
      
      setPRDetails(prev => ({
        ...prev,
        purchaseOrder: response?.purchaseOrder || response,
        openCreatePOdialog: false,
        isEditingRejectedPO: false
      }));
      
      onClose();
      
      navigate('/purchase-orders', { 
        state: { 
          notification: { 
            type: 'success', 
            message: isEditingRejectedPO ? 'Purchase Order updated successfully!' : 'Purchase Order created successfully!' 
          }
        }
      });
      
    } catch (error) {
      console.error('Error with purchase order:', error);
      
      let errorMessage = 'Failed to process purchase order';
      
      if (error.response) {
        console.error('Response error data:', error.response.data);
        console.error('Response error status:', error.response.status);
        
        errorMessage = error.response.data?.message || 
                       `Server error: ${error.response.status} - ${error.response.statusText}`;
      } else if (error.request) {
        console.error('Error request:', error.request);
        errorMessage = 'No response received from server. Please check your network connection.';
      } else {
        console.error('Error message:', error.message);
        errorMessage = error.message || errorMessage;
      }
      
      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateTotal = () => {
    return items.reduce(
      (sum, item) => sum + parseFloat(item.totalWithGst || 0), 
      0
    ).toFixed(2);
  };

  const calculateTotalGst = () => {
    return items.reduce(
      (sum, item) => sum + parseFloat(item.gstAmount || 0), 
      0
    ).toFixed(2);
  };

  const handleAddItem = () => {
    const newItem = {
      id: `new-item-${Date.now()}`,
      itemName: '',
      itemDescription: '',
      quantity: 1,
      uom: 'Unit',
      estimatedPricePerQuantity: 1,
      pricePerUnit: 1,
      gstPercentage: 18,
      gstRate: 18,
      gstAmount: 0.18,
      totalWithGst: 1.18,
      isNewItem: true,
      itemCategoryId: items.length > 0 ? (items[0].itemCategoryId || items[0].category?.id) : '',
      itemCategory: items.length > 0 ? (items[0].itemCategory || items[0].category) : { id: '', name: 'N/A' }
    };
    
    setItems([...items, newItem]);
  };

  const handleRemoveItem = (index) => {
    if (items.length <= 1) return;
    
    const updatedItems = [...items];
    updatedItems.splice(index, 1);
    setItems(updatedItems);
  };
  
  const handleItemNameChange = (index, newName) => {
    const updatedItems = [...items];
    updatedItems[index] = {
      ...updatedItems[index],
      itemName: newName
    };
    setItems(updatedItems);
  };

  // Item editing handlers
  const handleGstRateChange = (index, newValue, inputGstType) => {
    const updatedItems = [...items];
    const item = updatedItems[index];
    
    // newValue could be the rate object or just the rate value
    // If it's a rate object from the dropdown, extract the needed values
    let gstRate, gstType;
    
    if (typeof newValue === 'object' && newValue !== null) {
      // If we're receiving the full GST rate object
      gstRate = parseFloat(newValue.value);
      gstType = newValue.type || 'IGST'; // Default to IGST if type not specified
    } else {
      // If we're just receiving the rate value
      gstRate = parseFloat(newValue);
      // Preserve the existing GST type if available, otherwise default to IGST
      gstType = inputGstType || item.gstType || 'IGST';
    }
    
    console.log('GST change:', { index, gstRate, gstType });
    
    const gstAmount = calculateGstAmount(item.estimatedPricePerQuantity, item.quantity, gstRate);
    const totalWithGst = calculateTotalWithGst(item.estimatedPricePerQuantity, item.quantity, gstRate);
    
    updatedItems[index] = {
      ...item,
      gstRate,     // The numerical value for calculations
      gstType,     // Track the GST type (IGST or SPLIT for CGST+SGST)
      gstAmount,
      totalWithGst
    };
    
    setItems(updatedItems);
  };

  const handleDescriptionChange = (index, newDescription) => {
    const updatedItems = [...items];
    updatedItems[index] = {
      ...updatedItems[index],
      itemDescription: newDescription
    };
    setItems(updatedItems);
  };

  const handlePriceChange = (index, newPrice) => {
    const updatedItems = [...items];
    const item = updatedItems[index];
    
    const price = parseFloat(newPrice);
    const gstAmount = calculateGstAmount(price, item.quantity, item.gstRate);
    const totalWithGst = calculateTotalWithGst(price, item.quantity, item.gstRate);
    
    updatedItems[index] = {
      ...item,
      estimatedPricePerQuantity: newPrice,
      pricePerUnit: newPrice,
      gstAmount,
      totalWithGst
    };
    
    setItems(updatedItems);
  };
  
  const handleQuantityChange = (index, newQuantity) => {
    const updatedItems = [...items];
    const item = updatedItems[index];
    
    const quantity = parseFloat(newQuantity);
    const gstAmount = calculateGstAmount(item.estimatedPricePerQuantity, quantity, item.gstRate);
    const totalWithGst = calculateTotalWithGst(item.estimatedPricePerQuantity, quantity, item.gstRate);
    
    updatedItems[index] = {
      ...item,
      quantity: newQuantity,
      gstAmount,
      totalWithGst
    };
    
    setItems(updatedItems);
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="xl"
      fullWidth
      fullScreen={fullScreen}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            {isEditingRejectedPO ? 'Edit Purchase Order' : 'Create Purchase Order'}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="body2" color="textSecondary">
              PR #{prDetails?.id}
            </Typography>
          </Box>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <QuotationComparison prDetails={prDetails} />
          </Grid>
          
          <Grid item xs={12}>
            <VendorDetails 
              vendorId={vendorDetails.vendorId} 
              vendorName={vendorDetails.vendorName}
              quotationId={isEditingRejectedPO || prDetails.poStatus === "Rejected" ? prDetails.selectedQuoteId : null}
            />
          </Grid>

          <Grid item xs={12}>
            <ItemsTable 
              items={items}
              GST_RATES={GST_RATES}
              handleGstRateChange={handleGstRateChange}
              handleDescriptionChange={handleDescriptionChange}
              handlePriceChange={handlePriceChange}
              handleCategoryChange={handleCategoryChange}
              handleQuantityChange={handleQuantityChange}
              handleUomChange={handleUomChange}
              calculateTotal={calculateTotal}
              calculateTotalGst={calculateTotalGst}
              handleAddItem={handleAddItem}
              handleRemoveItem={handleRemoveItem}
              handleItemNameChange={handleItemNameChange}
              itemCategories={itemCategories}
              unitsOfMeasurement={UNITS_OF_MEASUREMENT}
              itemNameEditable
            />
          </Grid>

          <Grid item xs={12}>
            <Paper sx={{ p: 2, mb: 2 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
                Terms & Conditions
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Payment Terms"
                    variant="outlined"
                    value={paymentTerms}
                    onChange={(e) => setPaymentTerms(e.target.value)}
                    required
                    placeholder="Enter payment terms"
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Delivery Terms"
                    variant="outlined"
                    value={deliveryTerms}
                    onChange={(e) => setDeliveryTerms(e.target.value)}
                    required
                    placeholder="Enter delivery terms"
                    size="small"
                  />
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          <Grid item xs={12}>
            <Paper sx={{ p: 2, mb: 2 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
                Billing & Shipping Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth size="small">
                    <InputLabel id="billing-address-label">Billing Address</InputLabel>
                    <Select
                      labelId="billing-address-label"
                      id="billing-address-select"
                      value={selectedBillingAddressId}
                      onChange={handleBillingAddressChange}
                      label="Billing Address"
                      displayEmpty
                      disabled={isLoadingAddresses}
                      sx={{
                        '.MuiSelect-select': {
                          whiteSpace: 'pre-line', // Keep line breaks but collapse whitespace
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          padding: '8px 14px',
                          minHeight: '40px',
                          display: 'flex',
                          alignItems: 'center'
                        }
                      }}
                      MenuProps={{
                        PaperProps: {
                          style: {
                            maxHeight: 300
                          }
                        }
                      }}
                      renderValue={(selected) => {
                        if (!selected || selected === '') {
                          return (
                            <Typography color="text.secondary" sx={{ fontStyle: 'italic' }}>
                              Select billing address
                            </Typography>
                          );
                        }
                        
                        const selectedAddr = billingAddresses.find(addr => addr.id === selected);
                        if (!selectedAddr) return '';
                        
                        // Display only the first part of the address (up to the first comma or 30 chars)
                        const shortAddress = selectedAddr.address.split(',')[0] || selectedAddr.address;
                        const displayAddress = shortAddress.length > 30 ? 
                          shortAddress.substring(0, 30) + '...' : 
                          shortAddress;
                          
                        return (
                          <Typography noWrap title={selectedAddr.address}>
                            {displayAddress}
                          </Typography>
                        );
                      }}
                      startAdornment={
                        isLoadingAddresses ? (
                          <InputAdornment position="start">
                            <CircularProgress size={20} />
                          </InputAdornment>
                        ) : null
                      }
                    >
                      {billingAddresses.length === 0 ? (
                        <MenuItem disabled>No billing addresses available</MenuItem>
                      ) : (
                        billingAddresses.map((address) => (
                          <MenuItem 
                            key={address.id} 
                            value={address.id}
                            sx={{
                              whiteSpace: 'normal', // Allow text to wrap in menu items
                              wordBreak: 'break-word'
                            }}
                          >
                            {address.address}
                          </MenuItem>
                        ))
                      )}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Shipping Address"
                    variant="outlined"
                    value={shipToAddress}
                    onChange={(e) => setShipToAddress(e.target.value)}
                    placeholder="Enter complete shipping address"
                    size="small"
                    multiline
                    rows={2}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Expected Delivery Date"
                    type="date"
                    variant="outlined"
                    value={expectedDeliveryDate ? 
                      (typeof expectedDeliveryDate === 'string' ? expectedDeliveryDate : expectedDeliveryDate.toISOString().split('T')[0]) : 
                      ''}
                    onChange={(e) => {
                      if (e.target.value) {
                        setExpectedDeliveryDate(new Date(e.target.value));
                      } else {
                        setExpectedDeliveryDate(null);
                      }
                    }}
                    InputLabelProps={{ shrink: true }}
                    size="small"
                  />
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
              Remarks
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={3}
              variant="outlined"
              placeholder="Enter any additional notes or remarks for this Purchase Order"
              value={remarks}
              onChange={(e) => setRemarks(e.target.value)}
              sx={{ mb: 2 }}
            />
          </Grid>

          {error && (
            <Grid item xs={12}>
              <Box sx={{ color: 'error.main', mt: 1, mb: 1 }}>
                {error}
              </Box>
            </Grid>
          )}
          {
            isLoading && (
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, mb: 2 }}>
                  <Typography>Loading purchase order details...</Typography>
                </Box>
              </Grid>
            )
          }
        </Grid>
      </DialogContent>

      <DialogActionButtons 
        onClose={onClose}
        handleSubmit={handleSubmit}
        isSubmitting={isSubmitting}
        isEditingRejectedPO={isEditingRejectedPO}
      />
    </Dialog>
  );
};

export default GSTApprovalDialog;
