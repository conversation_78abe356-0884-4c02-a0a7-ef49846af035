import React, { useState } from 'react';
import {
  Typography,
  Box,
  Divider,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  IconButton,
  Tooltip,
  Collapse,
  Button
} from '@mui/material';
import {
  Download as DownloadIcon,
  AttachFile as AttachFileIcon,
  ZoomIn as ZoomInIcon
} from '@mui/icons-material';
import AttachmentThumbnail from '../common/AttachmentThumbnail';
import { formatFileSize } from '../common/FilePreview';

// Helper function to format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const AttachmentCard = ({ file }) => {
  const [expanded, setExpanded] = useState(false);

  // Handle download of attachment
  const handleDownload = (e) => {
    e.stopPropagation();
    if (!file.fileUrl) {
      alert('File URL is not available for download');
      return;
    }
    
    // Create a temporary anchor element for direct download
    const link = document.createElement('a');
    link.href = file.fileUrl;
    link.download = file.fileName || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Card
      variant="outlined"
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'all 0.2s ease',
        borderRadius: 1,
        overflow: 'hidden',
        '&:hover': {
          boxShadow: 2,
          transform: 'translateY(-2px)'
        }
      }}
    >
      <CardActionArea
        onClick={() => setExpanded(!expanded)}
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'stretch',
          position: 'relative',
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: 8,
            right: 8,
            width: 24,
            height: 24,
            borderRadius: '50%',
            backgroundColor: 'primary.main',
            opacity: expanded ? 1 : 0.7,
            transition: 'all 0.2s',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
            backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z'/%3E%3C/svg%3E")`,
            backgroundSize: '16px',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            zIndex: 1
          }
        }}
      >
        {/* Enhanced Thumbnail Area */}
        <Box sx={{ 
          height: 140, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          p: 2,
          position: 'relative',
          bgcolor: 'background.default'
        }}>
          <AttachmentThumbnail 
            fileUrl={file.fileUrl}
            fileName={file.fileName}
            fileType={file.contentType}
            size={100}
            showPreviewOnHover={true}
            sx={{ 
              '& .MuiIconButton-root': {
                visibility: 'visible',
                opacity: 0.8,
                transition: 'all 0.2s',
                '&:hover': {
                  opacity: 1,
                  transform: 'scale(1.1)'
                }
              }
            }}
          />
          
          {/* Floating preview indicator */}
          <Box sx={{
            position: 'absolute',
            bottom: 8,
            left: 8,
            bgcolor: 'rgba(0, 0, 0, 0.6)',
            color: 'white',
            px: 1,
            py: 0.5,
            borderRadius: 1,
            fontSize: '0.7rem',
            display: 'flex',
            alignItems: 'center'
          }}>
            <ZoomInIcon sx={{ fontSize: '0.9rem', mr: 0.5 }} />
            Preview
          </Box>
        </Box>

        <CardContent sx={{ p: 1.5, flexGrow: 1 }}>
          <Typography variant="body2" fontWeight="medium" noWrap title={file.displayName || file.fileName}>
            {file.displayName || file.fileName}
          </Typography>
          <Typography variant="caption" color="text.secondary" display="block">
            {formatFileSize(file.fileSize)} • {formatDate(file.createdAt)}
          </Typography>
        </CardContent>
      </CardActionArea>

      <Collapse in={expanded} timeout="auto" unmountOnExit>
        <Box sx={{
          p: 2,
          textAlign: 'center',
          borderTop: '1px solid',
          borderColor: 'divider',
          bgcolor: 'background.default'
        }}>
          <Box sx={{ p: 3, textAlign: 'center' }}>
            {/* Larger thumbnail with more prominent preview button */}
            <AttachmentThumbnail 
              fileUrl={file.fileUrl}
              fileName={file.fileName}
              fileType={file.contentType}
              size={180}
              showPreviewOnHover={true}
              sx={{ 
                margin: '0 auto', 
                mb: 2,
                // Make the preview button more prominent
                '& .MuiIconButton-root': {
                  width: '40%',
                  height: '40%',
                  visibility: 'visible',
                  opacity: 0.9,
                  bgcolor: 'rgba(0, 0, 0, 0.5)',
                  '&:hover': {
                    bgcolor: 'rgba(0, 0, 0, 0.7)',
                    transform: 'scale(1.05)'
                  }
                },
                '& .MuiSvgIcon-root': {
                  fontSize: '1.8rem',
                  color: 'white'
                }
              }}
            />
            <Typography variant="h6" gutterBottom>
              {file.displayName || file.fileName}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {formatFileSize(file.fileSize)} • {formatDate(file.createdAt)}
            </Typography>
            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={handleDownload}
                disabled={!file.fileUrl}
              >
                Download
              </Button>
            </Box>
          </Box>
        </Box>
      </Collapse>

      <Box sx={{
        display: 'flex',
        justifyContent: 'flex-end',
        p: 1,
        borderTop: '1px solid',
        borderColor: 'divider'
      }}>
        <Tooltip title={file.fileUrl ? "Download file" : "Download not available"}>
          <span>
            <IconButton
              size="small"
              onClick={handleDownload}
              disabled={!file.fileUrl}
              sx={{
                color: 'primary.main'
              }}
            >
              <DownloadIcon fontSize="small" />
            </IconButton>
          </span>
        </Tooltip>
      </Box>
    </Card>
  );
};

const PRAttachmentsWithThumbnails = ({ attachments = [] }) => {
  return (
    <Paper
      variant="outlined"
      sx={{
        p: 2,
        bgcolor: 'background.default',
        borderRadius: 1,
        boxShadow: 'none',
        border: '1px solid',
        borderColor: 'divider'
      }}
    >
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        mb: 2
      }}>
        <AttachFileIcon color="primary" sx={{ mr: 1 }} />
        <Typography variant="h6" color="primary">
          Attachments
        </Typography>
        {attachments?.length > 0 && (
          <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
            ({attachments.length})
          </Typography>
        )}
      </Box>

      <Divider sx={{ mb: 2 }} />

      {!attachments || attachments.length === 0 ? (
        <Box sx={{ py: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            No attachments available.
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={2}>
          {attachments.map((file, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <AttachmentCard file={file} />
            </Grid>
          ))}
        </Grid>
      )}
    </Paper>
  );
};

export default PRAttachmentsWithThumbnails;
