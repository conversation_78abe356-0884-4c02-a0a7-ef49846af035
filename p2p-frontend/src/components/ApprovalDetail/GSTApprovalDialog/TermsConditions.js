import React, { useState, useEffect } from 'react';
import {
  Typography,
  Paper,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import apiService from '../../../services/apiService';

const TermsConditions = ({ 
  paymentTerms, 
  setPaymentTerms, 
  deliveryTerms, 
  setDeliveryTerms,
  billTo,
  setBillTo,
  shipToAddress,
  setShipToAddress,
  expectedDeliveryDate,
  setExpectedDeliveryDate
}) => {
  const [locations, setLocations] = useState([]);
  const [isLoadingLocations, setIsLoadingLocations] = useState(false);
  
  // Fetch available billing addresses for Bill To dropdown
  useEffect(() => {
    const fetchBillingAddresses = async () => {
      try {
        setIsLoadingLocations(true);
        // Use the specific endpoint for billing addresses
        const response = await apiService.get('/admin/billing-addresses');
        if (response && response.data && response.data.data) {
          setLocations(response.data.data);
        }
      } catch (error) {
        console.error('Error fetching billing addresses:', error);
        // Fallback to empty array if API fails
        setLocations([]);
      } finally {
        setIsLoadingLocations(false);
      }
    };
    
    fetchBillingAddresses();
  }, []);
  return (
    <Paper elevation={0} sx={{ p: 2, bgcolor: 'background.default', mb: 2 }}>
      <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
        Terms & Conditions
      </Typography>
      <Grid container spacing={2}>
        {/* First row */}
        <Grid item xs={12} md={4}>
          <TextField
            label="Payment Terms"
            fullWidth
            value={paymentTerms}
            onChange={(e) => setPaymentTerms(e.target.value)}
            size="small"
            placeholder="e.g., 30 days, Immediate"
            sx={{ mb: 2 }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <TextField
            label="Delivery Terms"
            fullWidth
            value={deliveryTerms}
            onChange={(e) => setDeliveryTerms(e.target.value)}
            size="small"
            placeholder="e.g., 1 week, 10 days"
            sx={{ mb: 2 }}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="Expected Delivery Date"
              value={expectedDeliveryDate}
              onChange={(newDate) => setExpectedDeliveryDate(newDate)}
              slotProps={{
                textField: {
                  fullWidth: true,
                  size: "small",
                  sx: { mb: 2 }
                }
              }}
            />
          </LocalizationProvider>
        </Grid>
        
        {/* Second row */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth size="small">
            <InputLabel id="bill-to-label">Billing Address</InputLabel>
            <Select
              labelId="bill-to-label"
              id="bill-to-select"
              value={billTo || ''}
              onChange={(e) => setBillTo(e.target.value)}
              label="Billing Address"
              MenuProps={{
                PaperProps: {
                  style: {
                    maxHeight: 300,
                  },
                },
              }}
            >
              {isLoadingLocations ? (
                <MenuItem disabled>Loading billing addresses...</MenuItem>
              ) : locations.length === 0 ? (
                <MenuItem disabled>No billing addresses available</MenuItem>
              ) : (
                locations.map((location) => (
                  <MenuItem key={location.id} value={location.id}>
                    <div style={{ whiteSpace: 'normal', wordBreak: 'break-word' }}>
                      {location.address}
                    </div>
                  </MenuItem>
                ))
              )}
            </Select>
            <FormHelperText>Select billing address</FormHelperText>
          </FormControl>
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            label="Delivery Address"
            fullWidth
            multiline
            rows={3}
            value={shipToAddress || ''}
            onChange={(e) => setShipToAddress(e.target.value)}
            size="small"
            placeholder="Enter complete delivery address"
            helperText="Specify where items should be delivered"
          />
        </Grid>
      </Grid>
    </Paper>
  );
};

export default TermsConditions;
