import React from 'react';
import {
  Typography,
  Box,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControl,
  Select,
  MenuItem,
  IconButton,
  InputAdornment,
  Tooltip
} from '@mui/material';
import { Info, Add, Delete, TextFields } from '@mui/icons-material';
import PropTypes from 'prop-types';

const ItemsTable = ({
  items,
  GST_RATES,
  handleGstRateChange,
  handleDescriptionChange,
  handlePriceChange,
  handleCategoryChange,
  handleQuantityChange,
  handleUomChange,
  calculateTotal,
  calculateTotalGst,
  handleAddItem,
  handleRemoveItem,
  handleItemNameChange,
  itemCategories = [],
  unitsOfMeasurement = [],
  itemNameEditable = true // Default to true for backward compatibility
}) => {
  return (
    <>
      <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
        Items with GST Details
      </Typography>
      <TableContainer component={Paper} sx={{ mb: 3, overflow: 'visible' }}>
        <Table size="small" sx={{ minWidth: 1000 }}>
          <TableHead>
            <TableRow sx={{ bgcolor: 'primary.light' }}>
              <TableCell>Item Name</TableCell>
              <TableCell>Item Category</TableCell>
              <TableCell>Description</TableCell>
              <TableCell align="right">Quantity</TableCell>
              <TableCell align="right">UOM</TableCell>
              <TableCell align="right">Price/Unit</TableCell>
              <TableCell align="right">
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                  GST Rate
                  <Tooltip title="Select applicable GST rate">
                    <IconButton size="small" sx={{ ml: 0.5 }}>
                      <Info fontSize="inherit" />
                    </IconButton>
                  </Tooltip>
                </Box>
              </TableCell>
              <TableCell align="right">GST Amount</TableCell>
              <TableCell align="right">Total</TableCell>
              <TableCell width="50px" align="center"></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {items.map((item, index) => (
              <TableRow key={item.id || index}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {itemNameEditable ? (
                      <TextField
                        fullWidth
                        size="small"
                        value={item.itemName || ""}
                        onChange={(e) => handleItemNameChange(index, e.target.value)}
                        variant="outlined"
                        placeholder="Enter item name"
                        required
                        error={!item.itemName}
                        helperText={!item.itemName ? "Item name is required" : ""}
                        InputProps={{
                          startAdornment: <InputAdornment position="start"><TextFields fontSize="small" /></InputAdornment>,
                        }}
                      />
                    ) : (
                      <Typography variant="body2" title={item.itemName}>
                        {item.itemName || "N/A"}
                      </Typography>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <FormControl fullWidth size="small">
                    <Select
                      value={item.itemCategoryId || item.itemCategory?.id || ''}
                      onChange={(e) => handleCategoryChange(index, e.target.value)}
                      displayEmpty
                      required
                    >
                      <MenuItem disabled value="">
                        <em>Select Category</em>
                      </MenuItem>
                      {(itemCategories || []).map((cat) => (
                        <MenuItem key={cat.id} value={cat.id}>
                          {cat.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </TableCell>
                <TableCell>
                  <TextField
                    fullWidth
                    size="small"
                    value={item.itemDescription || ""}
                    onChange={(e) => handleDescriptionChange(index, e.target.value)}
                    variant="outlined"
                    placeholder="Description"
                  />
                </TableCell>
                <TableCell align="right">
                  <TextField
                    size="small"
                    type="number"
                    value={item.quantity || ''}
                    onChange={(e) => handleQuantityChange(index, e.target.value)}
                    inputProps={{ min: 1, step: 1 }}
                    sx={{ width: 70 }}
                    required
                    error={!item.quantity || parseFloat(item.quantity) <= 0}
                    helperText={!item.quantity || parseFloat(item.quantity) <= 0 ? "Required" : ""}
                  />
                </TableCell>
                <TableCell align="right">
                  <FormControl fullWidth size="small">
                    <Select
                      value={item.uom || ''}
                      onChange={(e) => handleUomChange(index, e.target.value)}
                      displayEmpty
                      required
                    >
                      <MenuItem disabled value="">
                        <em>Select UOM</em>
                      </MenuItem>
                      {(unitsOfMeasurement || []).map((uom) => (
                        <MenuItem key={uom.value || uom} value={uom.value || uom}>
                          {uom.label || uom}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </TableCell>
                <TableCell align="right">
                  <TextField
                    size="small"
                    type="number"
                    value={item.estimatedPricePerQuantity || ''}
                    onChange={(e) => handlePriceChange(index, e.target.value)}
                    inputProps={{ min: 0, step: 0.01 }}
                    sx={{ width: 120 }}
                    required
                    error={(item.pricePerUnit === undefined || item.pricePerUnit === '') && 
                           (item.price === undefined || item.price === '') &&
                           (item.estimatedPricePerQuantity === undefined || item.estimatedPricePerQuantity === '')}
                    helperText={(item.pricePerUnit === undefined || item.pricePerUnit === '') && 
                               (item.price === undefined || item.price === '') &&
                               (item.estimatedPricePerQuantity === undefined || item.estimatedPricePerQuantity === '') ? "Required" : ""}
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                    }}
                  />
                </TableCell>
                <TableCell align="right">
                  <FormControl fullWidth size="small" sx={{ minWidth: 120 }}>
                    <Select
                      value={item.gstRate || item.gstPercentage || ''}
                      onChange={(e) => {
                        // Find the selected GST rate object
                        const selectedRate = GST_RATES.find(rate => {
                          if (typeof e.target.value === 'string' && e.target.value.includes('_')) {
                            const [rateValue, type] = e.target.value.split('_');
                            return rate.value === parseFloat(rateValue) && rate.type === type;
                          }
                          return rate.value === parseFloat(e.target.value) && !rate.type;
                        });
                        // Pass the complete rate object to the handler
                        handleGstRateChange(index, selectedRate || e.target.value);
                      }}
                      displayEmpty
                      placeholder="Select GST rate"
                      renderValue={(value) => {
                        // Just show the percentage value without GST type
                        if (typeof value === 'object' && value.value) {
                          // If value is an object with a value property
                          return `${value.value}%`;
                        } else if (typeof value === 'string' && value.includes('_')) {
                          // If value is a string like "18_SPLIT", extract just the rate
                          const [rate] = value.split('_');
                          return `${rate}%`;
                        } else if (value) {
                          // For simple numeric values
                          return `${value}%`;
                        } else {
                          // Empty value
                          return '';
                        }
                      }}
                    >
                      <MenuItem disabled value="">
                        <em>Select GST Rate</em>
                      </MenuItem>
                      {GST_RATES.map(option => {
                        // Simplify the display format
                        const label = option.type === 'SPLIT' ?
                          `(${option.value}%) CGST ${option.value/2}% + SGST ${option.value/2}%` :
                          `(${option.value}%) IGST ${option.value}%`;
                        
                        return (
                          <MenuItem key={option.value + (option.type || '')} value={option.value}>
                            {label}
                          </MenuItem>
                        );
                      })}
                    </Select>
                  </FormControl>
                </TableCell>
                <TableCell align="right">₹{item.gstAmount}</TableCell>
                <TableCell align="right">₹{item.totalWithGst || item.totalValue}</TableCell>
                <TableCell align="center">
                  <Tooltip title={item.isNewItem ? "Delete this item" : "Cannot delete original items from PR"}>
                    <span>
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleRemoveItem(index)}
                        disabled={!item.isNewItem} // Only allow deleting new items
                        sx={{ 
                          '&:hover': { backgroundColor: item.isNewItem ? 'rgba(211, 47, 47, 0.04)' : 'transparent' },
                          visibility: items.length > 1 ? 'visible' : 'hidden', // Hide delete button when only one item remains
                          opacity: item.isNewItem ? 1 : 0.3 // Dim the button for original items
                        }}
                      >
                        <Delete fontSize="small" />
                      </IconButton>
                    </span>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}

            {/* Summary Row */}
            <TableRow>
              <TableCell colSpan={6} />
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>Total:</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>₹{calculateTotalGst()}</TableCell>
              <TableCell align="right" sx={{ fontWeight: 'bold' }}>₹{calculateTotal()}</TableCell>
              <TableCell />
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>
      
      {/* Add Item Button */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-start', mt: 2 }}>
        <IconButton 
          color="primary" 
          onClick={handleAddItem}
          sx={{ 
            border: '1px dashed',
            borderColor: 'primary.main',
            borderRadius: 1,
            p: 0.5,
            '&:hover': { backgroundColor: 'rgba(25, 118, 210, 0.04)' } 
          }}
        >
          <Add />
        </IconButton>
        <Typography 
          variant="body2" 
          color="primary" 
          sx={{ ml: 1, alignSelf: 'center', cursor: 'pointer' }} 
          onClick={handleAddItem}
        >
          Add Item
        </Typography>
      </Box>
    </>
  );
};


ItemsTable.propTypes = {
  items: PropTypes.array.isRequired,
  GST_RATES: PropTypes.array.isRequired,
  handleGstRateChange: PropTypes.func.isRequired,
  handleDescriptionChange: PropTypes.func.isRequired,
  handlePriceChange: PropTypes.func.isRequired,
  handleCategoryChange: PropTypes.func.isRequired,
  handleQuantityChange: PropTypes.func.isRequired,
  handleUomChange: PropTypes.func.isRequired,
  calculateTotal: PropTypes.func.isRequired,
  calculateTotalGst: PropTypes.func.isRequired,
  handleAddItem: PropTypes.func.isRequired,
  handleRemoveItem: PropTypes.func.isRequired,
  handleItemNameChange: PropTypes.func,
  itemCategories: PropTypes.array,
  unitsOfMeasurement: PropTypes.array,
  itemNameEditable: PropTypes.bool
};

export default ItemsTable;
