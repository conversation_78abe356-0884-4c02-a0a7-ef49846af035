import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Divider,
  Chip,
  Stack
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Help as HelpIcon,
  CurrencyRupee as CurrencyRupeeIcon
} from '@mui/icons-material';
import AttachmentThumbnail from '../../common/AttachmentThumbnail';

/**
 * A component that displays both proposed and selected quotations side by side
 * to allow users to compare them before finalizing the PO
 */
const QuotationComparison = ({ prDetails }) => {
  // Get the selected quotation
  const selectedQuote = prDetails?.vendorQuotes?.find(q => q.id === prDetails.selectedQuoteId) || null;
  
  // If available, get all other quotations for comparison
  const proposedQuotes = prDetails?.vendorQuotes?.filter(q => q.id !== prDetails.selectedQuoteId) || [];
  
  // If there's no quotation, return null
  if (!selectedQuote) {
    return null;
  }
  
  // Small component to display verification badges
  const VerificationBadge = ({ verified, label, color }) => (
    <Box sx={{ display: 'flex', alignItems: 'center', mr: 1.5 }}>
      {verified ? (
        <CheckCircleIcon fontSize="small" sx={{ color: color || 'success.main', mr: 0.5, fontSize: '0.9rem' }} />
      ) : (
        <HelpIcon fontSize="small" sx={{ color: 'grey.500', mr: 0.5, fontSize: '0.9rem' }} />
      )}
      <Typography variant="caption" color={verified ? 'text.primary' : 'text.secondary'}>
        {label}
      </Typography>
    </Box>
  );
  
  // Format currency
  const formatCurrency = (amount) => {
    if (!amount && amount !== 0) return 'N/A';
    return parseFloat(amount).toLocaleString('en-IN');
  };
  
  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
        Quotation Information
      </Typography>
      <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2}>
          {/* Selected Quotation */}
          <Grid item xs={12} md={6}>
            <Box sx={{ 
              p: 2, 
              borderRadius: 1, 
              border: '2px solid',
              borderColor: 'primary.main',
              backgroundColor: 'primary.lightest',
              height: '100%',
              display: 'flex',
              flexDirection: 'column'
            }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="subtitle1" fontWeight="bold" color="primary.main">
                  Selected Quotation
                </Typography>
                <Chip 
                  size="small"
                  color="primary"
                  label="Selected"
                  icon={<CheckCircleIcon fontSize="small" />}
                />
              </Box>
              <Divider sx={{ mb: 1.5 }} />
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1.5 }}>
                <Box>
                  <Typography variant="body2" fontWeight="bold">
                    {selectedQuote.vendorName}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    <CurrencyRupeeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.9rem' }} />
                    <Typography variant="h6" color="primary.main" fontWeight="bold">
                      {formatCurrency(selectedQuote.amount)}
                    </Typography>
                  </Box>
                </Box>
                
                <AttachmentThumbnail 
                  fileUrl={selectedQuote.fileUrl || ''}
                  fileName={selectedQuote.fileName || 'Quote.pdf'}
                  fileType={selectedQuote.fileType || '.pdf'}
                  size={50}
                />
              </Box>
              
              <Box sx={{ mb: 1.5 }}>
                <Typography variant="body2" fontWeight="bold" gutterBottom>
                  Terms
                </Typography>
                <Grid container spacing={1}>
                  <Grid item xs={6}>
                    <Typography variant="caption" color="text.secondary">Payment:</Typography>
                    <Typography variant="body2">{selectedQuote.paymentTerms || 'N/A'}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="caption" color="text.secondary">Delivery:</Typography>
                    <Typography variant="body2">{selectedQuote.deliveryTerms || 'N/A'}</Typography>
                  </Grid>
                </Grid>
              </Box>
              
              <Box sx={{ mt: 'auto' }}>
                <Typography variant="body2" fontWeight="bold" gutterBottom>
                  Verifications
                </Typography>
                <Stack direction="row" flexWrap="wrap" gap={0.5}>
                  <VerificationBadge 
                    verified={selectedQuote.gstVerified || false} 
                    label="GST" 
                    color="success.main" 
                  />
                  <VerificationBadge 
                    verified={selectedQuote.panVerified || false} 
                    label="PAN" 
                    color="#ff9800" 
                  />
                  <VerificationBadge 
                    verified={selectedQuote.bankVerified || false} 
                    label="Bank" 
                    color="#2196f3" 
                  />
                  <VerificationBadge 
                    verified={selectedQuote.msmeVerified || false} 
                    label="MSME" 
                    color="#9c27b0" 
                  />
                </Stack>
              </Box>
            </Box>
          </Grid>
          
          {/* Proposed Quotation(s) */}
          <Grid item xs={12} md={6}>
            <Box sx={{ 
              height: '100%', 
              display: 'flex', 
              flexDirection: 'column',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'divider',
              p: 2
            }}>
              <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 1 }}>
                {proposedQuotes.length === 0 ? 'No Other Quotations' : 'Other Proposed Quotations'}
              </Typography>
              <Divider sx={{ mb: 1.5 }} />
              
              {proposedQuotes.length === 0 ? (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                  <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                    No other quotations available for comparison
                  </Typography>
                </Box>
              ) : (
                <Box sx={{ overflowY: 'auto', flexGrow: 1 }}>
                  {proposedQuotes.map((quote, index) => (
                    <Box key={quote.id || index} sx={{
                      p: 1.5,
                      mb: index < proposedQuotes.length - 1 ? 1.5 : 0,
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: 'divider',
                      backgroundColor: 'background.paper'
                    }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {quote.vendorName}
                          </Typography>
                          
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <CurrencyRupeeIcon fontSize="small" sx={{ mr: 0.5, fontSize: '0.9rem' }} />
                            <Typography variant="body1" fontWeight="medium">
                              {formatCurrency(quote.amount)}
                            </Typography>
                          </Box>
                        </Box>
                        
                        <AttachmentThumbnail 
                          fileUrl={quote.fileUrl || ''}
                          fileName={quote.fileName || 'Quote.pdf'}
                          fileType={quote.fileType || '.pdf'}
                          size={40}
                        />
                      </Box>
                      
                      <Box sx={{ display: 'flex', gap: 2, mb: 1 }}>
                        <Box>
                          <Typography variant="caption" color="text.secondary">Payment:</Typography>
                          <Typography variant="body2" fontSize="0.8rem">{quote.paymentTerms || 'N/A'}</Typography>
                        </Box>
                        <Box>
                          <Typography variant="caption" color="text.secondary">Delivery:</Typography>
                          <Typography variant="body2" fontSize="0.8rem">{quote.deliveryTerms || 'N/A'}</Typography>
                        </Box>
                      </Box>
                      
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        <VerificationBadge 
                          verified={quote.gstVerified || false} 
                          label="GST" 
                          color="success.main" 
                        />
                        <VerificationBadge 
                          verified={quote.panVerified || false} 
                          label="PAN" 
                          color="#ff9800" 
                        />
                        <VerificationBadge 
                          verified={quote.bankVerified || false} 
                          label="Bank" 
                          color="#2196f3" 
                        />
                        <VerificationBadge 
                          verified={quote.msmeVerified || false} 
                          label="MSME" 
                          color="#9c27b0" 
                        />
                      </Box>
                    </Box>
                  ))}
                </Box>
              )}
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default QuotationComparison;
