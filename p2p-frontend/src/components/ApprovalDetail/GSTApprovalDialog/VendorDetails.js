import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Chip,
  CircularProgress,
  Tooltip,
  Divider
} from '@mui/material';
import VerifiedIcon from '@mui/icons-material/Verified';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import apiService from '../../../services/apiService';

/**
 * Vendor Details component that shows verification status and remarks
 * Fetches detailed partner information from the new API endpoint
 */
const VendorDetails = ({ vendorId, vendorName, quotationId }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [partnerDetails, setPartnerDetails] = useState(null);

  const formatLocation = (loc) => {
    const parts = [loc.village, loc.mandal, loc.district, loc.state].filter(Boolean);
    return parts.join(', ');
  };
  
  
  useEffect(() => {
    const fetchPartnerDetails = async () => {
      // Only attempt to fetch if we have a valid vendorId
      if (!vendorId) {
        console.log('No vendor ID provided, skipping partner details fetch');
        setLoading(false);
        return;
      }
      
      try {
        setLoading(true);
        setError(null);
        
        console.log('Fetching partner details for vendor ID:', vendorId);
        
        // Safely call the API with proper error handling
        try {
          // Use the updated API to fetch detailed vendor information
          const response = await apiService.getPartnerDetails({ id: vendorId });
          console.log('Partner details response:', response);
          // Check if the response contains partner data
          if (response && response.id ) {
            // The API might return either a single partner object or an array of partners
            // Handle both cases by extracting the relevant partner object
            const partnerData = response
            ? {
                id: response.id,
                name: response.name,
                phone: response.phone_number,
                whatsapp_number: response.whatsapp_number || '',
                email: response.email || 'N/A',
                gst: response.gst || null,
                bankVerified: response.bank_detail?.status === 'Verified',
                pan:
                  response.kyc_docs?.find((doc) => doc.doc_category === 'Pan Card')
                    ?.identification_number || 'N/A',
                msmeVerified:
                  response.kyc_docs?.find((doc) => doc.doc_category === 'MSME Document')
                    ?.is_api_verified || false,
                location:
                  typeof response.location === 'object'
                    ? response.location.full_address || formatLocation(response.location)
                    : response.location || 'N/A',
                remarks: response.remarks || '',
                kyc_docs: response.kyc_docs || [],
              }
            : (Array.isArray(response.items) && response.items.length > 0
                ? response.items[0]
                : null);

            
            if (partnerData) {
              setPartnerDetails(partnerData);
              console.log('Partner details fetched successfully:', partnerData);
            } else {
              console.warn('No partner data found in response');
              // Don't throw error, just set null to show generic info
              setPartnerDetails(null);
            }
          } else {
            console.warn('Invalid response format from API');
            // Don't throw error, just set null to show generic info
            setPartnerDetails(null);
          }
        } catch (apiError) {
          console.error('API call error:', apiError);
          // Handle API errors gracefully
          setPartnerDetails(null);
          // Only set error for actual API response errors, not undefined/null responses
          if (apiError.response && apiError.response.status !== 404) {
            setError('Could not retrieve vendor details');
          }
        }
      } finally {
        setLoading(false);
      }
    };
    
    fetchPartnerDetails();
  }, [vendorId]);
  
  const renderVerificationStatus = (status, label) => {
    return (
      <Tooltip title={status ? `${label} Verified` : `${label} Not Verified`}>
        <Chip 
          icon={status ? <VerifiedIcon /> : <ErrorOutlineIcon />} 
          label={label}
          color={status ? "success" : "default"}
          size="small"
          variant={status ? "filled" : "outlined"}
          sx={{ m: 0.5 }}
        />
      </Tooltip>
    );
  };
  
  if (loading) {
    return (
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress size={24} />
        <Typography variant="body2" sx={{ ml: 1 }}>
          Loading vendor details...
        </Typography>
      </Box>
    );
  }
  
  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="body2" color="error">
          {error}
        </Typography>
      </Box>
    );
  }
  
  if (!partnerDetails && !loading) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="body2">
          <strong>Vendor:</strong> {vendorName || 'Unknown'} (ID: {vendorId || 'N/A'})
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          <InfoOutlinedIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
          Detailed vendor information not available

        </Typography>
      </Box>
    );
  }
  
  return (
    <Paper variant="outlined" sx={{ p: 2, mb: 2 }}>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
            Vendor Information
          </Typography>
          <Divider sx={{ my: 1 }} />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <Typography variant="body2">
            <strong>Name:</strong> {partnerDetails?.name || partnerDetails?.partnerName || vendorName || 'Unknown'}
          </Typography>
          <Typography variant="body2">
            <strong>Contact:</strong> {partnerDetails?.phone || partnerDetails?.contact || 'N/A'}
          </Typography>
          <Typography variant="body2">
            <strong>Email:</strong> {partnerDetails?.email || 'N/A'}
          </Typography>
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <Typography variant="body2">
            <strong>GST:</strong> {partnerDetails?.gst || partnerDetails?.gstNumber || 'N/A'}
          </Typography>
          <Typography variant="body2">
            <strong>PAN:</strong> {partnerDetails?.pan || partnerDetails?.panNumber || 'N/A'}
          </Typography>
          <Typography variant="body2">
            <strong>Address:</strong> {partnerDetails?.location || partnerDetails?.address || 'N/A'}
          </Typography>
        </Grid>
        
        {(partnerDetails?.remarks || partnerDetails?.comment) && (
          <Grid item xs={12}>
            <Typography variant="body2">
              <strong>Remarks:</strong> {partnerDetails.remarks || partnerDetails.comment}
            </Typography>
          </Grid>
        )}
        
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', mt: 1 }}>
            {renderVerificationStatus(partnerDetails?.gstVerified || partnerDetails?.is_gst_verified, 'GST')}
            {renderVerificationStatus(partnerDetails?.panVerified || partnerDetails?.is_pan_verified, 'PAN')}
            {renderVerificationStatus(partnerDetails?.bankVerified || partnerDetails?.is_bank_verified, 'Bank')}
            {renderVerificationStatus(partnerDetails?.msmeVerified || partnerDetails?.is_msme_verified, 'MSME')}
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default VendorDetails;
