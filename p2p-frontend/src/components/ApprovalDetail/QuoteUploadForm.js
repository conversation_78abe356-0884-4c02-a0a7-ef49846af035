// import React from 'react';
// import {
//   Box,
//   Typography,
//   Paper,
//   TextField,
//   Button,
//   IconButton,
//   MenuItem,
//   Alert,
//   CircularProgress
// } from '@mui/material';
// import {
//   AttachFile as AttachFileIcon,
//   Add as AddIcon,
//   Cancel as CancelIcon
// } from '@mui/icons-material';

// const QuoteUploadForm = ({
//   vendors,
//   quoteState,
//   onVendorChange,
//   onCustomVendorNameChange,
//   onPaymentTermsChange,
//   onFileUpload,
//   onFileRemove,
//   onSubmit,
//   disabled
// }) => {
//   return (
//     <Box sx={{ width: '100%' }}>
//       {quoteState.error && (
//         <Alert severity="error" sx={{ mb: 2 }}>
//           {quoteState.error}
//         </Alert>
//       )}
      
//       {quoteState.success && (
//         <Alert severity="success" sx={{ mb: 2 }}>
//           Quote successfully uploaded!
//         </Alert>
//       )}
      
//       <Box sx={{ mb: 3 }}>
//         <Typography variant="h6" gutterBottom sx={{ 
//           fontWeight: 'bold', 
//           color: disabled ? 'text.disabled' : 'primary.main', 
//           display: 'flex', 
//           alignItems: 'center' 
//         }}>
//           <Box component="span" sx={{ display: 'inline-flex', mr: 1 }}>
//             <AddIcon fontSize="small" />
//           </Box>
//           Add New Vendor Quote
//         </Typography>
//         <TextField
//           select
//           fullWidth
//           label="Vendor Name"
//           variant="outlined"
//           size="small"
//           value={quoteState.vendorId}
//           onChange={(e) => onVendorChange(e.target.value)}
//           sx={{ mb: 2 }}
//           required
//           disabled={disabled}
//         >
//           {vendors.map(vendor => (
//             <MenuItem key={vendor.id} value={vendor.id}>
//               {vendor.name}
//             </MenuItem>
//           ))}
//         </TextField>
        
//         {quoteState.isCustomVendor && (
//           <TextField
//             fullWidth
//             label="Custom Vendor Name"
//             variant="outlined"
//             size="small"
//             value={quoteState.customVendorName}
//             onChange={(e) => onCustomVendorNameChange(e.target.value)}
//             sx={{ mb: 2 }}
//             required
//             disabled={disabled}
//             placeholder="Enter vendor name"
//           />
//         )}
//         <TextField
//           fullWidth
//           label="Payment Terms"
//           variant="outlined"
//           size="small"
//           value={quoteState.paymentTerms}
//           onChange={(e) => onPaymentTermsChange(e.target.value)}
//           placeholder="e.g., 30 days, Immediate, etc."
//           disabled={disabled}
//         />
//       </Box>
      
//       <Typography variant="subtitle2" gutterBottom color={disabled ? 'text.disabled' : 'inherit'}>
//         Upload Vendor Quotation
//       </Typography>
//       <Box sx={{ 
//         border: '1px dashed',
//         borderColor: disabled ? 'grey.300' : 'grey.400',
//         borderRadius: 1,
//         p: 3,
//         textAlign: 'center',
//         bgcolor: disabled ? 'grey.100' : 'grey.50',
//         mb: 2
//       }}>
//         <input
//           accept="application/pdf,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,image/*"
//           style={{ display: 'none' }}
//           id="quote-file-upload"
//           type="file"
//           onChange={onFileUpload}
//           disabled={disabled}
//         />
//         <label htmlFor="quote-file-upload">
//           <Button
//             variant="outlined"
//             component="span"
//             startIcon={<AttachFileIcon />}
//             disabled={disabled}
//           >
//             Select File
//           </Button>
//         </label>
//         <Typography variant="caption" display="block" sx={{ mt: 1, color: disabled ? 'text.disabled' : 'inherit' }}>
//           Supported formats: PDF, Excel, Images (Max: 5MB)
//         </Typography>
//       </Box>
//       {quoteState.file && (
//         <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', bgcolor: 'grey.100', p: 1, borderRadius: 1, mb: 2 }}>
//           <Box sx={{ display: 'flex', alignItems: 'center' }}>
//             <AttachFileIcon fontSize="small" sx={{ mr: 1 }} />
//             <Typography variant="body2" noWrap sx={{ maxWidth: '150px' }}>
//               {quoteState.file.name}
//             </Typography>
//           </Box>
//           <IconButton size="small" onClick={onFileRemove} disabled={disabled}>
//             <CancelIcon fontSize="small" />
//           </IconButton>
//         </Box>
//       )}
      
//       <Button 
//         variant="contained" 
//         fullWidth
//         color="primary"
//         size="large"
//         onClick={onSubmit}
//         disabled={disabled || quoteState.submitting || !quoteState.file || 
//           (quoteState.isCustomVendor ? !quoteState.customVendorName.trim() : !quoteState.vendorName.trim())}
//         sx={{ mt: 2, py: 1, opacity: disabled ? 0.5 : 1 }}
//       >
//         {quoteState.submitting ? <CircularProgress size={24} sx={{ mr: 1 }} /> : <AddIcon sx={{ mr: 1 }} />}
//         Submit Vendor Quote
//       </Button>
//     </Box>
//   );
// };

// export default QuoteUploadForm;
