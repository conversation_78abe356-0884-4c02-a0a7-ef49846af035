// import {
//   Box,
//   Typography,
//   Paper,
//   Grid,
//   Chip,
//   Button,
//   CircularProgress
// } from '@mui/material';
// import {
//   AttachFile as AttachFileIcon
// } from '@mui/icons-material';

// const VendorQuotesList = ({ 
//   quotations, 
//   selectedQuoteId, 
//   onSelectQuote, 
//   onSendProposal, 
//   sending, 
//   proposalSent,
//   getVendorNameById = () => 'Unknown Vendor'
// }) => {
//   // Ensure quotations is always an array (even if undefined or null)
//   const quotes = Array.isArray(quotations) ? quotations : [];
  
//   return quotes.length > 0 ? (
//     <Box sx={{ mt: 2, maxHeight: 500, overflow: 'auto' }}>
//       {quotes.map((quote, index) => (
//         <Paper 
//           key={quote.id || index} 
//           sx={{ 
//             p: 2, 
//             mb: 2, 
//             bgcolor: proposalSent && selectedQuoteId === quote.id ? 'success.light' : 'background.paper',
//             borderRadius: 1, 
//             boxShadow: 1,
//             position: 'relative'
//           }}
//         >
//           {!proposalSent && (
//             <Button
//               checked={selectedQuoteId === quote.id}
//               onChange={() => onSelectQuote(quote.id)}
//               value={quote.id}
//               name="quote-radio-buttons"
//               sx={{ position: 'absolute', right: 8, top: 8 }}
//               disabled={proposalSent}
//             />
//           )}
// //             mb: 2, 
// //             bgcolor: proposalSent && selectedQuoteId === quote.id ? 'success.light' : 'background.paper',
// //             borderRadius: 1, 
// //             boxShadow: 1,
// //             position: 'relative'
// //           }}
// //         >
// //           {!proposalSent && (
// //             <Radio
// //               checked={selectedQuoteId === quote.id}
// //               onChange={() => onSelectQuote(quote.id)}
// //               value={quote.id}
// //               name="quote-radio-buttons"
// //               sx={{ position: 'absolute', right: 8, top: 8 }}
// //               disabled={proposalSent}
// //             />
// //           )}
          
// //           {proposalSent && selectedQuoteId === quote.id && (
// //             <Chip 
// //               label="Proposed" 
// //               color="success" 
// //               size="small" 
// //               sx={{ position: 'absolute', right: 8, top: 8 }}
// //             />
// //           )}
          
// //           <Grid container spacing={1}>
// //             <Grid item xs={12} sx={{ borderBottom: '1px solid', borderColor: 'divider', pb: 1, mb: 1 }}>
// //               <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
// //                 {quote.vendorName}
// //               </Typography>
// //             </Grid>
// //             <Grid item xs={6}>
// //               <Typography variant="body2" color="text.secondary">
// //                 Payment: <strong>{quote.paymentTerms}</strong>
// //               </Typography>
// //             </Grid>
// //             <Grid item xs={12} sx={{ mt: 1 }}>
// //               <Box sx={{ display: 'flex', alignItems: 'center' }}>
// //                 <AttachFileIcon fontSize="small" sx={{ mr: 1, color: 'primary.main' }} />
// //                 <Typography variant="body2" color="primary.main">
// //                   {quote.fileName}
// //                 </Typography>
// //               </Box>
// //             </Grid>
// //             <Grid item xs={12} sx={{ mt: 1 }}>
// //               <Typography variant="caption" color="text.secondary">
// //                 Uploaded on {new Date(quote.uploadedOn).toLocaleString()}
// //               </Typography>
// //             </Grid>
// //           </Grid>
// //         </Paper>
// //       ))}
      
// //       {!proposalSent && (
// //         <Button
// //           variant="contained"
// //           color="primary"
// //           fullWidth
// //           onClick={onSendProposal}
// //           disabled={!selectedQuoteId || sending}
// //           sx={{ mt: 2, mb: 2 }}
// //         >
// //           {sending ? (
// //             <CircularProgress size={24} sx={{ mr: 1, color: 'white' }} />
// //           ) : (
// //             <CheckCircleIcon sx={{ mr: 1 }} />
// //           )}
// //           Send Proposal
// //         </Button>
// //       )}
// //     </Box>
// //   ) : (
// //     <Paper sx={{ p: 3, mb: 3, textAlign: 'center', borderStyle: 'dashed', borderWidth: 1, borderColor: 'divider' }}>
// //       <Typography variant="body1" color="text.secondary" sx={{ fontStyle: 'italic' }}>
// //         No quotes uploaded yet
// //       </Typography>
// //     </Paper>
// //   );
// // };

// // export default VendorQuotesList;
