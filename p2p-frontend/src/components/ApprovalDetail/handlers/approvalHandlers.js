/**
 * Handlers for PR approval actions
 */

/**
 * Get the appropriate approval method based on selected approval type
 * @param {string} approvalType - Selected approval type
 * @returns {Object} Method name and success message
 */
export const getApprovalMethodAndMessage = (approvalType) => {
  switch (approvalType) {
    case 'cc-head':
      return {
        methodName: 'ccHeadApprovePurchaseRequest',
        message: 'CC Head approval completed successfully'
      };
    case 'biz-fin':
      return {
        methodName: 'bizFinApprovePurchaseRequest',
        message: 'Business Finance approval completed successfully'
      };
    case 'procurement':
      return {
        methodName: 'procurementManagerApprovePurchaseRequest',
        message: 'Procurement Manager approval completed successfully'
      };
    default:
      return {
        methodName: 'approvePurchaseRequest',
        message: 'Purchase request approved successfully'
      };
  }
};

/**
 * Suggest an approval type based on PR status
 * @param {string} status - Current PR status
 * @returns {string} Suggested approval type
 */
export const suggestApprovalType = (status) => {
  if (status?.includes('CC_HEAD')) return 'cc-head';
  if (status?.includes('BIZ_FIN') || status?.includes('BUSINESS_HEAD')) return 'biz-fin';
  if (status?.includes('PROCUREMENT')) return 'procurement';
  return 'standard';
};

/**
 * Update PR details with new approval status
 * @param {Object} prDetails - Current PR details
 * @param {string} status - New status (Approved/Rejected)
 * @param {string} remarks - Approval/rejection remarks
 * @param {Date} date - Action date
 * @returns {Object} Updated PR details
 */
export const updatePRDetailsWithApprovalStatus = (prDetails, status, remarks, date = new Date()) => {
  if (!prDetails) return null;
  
  const updatedHistory = prDetails.approvalHistory?.map((item, index) => {
    if (index === 0) { // Assuming the first pending approval is the current one
      return {
        ...item,
        status: status,
        date: date.toISOString(),
        remarks: remarks
      };
    }
    return item;
  }) || [];
  
  return {
    ...prDetails,
    approvalHistory: updatedHistory
  };
};

/**
 * Check if PR is fully approved
 * @param {Object} prDetails - PR details
 * @returns {boolean} True if PR is fully approved
 */
export const isFullyApproved = (prDetails) => {
  return prDetails?.status === 'APPROVED';
};
