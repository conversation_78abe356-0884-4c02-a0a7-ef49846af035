/**
 * Handlers for GST approval dialog
 */

/**
 * <PERSON>le finalized quantity change
 * @param {Array} poItems - Current PO items
 * @param {number} index - Index of the item to update
 * @param {string|number} value - New quantity value
 * @returns {Array} Updated PO items
 */
export const handleFinalizedQuantityChange = (poItems, index, value) => {
  const updatedItems = [...poItems];
  const item = updatedItems[index];
  const finalizedQuantity = parseFloat(value) || 0;
  
  // Update finalized quantity
  item.finalizedQuantity = finalizedQuantity;
  
  // Recalculate GST amount
  item.gstAmount = (item.finalizedPricePerUnit * finalizedQuantity * (item.gstPercentage / 100));
  
  // Update total amount
  item.totalAmount = (item.finalizedPricePerUnit * finalizedQuantity) + item.gstAmount;
  
  return updatedItems;
};

/**
 * Handle finalized price change
 * @param {Array} poItems - Current PO items
 * @param {number} index - Index of the item to update
 * @param {string|number} value - New price value
 * @returns {Array} Updated PO items
 */
export const handleFinalizedPriceChange = (poItems, index, value) => {
  const updatedItems = [...poItems];
  const item = updatedItems[index];
  const finalizedPrice = parseFloat(value) || 0;
  
  // Update finalized price
  item.finalizedPricePerUnit = finalizedPrice;
  
  // Recalculate GST amount
  item.gstAmount = (finalizedPrice * item.finalizedQuantity * (item.gstPercentage / 100));
  
  // Update total amount
  item.totalAmount = (finalizedPrice * item.finalizedQuantity) + item.gstAmount;
  
  return updatedItems;
};

/**
 * Handle GST percentage change
 * @param {Array} poItems - Current PO items
 * @param {number} index - Index of the item to update
 * @param {string|number} value - New GST percentage value
 * @returns {Array} Updated PO items
 */
export const handleGstPercentageChange = (poItems, index, value) => {
  const updatedItems = [...poItems];
  const item = updatedItems[index];
  const gstPercentage = parseFloat(value) || 0;
  
  // Update GST percentage
  item.gstPercentage = gstPercentage;
  
  // Recalculate GST amount
  item.gstAmount = (item.finalizedPricePerUnit * item.finalizedQuantity * (gstPercentage / 100));
  
  // Update total amount
  item.totalAmount = (item.finalizedPricePerUnit * item.finalizedQuantity) + item.gstAmount;
  
  return updatedItems;
};

/**
 * Prepare PO items from PR items
 * @param {Array} prItems - PR items
 * @returns {Array} Prepared PO items
 */
export const preparePOItems = (prItems) => {
  return prItems.map(item => ({
    prItemId: item.id,
    itemName: item.itemDescription,
    itemType: item.itemType,
    description: item.itemDescription,
    quantity: item.quantity,
    finalizedQuantity: item.quantity,
    uom: item.uom,
    pricePerUnit: item.estimatedPricePerQuantity || 0,
    finalizedPricePerUnit: item.estimatedPricePerQuantity || 0,
    gstPercentage: 0, // No default GST percentage
    gstAmount: 0, // No default GST amount
    totalAmount: (item.estimatedPricePerQuantity * item.quantity) // Total without GST
  }));
};

/**
 * Create payload for quotation approval
 * @param {Object} state - Approval state
 * @returns {Object} Payload for API
 */
export const createApprovalPayload = (state) => {
  return {
    reason: state.approvalReason,
    paymentTerms: state.paymentTerms,
    deliveryTerms: state.deliveryTerms,
    poItems: state.poItems.map(item => ({
      prItemId: item.prItemId,
      finalizedQuantity: item.finalizedQuantity,
      finalizedPricePerUnit: item.finalizedPricePerUnit,
      gstPercentage: item.gstPercentage
    }))
  };
};
