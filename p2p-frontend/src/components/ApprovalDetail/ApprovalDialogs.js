import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  TextField,
  Button,
  MenuItem,
  CircularProgress
} from '@mui/material';

export const ApproveDialog = ({
  open,
  onClose,
  onApprove,
  remarks,
  onRemarksChange,
  approvalType,
  onApprovalTypeChange,
  loading
}) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Approve Purchase Request</DialogTitle>
      <DialogContent>
        <DialogContentText>
          Please select the appropriate approval type and provide remarks.
        </DialogContentText>
        
        {/* Approval Type Selector */}
        <TextField
          select
          margin="dense"
          id="approvalType"
          label="Approval Type"
          fullWidth
          variant="outlined"
          value={approvalType}
          onChange={(e) => onApprovalTypeChange(e.target.value)}
          sx={{ mb: 2 }}
        >
          <MenuItem value="standard">Standard Approval</MenuItem>
          <MenuItem value="cc-head">Cost Center Head Approval</MenuItem>
          <MenuItem value="biz-fin">Business Finance Approval</MenuItem>
          <MenuItem value="procurement">Procurement Manager Approval</MenuItem>
        </TextField>
        
        <TextField
          autoFocus
          margin="dense"
          id="remarks"
          label="Remarks (Optional)"
          type="text"
          fullWidth
          multiline
          rows={3}
          variant="outlined"
          value={remarks}
          onChange={(e) => onRemarksChange(e.target.value)}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>Cancel</Button>
        <Button 
          onClick={onApprove} 
          color="success" 
          variant="contained"
          disabled={loading}
        >
          {loading ? <CircularProgress size={24} /> : 'Approve'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export const RejectDialog = ({
  open,
  onClose,
  onReject,
  remarks,
  onRemarksChange,
  loading
}) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Reject Purchase Request</DialogTitle>
      <DialogContent>
        <DialogContentText>
          Please provide a reason for rejecting this purchase request.
        </DialogContentText>
        <TextField
          autoFocus
          margin="dense"
          id="remarks"
          label="Rejection Reason"
          type="text"
          fullWidth
          multiline
          rows={3}
          variant="outlined"
          value={remarks}
          onChange={(e) => onRemarksChange(e.target.value)}
          required
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>Cancel</Button>
        <Button 
          onClick={onReject} 
          color="error" 
          variant="contained"
          disabled={loading || !remarks.trim()}
        >
          {loading ? <CircularProgress size={24} /> : 'Reject'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
