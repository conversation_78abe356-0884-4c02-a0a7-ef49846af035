import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { CircularProgress, Box } from '@mui/material';
import { isUserLoggedIn } from '../utils/localStorage';

/**
 * ProtectedRoute component
 * Redirects to login page if user is not authenticated
 */
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading, user } = useAuth();
  const location = useLocation();
  
  // Double-check authentication status from localStorage
  // This helps prevent issues with token validation during navigation
  const isLoggedInFromStorage = isUserLoggedIn();
  
  useEffect(() => {
    // Log authentication state for debugging
    console.log('Auth state:', { 
      isAuthenticated, 
      loading, 
      isLoggedInFromStorage,
      currentPath: location.pathname
    });
  }, [isAuthenticated, loading, isLoggedInFromStorage, location]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
        <CircularProgress color="primary" />
      </Box>
    );
  }

  // Enforce authentication: redirect to login if not authenticated
  if (!isAuthenticated || !isLoggedInFromStorage) {
    // Save the attempted URL for redirecting back after login
    const returnTo = location.pathname + location.search;
    return <Navigate to={`/login?returnTo=${encodeURIComponent(returnTo)}`} replace />;
  }

  // Render children if authenticated
  return children;
};

export default ProtectedRoute;
