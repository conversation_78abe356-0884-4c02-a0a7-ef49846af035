import { createTheme } from '@mui/material/styles';

const muiTheme = createTheme({
  palette: {
    primary: {
      light: '#81C784', // lighter green
      main: '#4CAF50',  // Green - Primary Color for buttons, highlights
      dark: '#388E3C',  // darker green
      contrastText: '#fff',
    },
    secondary: {
      light: '#F1F8E9', // very light green
      main: '#E8F5E9',  // Light Green - Secondary Color for backgrounds
      dark: '#C8E6C9',  // slightly darker light green
      contrastText: '#000',
    },
    background: {
      default: '#ffffff', // white background
      paper: '#FAFAFA',   // Almost white for sidebar and paper elements
    },
    text: {
      primary: '#37474F',   // Dark Grey for sidebar text & icons
      secondary: '#546E7A', // medium gray for secondary text
    },
    error: {
      main: '#F44336', // Red for error states
    },
    warning: {
      main: '#FF9800', // Orange for mild alerts
    },
    info: {
      main: '#2196F3', // blue
    },
    success: {
      main: '#2E7D32', // Dark Green for confirmed actions
    },
    action: {
      active: '#4CAF50',
      hover: 'rgba(76, 175, 80, 0.08)',
      selected: 'rgba(76, 175, 80, 0.16)',
      disabled: 'rgba(0, 0, 0, 0.26)',
      disabledBackground: 'rgba(0, 0, 0, 0.12)',
    },
    grey: {
      50: '#FAFAFA',
      100: '#F5F5F5',
      200: '#EEEEEE',
      300: '#E0E0E0',
      400: '#BDBDBD',
      500: '#9E9E9E',
      600: '#757575',
      700: '#616161',
      800: '#424242',
      900: '#212121',
      A100: '#D5D5D5',
      A200: '#AAAAAA',
      A400: '#616161',
      A700: '#303030',
    },
  },
  typography: {
    fontFamily: [
      'Inter',
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ].join(','),
    h1: {
      fontWeight: 600,
      fontSize: '2.5rem',
    },
    h2: {
      fontWeight: 600,
      fontSize: '2rem',
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.75rem',
    },
    h4: {
      fontWeight: 600,
      fontSize: '1.5rem',
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.25rem',
    },
    h6: {
      fontWeight: 600,
      fontSize: '1rem',
    },
    button: {
      textTransform: 'none', // Avoid all-caps buttons
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 8, // Slightly rounded corners
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          boxShadow: 'none', // Remove default shadow
          textTransform: 'none', // No uppercase text
          borderRadius: 4,
          '&:hover': {
            boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)', // Add subtle shadow on hover
          },
        },
        containedPrimary: {
          backgroundColor: '#4CAF50', // Green for primary buttons
          color: '#FFFFFF',
          '&:hover': {
            backgroundColor: '#388E3C', // Slightly darker on hover
          },
        },
        containedSecondary: {
          backgroundColor: '#CFD8DC', // Light Grey for secondary/cancel buttons
          color: '#37474F',
          '&:hover': {
            backgroundColor: '#B0BEC5', // Slightly darker on hover
          },
        },
        outlined: {
          borderColor: '#CFD8DC',
          '&:hover': {
            backgroundColor: 'rgba(207, 216, 220, 0.1)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.05)', // Very subtle shadow for cards
          borderRadius: 8, // Slightly rounded cards
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0px 1px 2px rgba(0, 0, 0, 0.03)', // Very subtle shadow for app bar
          backgroundImage: 'none', // Remove default gradient
          backgroundColor: '#FFFFFF', // White top bar
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#FAFAFA', // Almost white sidebar background
          borderRight: '1px solid rgba(0, 0, 0, 0.05)',
        },
      },
    },
    MuiListItemIcon: {
      styleOverrides: {
        root: {
          color: '#37474F', // Dark grey for sidebar icons
          minWidth: 40,
        },
      },
    },
    MuiListItemText: {
      styleOverrides: {
        primary: {
          color: '#37474F', // Dark grey for sidebar text
          fontSize: '0.875rem',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 4,
        },
        colorSuccess: {
          backgroundColor: 'rgba(46, 125, 50, 0.1)',
          color: '#2E7D32',
        },
        colorError: {
          backgroundColor: 'rgba(244, 67, 54, 0.1)',
          color: '#F44336',
        },
        colorWarning: {
          backgroundColor: 'rgba(255, 152, 0, 0.1)',
          color: '#FF9800',
        },
      },
    },
  },
});

export default muiTheme;
